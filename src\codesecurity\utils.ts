import * as vscode from 'vscode';
import { FileNode } from './type';
import * as path from 'path';
import * as fs from 'fs';
import languages from '../common/languages';
import { EXCLUSION_RULES } from '../common/exclusionRules';
import * as minimatch from 'minimatch';

export default class Utils {
  public static async traverseFolder(directoryPath: string, depth = 0, rootDir: string = directoryPath): Promise<FileNode[] | null> {
    try {
      const files = await fs.promises.readdir(directoryPath, { withFileTypes: true });
      const fileNodes: FileNode[] = [];
      if (files.length === 0) {
        return null;
      }
      for (const file of files) {
        const fullPath = path.join(directoryPath, file.name);
        const stats = fs.statSync(fullPath);

        // 计算相对路径用于模式匹配（统一使用'/'做分隔符）
        const relativePath = path.relative(rootDir, fullPath);
        const normalizedPath = relativePath.split(path.sep).join('/');
        // 目录排除
        if (file.isDirectory()) {
          if (EXCLUSION_RULES.DIRECTORIES.has(file.name)) {
            continue;
          }
        }
        // 文件排除
        else if (
          EXCLUSION_RULES.FILE_PATTERNS.some(pattern =>
            minimatch(normalizedPath, pattern, { dot: true })
          )
        ) {
          continue;
        }

        const fileNode: FileNode = { 
          name: file.name,
          relativePath: relativePath 
        };

        if (stats.isFile()) {
          fileNode.size = stats.size;
          fileNode.path = fullPath;
        } else if (file.isDirectory()) {
          fileNode.path = fullPath;
          const children = await this.traverseFolder(fullPath, depth + 1, rootDir);
          if (children) {
            fileNode.children = children;
          } else {
            fileNode.children = [];
          }
        }
        fileNodes.push(fileNode);
      };
      return fileNodes;
    } catch (error) {
      return null;
    }
  }

  public static findLanguageByExtension(extension: string): string {
    for (const [language, ext] of Object.entries(languages)) {
      if (ext === extension) {
        return language;
      }
    }
    return '';
  }

  public static extractFileExtension(filePath: string): string {
    const fileName = path.basename(filePath);
    const extension = path.extname(fileName);
    return extension;
  }
}
