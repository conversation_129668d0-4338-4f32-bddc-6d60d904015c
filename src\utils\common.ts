import { RuntimeEnv } from "../common/constants";
import { getCodeAIConfig } from '../common/globalContext';
import { DEFAULT_IGNORE_FILETYPES, DEFAULT_IGNORE_DIRS } from '../composer/ideprotocolclient';
import { Logger } from "./logger";

/**
 * 常用工具方法合集
 */
export function withPolling(
  callback: (clear: () => void) => void | Promise<void>,
  interval: number,
  timeout: number,
  shouldImmediatelyInvoke = false,
  onTimeout = () => {}
): void {
  const pollingInterval = setInterval(() => void callback(clearPolling), interval);

  const pollingTimeout = setTimeout(() => {
    clearInterval(pollingInterval);
    onTimeout();
  }, timeout);

  function clearPolling() {
    clearInterval(pollingInterval);
    clearTimeout(pollingTimeout);
  }

  if (shouldImmediatelyInvoke) {
    void callback(clearPolling);
  }
}

export function sleep(time: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, time));
}

export function rejectOnTimeout<T>(promise: Promise<T>, time: number): Promise<unknown> {
  return Promise.race([sleep(time).then(() => Promise.reject()), promise]);
}

export function waitForRejection<T>(promise: Promise<T>, time: number) {
  return Promise.race([sleep(time), promise.then(() => Promise.reject())]);
}

// eslint-disable-next-line
export function isFunction(functionToCheck: any): boolean {
  // eslint-disable-next-line
  return (
    functionToCheck && {}.toString.call(functionToCheck) === '[object Function]'
  );
}

export async function asyncFind<T>(
  arr: T[],
  predicate: (element: T) => Promise<boolean>
): Promise<T | null> {
  // eslint-disable-next-line no-restricted-syntax
  for await (const element of arr) {
    if (await predicate(element)) {
      return element;
    }
  }
  return null;
}

export function formatError(error: Error): string {
  return `OS: ${process.platform} - ${process.arch}\n Error: ${error.name}\nMessage: ${
    error.message
  }\nStack: ${error.stack || ''}`;
}

export function escapeRegExp(value: string): string {
  return value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export function trimEnd(str: string, suffix: string): string {
  return str.replace(new RegExp(`${escapeRegExp(suffix)}$`), '');
}

export function escapeTabStopSign(value: string): string {
  return value.replace(new RegExp('\\$', 'g'), '\\$');
}

export function isMultiline(text?: string): boolean {
  return text?.includes('\n') || false;
}

export function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }

  return text;
}

/**
 * 字符串转JSON对象
 * @param str
 * @returns
 */
export function str2obj(str: string | undefined) {
  if (!str) {
    return {};
  }

  try {
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
}

/**
 * uri路径参数转JSON对象
 * @param query
 * @returns
 */
export function parseUriQuery(query: string) {
  const params: Record<string, string> = {};

  if (!query) {
    return params;
  }

  const pairs = query.split('&');

  pairs.forEach(pair => {
    const [key, value] = pair.split('=');

    if (key && value) {
      params[decodeURIComponent(key)] = decodeURIComponent(value);
    }
  });

  return params;
}

/**
 * 生成uuid
 * @returns {*} string
 */
export function generateUUID() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }

  return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4();
}

/**
 * 防抖带返回值
 * @param callback
 * @param limit
 * @returns
 */
export function debounceWithPromise<T extends unknown[], R>(
  callback: (...rest: T) => R,
  limit: number,
  abortValue?: string
): (...rest: T) => Promise<R | undefined> {
  let timer: ReturnType<typeof setTimeout>;
  let cancel = () => {};

  return function (...rest): Promise<R | undefined> {
    cancel();
    return new Promise((resolve, reject) => {
      timer = setTimeout(() => resolve(callback(...rest)), limit);
      cancel = () => {
        clearTimeout(timer);
        if (abortValue !== undefined) {
          reject(abortValue);
        }
      };
    });
  };
}

/**
 * 一般防抖处理
 */
export function debounce<T extends unknown[]>(callback: (...rest: T) => void, limit: number) {
  let timeoutId: ReturnType<typeof setTimeout>;

  return function (...rest: T): void {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => callback(...rest), limit);
  };
}

/**
 * 获取当前文本的换行符
 * @param text
 * @returns
 */
export function getEndOfLine(text: string) {
  if (/\r\n/.test(text)) {
    return '\r\n';
  } else if (/\n/.test(text)) {
    return '\n';
  } else if (/\r/.test(text)) {
    return '\r';
  } else {
    return '';
  }
}

export function filePathBySystem(filePath: string) {
  if (process.platform === 'win32') {
    // Windows系统，如果路径不以反斜杠结尾，添加两个反斜杠
    return filePath.endsWith('\\') ? filePath : `${filePath}\\`;
  } else {
    // UNIX系统，如果路径不以斜杠结尾，添加一个斜杠
    return filePath.endsWith('/') ? filePath : `${filePath}/`;
  }
}

export function getEnvTag(): string | undefined{
  const runtimeEnv = process.env.RUNTIME_ENVIRONMENT;
  return runtimeEnv;
}

export function generateIgnoreList() {
  Logger.debug(`[common] generateIgnoreList ignoreList from server: ${getCodeAIConfig()?.ignoreList}`);
  let ignoreList;
  try {
    ignoreList = getCodeAIConfig()?.ignoreList
      ? JSON.parse(getCodeAIConfig()!.ignoreList)
      : [...DEFAULT_IGNORE_FILETYPES, ...DEFAULT_IGNORE_DIRS];
  } catch (e) {
    console.warn('Failed to parse ignoreList from config:', e);
    ignoreList = [...DEFAULT_IGNORE_FILETYPES, ...DEFAULT_IGNORE_DIRS];
  }
  return ignoreList;
}