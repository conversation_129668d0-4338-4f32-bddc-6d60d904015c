import { CancellationToken, Position, TextDocument } from 'vscode';
import { AutocompleteResult } from './types';
import { getFileNameWithExtension, getDocumentPrefixAndSuffix } from '../utils/textEditor';
import { CodeCompleteEnginInst } from './codeCompleteEngin';
import { Logger } from '../utils/logger';
import { ImportSnippet } from './types';
import { DataReportServiceInst } from '../service/dataReportService';
import { ActivityType } from '../service/types/dataReport';
import RunCompletionCache from './runCompletionCache';
import { getCodeAIConfig } from '../common/globalContext';

/**
 * 向CodeCompleteEngin发送请求
 * @param
 * @returns
 */
export default async function runCompletion({
  document,
  position,
  isAuto,
  currentSuggestionText = '',
  retry,
}: {
  document: TextDocument;
  position: Position;
  isAuto: boolean;
  currentSuggestionText?: string;
  retry?: {
    cancellationToken?: CancellationToken;
  };
  }): Promise<AutocompleteResult | null | undefined> {
  // todo：待对接设置页面的变更
  // const fileName = document.fileName;
  // const fileExtension = path.extname(fileName).substring(1);
  // if (isExtensionDisabled(fileExtension)) {
  //   logger.debug(`[secidea-cpl-${requestId}] Skipped - extension disabled: ${fileExtension}`);
  //   return null;
  // }
  // const completionMode = getCompletionMode();


  const startTime = Date.now();
  // 获取跨文件关联snippets
  const importSnippets = getImportSnippets(document, position);
  // const snippetsCharacterLimit = getSnippetsContentSize(importSnippets);
  const snippetsCharacterLimit = "0"

  // 获取光标前后字符
  const { prefix, suffix } = getDocumentPrefixAndSuffix(document, position, snippetsCharacterLimit);
  const mPrefix = prefix + currentSuggestionText;

  // 自动补全是否命中缓存
  if (isAuto && RunCompletionCache.isMatchCache(mPrefix)) {
    const cacheResult = RunCompletionCache.getMatchItem(mPrefix);
    Logger.debug(`[runCompletion] trigger the cache`);

    if (cacheResult) {
      return cacheResult;
    }
  }

  const requestData = {
    filename: document.uri.fsPath,
    isAuto,
    language: document.languageId,
    before: mPrefix,
    after: suffix,
    line: position.line,
    character: position.character,
    importSnippets,
  };

  let result = await CodeCompleteEnginInst.askCompletion(requestData);

  // 处理临界场景：当带下拉框提示词且answer返回为空，仍显示补全即currentSuggestionText
  if (currentSuggestionText && !result?.results?.length) {
    result = {
      old_prefix: mPrefix,
      results: [
        {
          new_prefix: mPrefix,
          answer: '',
          old_suffix: '',
          new_suffix: '',
        },
      ],
    };
  }

  if (result?.results.length) {
    if (isAuto) {
      RunCompletionCache.setItems(result.results);
    }

    dataReport(result, isAuto, startTime);

    return result;
  }

  return undefined;
}

/**
 * 上报代码补全生成情况
 * @param result
 * @param isAuto
 * @param startTime
 */
function dataReport(result: AutocompleteResult, isAuto: boolean, startTime: number) {
  const prefix = result.old_prefix;
  const latency = Date.now() - startTime;
  result.results.forEach(result => {
    const answer = result?.answer || '';
    DataReportServiceInst.notifyUserActivity(
      ActivityType.CODE_COMPLETION_GEN, 
      answer, 
      prefix,
      isAuto,
      latency
    );
  });
}

/**
 * 获取跨文件关联snippets
 * @param document
 * @param position
 */
function getImportSnippets(document: TextDocument, position: Position) {
  const languageId = document.languageId;
  // const relativeObjects: RelativeCodeObject[] | null =
  //   ProjectCodeIndexer.getInstance().FindRelativeObject(
  //     languageId,
  //     document,
  //     position.line,
  //     position.character
  //   );

  const importSnippets: ImportSnippet[] = [];

  // if (relativeObjects) {
  //   for (const relativeObject of relativeObjects) {
  //     const importSnippet: ImportSnippet = {
  //       filePath: relativeObject?.getCodeFilePath(),
  //       snippet: relativeObject?.getObjectText(),
  //     };

  //     importSnippets.push(importSnippet);
  //   }
  // }

  // Logger.debug(
  //   `[runCompletion] getImportSnippets relativeObject: ${JSON.stringify(
  //     relativeObjects
  //   )}, importSnippets:${JSON.stringify(importSnippets)}`
  // );

  return importSnippets.length > 0 ? importSnippets : undefined;
}

// function getSnippetsContentSize(importSnippets: ImportSnippet[] | undefined): string {
//   const maxLength = parseInt(getCodeAIConfig()!.SnippetsCharacterLimit);
//   let totalLength = 0;
//   if (!importSnippets) {
//     Logger.debug(`[runCompletion] getSnippetsContentSize importSnippets is undefined`);
//     return totalLength.toString();
//   }

//   // 从数组末尾开始遍历
//   for (let i = importSnippets.length - 1; i >= 0; i--) {
//     const objectTextLength = importSnippets[i].snippet.length;

//     // 判断是否超出最大长度
//     if (totalLength + objectTextLength > maxLength) {
//       break;
//     }
//     totalLength += objectTextLength;
//   }

//   return totalLength.toString();
// }
