import {
  AnswerMode,
  IsAnswerEnd,
  ChatMessageType,
  PromptRoleType,
  QuestionType,
  RtnCode,
} from '../common/constants';
import QuestionTask from '../service/questionTask';
import { IQuestionTaskEvent } from '../service/types/questionTask';
import { CodeNaturalRequest, ICodeNaturalEnginHandler } from './types';
import { CodeAIRequestPromptChat } from '../service/types/codeAI';
import { Logger } from '../utils/logger';
import { IStatusBarHandler } from '../statusbar/types';
import { extractFirstCodeBlock } from '../utils/textEditor';

export default class CodeNaturalEngin implements IQuestionTaskEvent {
  private enginHandler: ICodeNaturalEnginHandler;

  private statusHandler: IStatusBarHandler;

  private curQuestion: QuestionTask | null = null;

  private curAllAnswer = '';

  private lastExtractAnswer = '';

  public constructor(engineHandler: ICodeNaturalEnginHandler, statusHandler: IStatusBarHandler) {
    this.enginHandler = engineHandler;
    this.statusHandler = statusHandler;
  }

  /**
   * 发起自然语言提问请求
   * @param request
   */
  public askQuestion(request: CodeNaturalRequest): string | undefined {
    this.curQuestion = new QuestionTask(this, AnswerMode.ASYNC);
    this.curAllAnswer = '';
    this.lastExtractAnswer = '';

    const prompts = this.getNaturalPrompts(request);
    const question = this.buildNaturalQuestion(request);

    const askResult = this.curQuestion.askQuestion({
      questionType: QuestionType.NATURAL_LANG,
      question,
      prompts,
      manualType: request.manualType,
    });

    if (!askResult.reqId) {
      this.enginHandler.onCodeNaturalResponse('', IsAnswerEnd.YES, '', askResult.rtnCode);
      this.statusHandler.onCodeChatStatusChange(askResult.rtnCode);
      return;
    }

    return askResult.reqId;
  }

  /**
   * 取消自然语言提问请求
   */
  public cancelQuestion() {
    if (this.curQuestion) {
      this.curQuestion.cancelQuestion();
    }
  }

  /**
   * 收到正常的自然语言响应处理
   * @param reqId
   * @param isEnd
   * @param answer
   */
  public onAnswer(reqId: string, isEnd: number, answer: string): void {
    if (reqId !== this.curQuestion?.getReqId()) {
      return;
    }

    this.curAllAnswer += answer;
    const extractAllAnswer = extractFirstCodeBlock(this.curAllAnswer);
    const startIdx = extractAllAnswer.indexOf(this.lastExtractAnswer);
    const extractAnswer =
      startIdx === -1
        ? extractAllAnswer
        : extractAllAnswer.slice(startIdx + this.lastExtractAnswer.length);

    this.lastExtractAnswer = extractAllAnswer;
    this.enginHandler.onCodeNaturalResponse(reqId, isEnd, extractAnswer, RtnCode.SUCCESS);

    // 更新状态栏，主要需要从异常恢复成正常
    this.statusHandler.onCodeChatStatusChange(RtnCode.SUCCESS);
  }

  /**
   * 收到异常响应处理
   * @param reqId
   * @param rtnCode
   */
  public onTaskError(reqId: string, rtnCode: number): void {
    if (reqId !== this.curQuestion?.getReqId()) {
      return;
    }
    Logger.debug(`[CodeNaturalEngin] onTaskError, rtnCode: ${rtnCode}`);

    this.statusHandler.onCodeChatStatusChange(rtnCode);
    this.enginHandler.onCodeNaturalResponse(reqId, IsAnswerEnd.YES, '', rtnCode);
  }

  /**
   * 获取前置prompts
   * @param request
   * @returns
   */
  private getNaturalPrompts(request: CodeNaturalRequest): CodeAIRequestPromptChat[] {
    const prompts: CodeAIRequestPromptChat[] = [];

    switch (request.manualType) {
      case ChatMessageType.MANUAL_GENERATE:
        prompts.push({
          role: PromptRoleType.SYSTEM,
          content: '接下来的回答请不要回答完整代码，只需要回答提出问题的答案即可。',
        });
        break;
      default:
        break;
    }

    return prompts;
  }

  /**
   * 构建自然语言问题
   * @param request
   */
  private buildNaturalQuestion(request: CodeNaturalRequest) {
    let question = request.question;

    switch (request.manualType) {
      case ChatMessageType.MANUAL_GENERATE:
        question = `我的问题是：“${request.question}”，问题所指的优化、修改、生成代码等要求都是指下面具体代码内容中的“问题提问位置”的所在的方法，请回答适用于该位置的片段${request.language}代码，具体代码内容（其中“问题提问位置”不是原有代码内容）：`;
        // 添加前后片断
        question += `${request.prefix || ''} “问题提问位置” ${request.suffix || ''}`;
        break;
      default:
        break;
    }

    return question;
  }
}
