import * as vscode from 'vscode';
import { SrdCommand } from '../common/constants';
import { getHttpServerHost } from '../utils/envUtil';
import { getHelpUrl } from '../login/loginUtils';

function openLink(name: string) {
  if (process.env.ISSEC !== 'false') {
    if (name === 'help') {
      vscode.env.openExternal(vscode.Uri.parse(getHelpUrl()));
    } else if (name === 'settings') {
      vscode.commands.executeCommand(SrdCommand.SETTINGS);
    }
  } else {
    // https://www.srdcloud.cn
    const target: any = {
      feedback: '/feedback/feedback',
      help: '/helpcenter/content?id=1189244999559761920',
      keyboard: '/helpcenter/content?id=1189245267638702080&pageIndex=1',
    };
    const path = target[name];
    const uri = `${getHttpServerHost()}${path}`;
    vscode.env.openExternal(vscode.Uri.parse(uri));
  }
}
export function registerSidebarToolbar(context: vscode.ExtensionContext) {
  if (process.env.ISSEC !== 'false') {
    context.subscriptions.push(
      vscode.commands.registerCommand(SrdCommand.OPEN_FEEDBACK, async () => {
        openLink('settings');
      }),
      vscode.commands.registerCommand(SrdCommand.OPEN_QUESTION, async () => {
        openLink('help');
      }),
      vscode.commands.registerCommand(SrdCommand.CLOSE_SIDEBAR, async () => {
        vscode.commands.executeCommand('workbench.action.closeSidebar');
      })
    );
  } else {
    context.subscriptions.push(
      vscode.commands.registerCommand(SrdCommand.OPEN_FEEDBACK, async () => {
        openLink('feedback');
      }),
      vscode.commands.registerCommand(SrdCommand.OPEN_QUESTION, async () => {
        openLink('help');
      }),
      vscode.commands.registerCommand(SrdCommand.CLOSE_SIDEBAR, async () => {
        vscode.commands.executeCommand('workbench.action.closeSidebar');
      })
    );
  }
}
