import { WorkItem } from './types';

export const mockWorkItems: WorkItem[] = [
  {
    id: 'mock-1',
    workItemKey: 'MOCK-1',
    title: 'Implement user authentication',
    description: 'Add user authentication flow using OAuth 2.0',
    creatorLoginName: 'developer1',
    creatorDisplayName: '创建人A',
    assigneeLoginName: 'developer1',
    assigneeDisplayName: 'Developer One',
    workitemTypeKey: 'task',
    workitemTypeName: 'Task',
    statusKey: 'in-progress',
    statusName: 'In Progress',
    statusGroupKey: 'active',
    url: 'https://example.com/work-items/MOCK-1'
  },
  {
    id: 'mock-2',
    workItemKey: 'MOCK-2',
    title: 'Fix sidebar navigation bug',
    description: 'Sidebar navigation breaks on mobile view when the screen width is less than 360px',
    creatorLoginName: 'developer2',
    creatorDisplayName: '创建人B',
    assigneeLoginName: 'developer1',
    assigneeDisplayName: 'Developer One',
    workitemTypeKey: 'bug',
    workitemTypeName: 'Bug',
    statusKey: 'to-do',
    statusName: 'To Do',
    statusGroupKey: 'pending',
    url: 'https://example.com/work-items/MOCK-2'
  },
  {
    id: 'mock-3',
    workItemKey: 'MOCK-3',
    title: 'Improve loading performance',
    description: 'Optimize initial load time by implementing lazy loading for images',
    creatorLoginName: 'developer3',
    creatorDisplayName: '创建人C',
    assigneeLoginName: 'developer2',
    assigneeDisplayName: 'Developer Two',
    workitemTypeKey: 'enhancement',
    workitemTypeName: 'Enhancement',
    statusKey: 'done',
    statusName: 'Done',
    statusGroupKey: 'completed',
    url: 'https://example.com/work-items/MOCK-3'
  }
]; 