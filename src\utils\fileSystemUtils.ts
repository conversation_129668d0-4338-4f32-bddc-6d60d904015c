import * as fs from 'fs';
import * as os from 'os';
import { execSync } from 'child_process';
import { Logger } from './logger'; // Assuming Logger is in ./logger relative to this new file's location

/**
 * Sets appropriate permissions for a given path (file or directory).
 * On Windows, it uses `icacls` to grant Full Control to the current user.
 * On POSIX systems, it uses `chmod` (0o700 for directories, 0o600 for files).
 *
 * @param targetPath The absolute path to the file or directory.
 * @param type Specifies whether the path is a 'file' or a 'directory'.
 */
export function setPathPermissions(targetPath: string, type: 'file' | 'directory'): void {
  const isWindows = os.platform() === 'win32';
  let username = '';

  if (isWindows) {
    try {
      username = os.userInfo().username;
      if (!username) { // Handle cases where username might be empty or undefined
        Logger.warn(`Username could not be determined for setting permissions on ${targetPath}. Skipping icacls.`);
        return;
      }
    } catch (userError: any) {
      Logger.error(`获取用户信息失败 for ${targetPath}: ${userError.message || userError}`);
      return; // Cannot proceed with icacls without username
    }
  }

  try {
    if (isWindows) {
      // Ensure path is quoted for icacls, especially if it contains spaces
      const quotedPath = `"${targetPath}"`;
      let icaclsCommand = '';
      if (type === 'directory') {
        icaclsCommand = `icacls ${quotedPath} /grant "${username}":(OI)(CI)F /Q`;
      } else { // 'file'
        icaclsCommand = `icacls ${quotedPath} /grant "${username}":F /Q`;
      }
      execSync(icaclsCommand);
    } else { // POSIX-like systems
      if (type === 'directory') {
        fs.chmodSync(targetPath, 0o700);
      } else { // 'file'
        fs.chmodSync(targetPath, 0o600);
      }
    }
  } catch (permError: any) {
    Logger.error(`设置权限失败 for ${type} at ${targetPath}: ${permError.message || permError}`);
  }
} 