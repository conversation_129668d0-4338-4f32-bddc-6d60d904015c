import {
  AnswerMode,
  ChatMessageType,
  QuestionAskType,
  ChatMessageTypeDesc,
  ChatTips,
  IsAnswerEnd,
  PromptRoleType,
  QuestionType,
  RtnCode,
  RtnMessage,
  WebViewRspCode,
} from '../common/constants';
import QuestionTask from '../service/questionTask';
import {
  CodeAIRequestPromptChat,
  CodeAIResponsePayload,
  KbQuoteRecv,
} from '../service/types/codeAI';
import { IQuestionTaskEvent } from '../service/types/questionTask';
import { IStatusBarHandler } from '../statusbar/types';
import { Logger } from '../utils/logger';
import { formatDate } from '../utils/time.utils';
import { ChatMessage, Conversation, ChatMessageSimple } from './conversation';
import ConversionManager from './conversationManager';
import { ChatRequest, ICodeChatEnginHandler, AnswerStopParams } from './types';
import { ChatHistoryServiceInst } from '../service/chatHistoryService';


export default class CodeChatEngin implements IQuestionTaskEvent {
  private enginHandler: ICodeChatEnginHandler;

  private statusHandler: IStatusBarHandler;

  private conversationManager: ConversionManager;

  private curQuestion: QuestionTask | null = null;

  private curConversation: Conversation | null = null;

  private curAllAnswer = '';

  private reqIdRelateMap: Map<string, string> = new Map();

  public constructor(
    handler: ICodeChatEnginHandler,
    conversationManager: ConversionManager,
    statusHandler: IStatusBarHandler
  ) {
    this.enginHandler = handler;
    this.conversationManager = conversationManager;
    this.statusHandler = statusHandler;
  }


  /**
   * 发起Chat请求
   * @param request
   * @returns
   */
  public askQuestion(conversation: Conversation, request: ChatRequest, relateReqId?: string) {
    this.curConversation = conversation;
    this.curQuestion = new QuestionTask(this, AnswerMode.ASYNC);
    this.curAllAnswer = '';

    const {
      question,
      type = ChatMessageType.CHAT_GENERATE,
      templateId,
      questionAskType = QuestionAskType.NEW_ASK,
      kbId,
      quote,
      parentReqId,
      chatContext = {},
      relatedFiles,
      selectedWorkItems
    } = request;

    const prompts = this.getChatPrompts(type, chatContext);
    const askResult = this.curQuestion.askQuestion({
      questionType: QuestionType.CODE_CHAT,
      question,
      prompts,
      manualType: type,
      dialogId: conversation.id,
      questionAskType,
      parentReqId,
      templateId,
      kbId,
      quote,
      relatedFiles,
      selectedWorkItems
    });

    if (!askResult.reqId) {
      this.enginHandler.onCodeChatResponse(
        undefined,
        IsAnswerEnd.YES,
        undefined,
        askResult.rtnCode === RtnCode.NOT_LOGIN
          ? ChatTips.NOT_LOGIN
          : RtnMessage[askResult.rtnCode],
        askResult.rtnCode === RtnCode.NOT_LOGIN ? WebViewRspCode.NOT_LOGIN : askResult.rtnCode
      );
      this.statusHandler.onCodeChatStatusChange(askResult.rtnCode);

      return;
    }

    if (relateReqId) {
      this.reqIdRelateMap.set(askResult.reqId, relateReqId);
    }

    // this.addOrUpdateConversationMessage(
    //   askResult.reqId,
    //   PromptRoleType.USER,
    //   question,
    //   createTime,
    //   type
    // );

    return askResult.reqId;
  }

  /**
   * 取消chat请求
   */
  public cancelQuestion() {
    if (this.curQuestion) {
      this.curQuestion.cancelQuestion();
    }
  }

  /**
   * 停止chat回答
   */
  public stopAnswer(reqId: string, request: AnswerStopParams) {
    if (this.curQuestion) {
      this.curQuestion.stopAnswer(reqId, request);
    }
  }

  /**
   * 收到正常的chat响应
   * @param reqId
   * @param isEnd
   * @param answer
   */
  public onAnswer(
    reqId: string,
    isEnd: number,
    answer: string,
    payload?: CodeAIResponsePayload
  ): void {
    if (reqId !== this.curQuestion?.getReqId()) {
      return;
    }

    // 记录完整回答，因为answer是分片的
    this.curAllAnswer += answer;

    // 获取当前reqId的问题内容
    const message = this.curConversation?.getMessage(`${reqId}_${PromptRoleType.USER}`);

    // 1) 特殊逻辑：若当前回答是空串，重新发起一次提问
    if (this.curAllAnswer.length === 0 && isEnd && message && !this.reqIdRelateMap.get(reqId)) {
      this.curQuestion = null;
      this.removeConversationMessage(reqId, PromptRoleType.USER);

      // 重新发起提问
      const newReqId = this.askQuestion(
        this.curConversation as Conversation,
        {
          question: message.content,
          createTime: message.createTime,
          type: message.type,
        },
        reqId
      );
      this.enginHandler.onCurReqIdChange(newReqId);
    } else {
      // 2) 其它情况正常处理
      // 通知chatViewProvider处理
      this.enginHandler.onCodeChatResponse(
        reqId,
        isEnd,
        answer,
        undefined,
        WebViewRspCode.SUCCESS,
        payload
      );

      // 更新会话存储到文件
      // this.addOrUpdateConversationMessage(
      //   reqId,
      //   PromptRoleType.ASSISTANT,
      //   this.curAllAnswer,
      //   undefined,
      //   message?.type
      // );

      // 更新状态栏，主要需要从异常恢复成正常
      this.statusHandler.onCodeChatStatusChange(RtnCode.SUCCESS);

      if (isEnd) {
        this.curQuestion = null;
        this.reqIdRelateMap.delete(reqId);
      }
    }
  }

  /**
   * 收到异常的chat响应
   * @param reqId
   * @param isEnd
   * @param answer
   */
  public onTaskError(reqId: string, rtnCode: number, errMsg?: string): void {
    if (reqId !== this.curQuestion?.getReqId()) {
      return;
    }

    Logger.error(`[CodeChatEngin] onTaskError, reqId:${reqId}, rtnCode:${rtnCode}`);

    switch (rtnCode) {
      case RtnCode.CANCEL:
        this.enginHandler.onCodeChatResponse(
          reqId,
          IsAnswerEnd.YES,
          undefined,
          ChatTips.QUESTION_CANCEL,
          WebViewRspCode.NEED_CLEAR
        );
        break;
      case RtnCode.STOP_ANSWER:
        this.enginHandler.onCodeChatResponse(
          reqId,
          IsAnswerEnd.YES,
          undefined,
          ChatTips.ANSWER_STOP,
          WebViewRspCode.SUCCESS
        );
        break;
      case RtnCode.NOT_LOGIN:
        this.enginHandler.onCodeChatResponse(
          reqId,
          IsAnswerEnd.YES,
          undefined,
          ChatTips.NOT_LOGIN,
          WebViewRspCode.NOT_LOGIN
        );
        this.statusHandler.onCodeChatStatusChange(rtnCode);
        break;
      case RtnCode.INVALID_ANSWER:
        this.enginHandler.onCodeChatResponse(
          reqId,
          IsAnswerEnd.YES,
          undefined,
          errMsg || RtnMessage[rtnCode],
          rtnCode
        );
        break;
      case RtnCode.INVALID_QUESTION:
      case RtnCode.KNOWLEDGE_BASE_DELETED:
        this.enginHandler.onCodeChatResponse(
          reqId,
          IsAnswerEnd.YES,
          undefined,
          errMsg || RtnMessage[rtnCode],
          rtnCode
        );
        break;
      default:
        this.enginHandler.onCodeChatResponse(
          reqId,
          IsAnswerEnd.YES,
          undefined,
          errMsg || RtnMessage[rtnCode],
          rtnCode
        );
        this.statusHandler.onCodeChatStatusChange(rtnCode);
        break;
    }

    this.curQuestion = null;
    this.reqIdRelateMap.delete(reqId);
  }

  public getChatMessages(questions: ChatMessageSimple): ChatMessage[] {
    const messages: ChatMessage[] = [];
    if (!Object.keys(questions).length) {
      return messages;
    }
    function traverse(node: ChatMessageSimple) {
      const content = node.content?.[0]?.text || node.content?.[0]?.image_url;
      messages.push({ role: node.role, content, isErrMsg: !!node.errMsg } as ChatMessage);
      if (node.children) {
        for (const child of node.children) {
          traverse(child);
        }
      }
    }
    traverse(questions);
    return messages;
  }

  /**
   * 获取chat的Prompts
   * @returns
   */
  private getChatPrompts(
    type: ChatMessageType,
    chatContext: ChatMessageSimple
  ): CodeAIRequestPromptChat[] {
    let prompts: CodeAIRequestPromptChat[] = [];
    let messages: ChatMessage[] = [];
    if (this.curConversation) {
      this.curConversation.messages = this.getChatMessages(chatContext);
    }

    switch (type) {
      case ChatMessageType.CHAT_GENERATE:
        // messages = this.curConversation?.messages?.filter(msg => !msg.isErrMsg) || [];
        messages = this.curConversation?.messages || [];
        break;
      default:
        break;
    }

    // 如果没有会话记录，需要设置system prompt
    if (!messages || messages.length === 0) {
      prompts.push({
        role: PromptRoleType.SYSTEM,
        content: ChatTips.FIRST_PROMPT,
      });
    }

    prompts = prompts.concat(
      messages.map(
        msg =>
        ({
          role: msg.role,
          content: msg.content,
        } as CodeAIRequestPromptChat)
      )
    );

    return prompts;
  }

  /**
   * 构建问题
   * @param type
   * @param question
   * @returns
   */
  private buildQuestion(type: ChatMessageType, question: string) {
    switch (type) {
      case ChatMessageType.EXPLAIN:
        return `请用中文解释描述下面的每一行代码所代表的意思，但并不是注释，如果回答中有生成代码，请用“\`\`\`”来区分代码。\n${question}`;
      case ChatMessageType.UNITTEST:
        // return `请为下面的代码的生成单元测试用例的代码，而不需要额外解释代码和描述步骤，回答生成的代码请用“\`\`\`”来区分代码。\n${question}`;
        return (
          '请用中文回答本问题，为下面提问的代码生成单元测试代码并且不需要额外的解释，只需回答生成的单元测试代码，回答生成的代码请用“```”来区分代码且代码注释使用中文。' +
          '\n我提问的具体代码是：' +
          `${question}`
        );
      case ChatMessageType.COMMENT:
        // return `请为下面的代码的每一行生成中文注释，并在原代码里补充，而不需要额外解释代码，回答生成的代码请用“\`\`\`”来区分代码。\n我提问的具体代码是：${question}`;
        return (
          '请为下面的代码的每一行生成中文注释，并在原代码里补充，而不需要额外解释代码，回答生成的代码请用“```”来区分代码。\n例如，提问问题：public void send(String message) {\n' +
          '\t\tthis.session.getAsyncRemote().sendText(message);\n' +
          '\t}\n生成代码注释；只需回答补充注释后的代码：```java\n// 定义了一个名为 "send" 的方法，该方法接受一个字符串参数 "message"\n' +
          'public void send(String message) {\n' +
          '    // 在 Websocket Session 对象上调用了 getAsyncRemote() 方法，这是 WebSocket API 的一部分\n' +
          '    // AsyncRemote 接口提供了异步发送消息到远端点的功能\n' +
          '    this.session.getAsyncRemote();\n' +
          '    \n' +
          '    /* 然后，它调用了 sendText() 方法将传入的 "message" 转换并通过 websocket 连接发送出去\n' +
          '       sendText() 是一个异步方法，意味着它会在后台运行，不阻塞当前线程 */\n' +
          '    .sendText(message);\n' +
          '}\n```。我提问的具体代码是：' +
          `${question}`
        );
      case ChatMessageType.OPTIMIZATION:
        // return `请针对下面的代码做出代码优化的建议，如果回答中有生成代码，请用“\`\`\`”来区分代码。\n${question}`;
        return (
          '请为下面提问的代码生成优化建议并给出优化后的代码，回答生成的代码请用“```”来区分代码且每一行代码用中文注释，答案需要划分为“潜在问题、优化方向、优化后的代码”三个组成部分，回答按照以下格式进行输出：\n' +
          '潜在问题 \n' +
          '1.分点描述\n' +
          '2.分点描述\n' +
          '\n  ' +
          '优化方向 \n' +
          '1.分点描述\n' +
          '2.分点描述\n' +
          '\n  ' +
          '优化后的代码 \n' +
          '```\n' +
          'public static int calculateSumOfProducts() {\n' +
          '    int sumOfProducts = 0;\n' +
          '    // 使用嵌套循环遍历0到9之间的所有整数\n' +
          '    for (int outerIndex = 0; outerIndex <= 9; outerIndex++) {\n' +
          '        for (int innerIndex = 0; innerIndex <= 9; innerIndex++) {\n' +
          '            // 累加乘积到总和中\n' +
          '            sumOfProducts += outerIndex * innerIndex;\n' +
          '        }\n' +
          '    }\n' +
          '    return sumOfProducts;\n' +
          '}\n' +
          '```\n' +
          '\n  ' +
          '接下来是提问的代码：' +
          '：' +
          `${question}`
        );
      default:
        return question;
    }
  }

  /**
   * 添加或更新会话中的消息内容
   * @param reqId
   * @param role
   * @param content
   * @returns
   */
  private addOrUpdateConversationMessage(
    reqId: string,
    role: PromptRoleType,
    content: string,
    createTime?: string,
    type?: ChatMessageType,
    isErrMsg?: boolean
  ) {
    if (!this.curConversation) {
      return;
    }

    let typeDesc = '';

    if (
      type &&
      role === PromptRoleType.USER &&
      (type === ChatMessageType.EXPLAIN ||
        type === ChatMessageType.UNITTEST ||
        type === ChatMessageType.COMMENT ||
        type === ChatMessageType.OPTIMIZATION)
    ) {
      typeDesc = `\n${ChatMessageTypeDesc[type]}`;
    }

    this.curConversation.addOrUpdateMessage({
      id: `${reqId}_${role}`,
      role: role,
      content: `${content}${typeDesc}`,
      createTime: createTime || formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss'),
      type,
      isErrMsg,
    });

    this.conversationManager.updateAndSendConversation(this.curConversation);
    this.conversationManager.saveConversations();
  }

  /**
   * 删除会话中的消息内容
   * @param reqId
   * @param role
   * @returns
   */
  private removeConversationMessage(reqId: string, role: PromptRoleType) {
    if (!this.curConversation) {
      return;
    }

    this.curConversation.removeMessage(`${reqId}_${role}`);
    this.conversationManager.updateAndSendConversation(this.curConversation);
    this.conversationManager.saveConversations();
  }

  public async updateConversationMessage() {
    if (this.curConversation) {
      // await this.conversationManager.updateConversationList(this.curConversation);
      this.conversationManager.updateConversation(this.curConversation);
    }
  }
}
