import { spawn } from 'child_process';
import { AgentConfig, CORE_AGENT, TABBY_AGENT } from '../types';
import { AgentManager } from '../agentmanager';
import { Logger } from '../../utils/logger';
import { AgentProcess } from '../process/AgentProcess';
import { TabbyAgentProcess } from '../process/TabbyAgentProcess';
import { CoreAgentProcess } from '../process/CoreAgentProcess';
import * as vscode from 'vscode';

export class ProcessManager implements vscode.Disposable {
  private agents: Map<string, AgentProcess>;

  private agentManager: AgentManager;

  private config: Map<string, AgentConfig> = new Map<string, AgentConfig>();

  private checkInterval: number;

  private monitorIntervals: Map<string, NodeJS.Timeout> = new Map();

  public constructor(config: Map<string, AgentConfig>, agentManager: AgentManager, checkInterval = 5000) {
    this.agents = new Map();
    this.config = config;
    this.agentManager = agentManager;
    this.checkInterval = checkInterval;
  }

  public async startAgent(agentProcess: AgentProcess, agentPath: string, port: number): Promise<any> {
    try {
      const config = this.config.get(agentProcess.getAgentName());
      const childProcess = agentProcess.buildProcess(agentPath, port, config!)
      Logger.info(`[ProcessManager] 启动Agent进程: ${agentProcess.getAgentName()}`);
      this.agents.set(agentProcess.getAgentName(), agentProcess);
      // this.startMonitoring(agentProcess.getAgentName());
      return childProcess;
    } catch (error) {
      Logger.error(`[ProcessManager] 启动Agent进程失败: ${error}`);
      // 不再处理状态栏通知，只记录日志并抛出异常
      throw error;
    }
  }

  public stopAll(): void {
    for (const [name, agent] of this.agents) {
      try {
        agent.getProcess().kill();
        agent.setStatus('stopped');
      } catch (e) {
        Logger.warn(`Error stopping agent process: ${String(e)}`);
      }
    }
    this.agents.clear();
  }

  private startMonitoring(agentName: string): void {
    this.stopMonitoring(agentName);
    const intervalId = setInterval(() => {
      const agent = this.agents.get(agentName);
      if (!agent) {
        return;
      }

      try {
        const process = agent.getProcess();
        if (!process || process.killed) {
          this.restartAgent(agentName);
          return;
        }

        // 检查进程是否响应
        if (process.pid) {
          process.kill(0);
        } else {
          this.restartAgent(agentName);
        }
      } catch {
        // 进程不存在，重启agent
        this.restartAgent(agentName);
      }
    }, this.checkInterval);
    this.monitorIntervals.set(agentName, intervalId);
  }

  private stopMonitoring(agentName: string): void {
    const intervalId = this.monitorIntervals.get(agentName);
    if (intervalId) {
      clearInterval(intervalId);
      this.monitorIntervals.delete(agentName);
    }
  }

  private async restartAgent(agentName: string): Promise<void> {
    const agent = this.agents.get(agentName);
    if (!agent) {
      return;
    }

    try {
      agent.getProcess().kill();
    } catch (e) {
      Logger.warn(`Error stopping agent process: ${String(e)}`);
    }

    // 重新启动agent
    const newProcess = await this.startAgent(agent, agent.getProcess().spawnfile, agent.getPort());

    // Notify AgentManager of restart
    this.agentManager.onAgentRestarted(agentName, newProcess);
  }

  public dispose(): void {
    // 清理所有监控
    for (const [agentName] of this.monitorIntervals) {
      this.stopMonitoring(agentName);
    }
    // ... 其他清理代码
  }
}
