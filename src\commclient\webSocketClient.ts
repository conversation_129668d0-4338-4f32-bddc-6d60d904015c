import { RawData, WebSocket } from 'ws';
import { Logger } from '../utils/logger';
import {
  NEED_WS_CLIENT_HEARTBEAT,
  WS_PATH,
  WS_HERRTBEAT_TIMEOUT,
  WS_RECONNECT_INTERVAL,
  WS_RECONNENT_LIMIT,
} from '../common/config';
import { ICommChannelEvent } from './types/codeAIComm';
import { ChannelStatus, MessageName, RtnCode } from '../common/constants';
import { SendMessageRtn, WSFrame } from './types/webSocket';
import { str2obj } from '../utils/common';
import { getWsServerHost } from '../utils/envUtil';

/**
 * WebSocket Client
 */
export default class WebSocketClient {
  private ws: WebSocket | null = null;

  private wsPath = WS_PATH;

  private wsServerUrl = '';

  private channelStatus = ChannelStatus.DISCONNECTED;

  private commHandler: ICommChannelEvent;

  private heartTimeoutTimer: NodeJS.Timer | undefined;

  private reconnectTimer: NodeJS.Timer | undefined;

  private isTerminate = false;

  private reconnectCount = 0;

  private canReconnect = false;

  public constructor(commHandler: ICommChannelEvent, wsPath?: string) {
    this.commHandler = commHandler;

    if (wsPath) {
      this.wsPath = wsPath;
    }

    this.wsServerUrl = `${getWsServerHost()}${this.wsPath}`;

  }

  /**
   * 获取通道状态
   * @returns
   */
  public getChannelStatus() {
    return this.channelStatus;
  }

  /**
   * 打开通道并启用重连机制
   * @returns
   */
  public connectToServer() {
    if (this.ws && this.channelStatus === ChannelStatus.CONNECTED) {
      return RtnCode.SUCCESS;
    }

    const rtnCode = this.openWS();
    this.enableReconnect();

    return rtnCode;
  }

  /**
   * 发送ws消息
   * @param message 消息内容
   * @returns
   */
  public sendMessage(message: string): SendMessageRtn {
    if (!this.ws || this.channelStatus === ChannelStatus.DISCONNECTED) {
      return new SendMessageRtn(RtnCode.NO_CHANNEL);
    }

    const frame = `${WSFrame.START}${message}${WSFrame.END}`;
    this.ws.send(frame);

    return new SendMessageRtn(RtnCode.SUCCESS);
  }

  /**
   * 主动关闭通道
   */
  public disconnect() {
    this.isTerminate = true;
    this.closeWS();

    clearInterval(this.reconnectTimer);
  }

  /**
   * 打开通道
   * @returns
   */
  private openWS(): RtnCode {
    // 仅当插件类型为Secidea时，才每次都获取wsServerUrl
    if (process.env.ISSEC !== 'false') {
      this.wsServerUrl = `${getWsServerHost()}${this.wsPath}`;
    }
    if (!this.wsServerUrl) {
      Logger.error('[WebSocketClient] wsServerUrl is empty, open failed');
      return RtnCode.NO_CHANNEL;
    }

    this.ws = new WebSocket(this.wsServerUrl, {
      perMessageDeflate: false,
    });

    this.ws.on('open', () => {
      this.onOpen();
    });

    this.ws.on('message', data => {
      this.onMessage(data);
    });

    this.ws.on('error', err => {
      this.onError(err);
    });

    this.ws.on('close', (code: number, reason: Buffer) => {
      this.onClose(code, reason);
    });

    return RtnCode.SUCCESS;
  }

  /**
   * 启用定时重连
   * @returns
   */
  private enableReconnect() {
    this.canReconnect = true;
    clearInterval(this.reconnectTimer);

    this.reconnectTimer = setInterval(() => {
      if (!this.isTerminate && (this.channelStatus === ChannelStatus.DISCONNECTED || !this.ws)) {
        this.reopenWS();
      }
    }, WS_RECONNECT_INTERVAL);
  }

  /**
   * 重连
   */
  private reopenWS() {
    if (this.reconnectCount < WS_RECONNENT_LIMIT) {
      this.reconnectCount += 1;
      this.openWS();
      Logger.info(`[WebSocketClient] reopen ws, count: ${this.reconnectCount}`);
    } else {
      this.disconnect();
      this.commHandler.onCommChannelEvent(this.channelStatus, this.isTerminate);
    }
  }

  /**
   * 关闭通道
   * @param code
   * @param reason
   */
  private closeWS(code?: number, reason?: string) {
    if (this.ws) {
      this.ws.close(code, reason);
      this.ws = null;
    }

    clearTimeout(this.heartTimeoutTimer);
  }

  /**
   * 打开通道成功后处理
   */
  private onOpen() {
    Logger.info(`[WebSocketClient] ws has opened`);
    this.isTerminate = false;
    this.reconnectCount = 0;

    this.setChannelStatus();
    this.commHandler.onCommChannelEvent(this.channelStatus);

    if (NEED_WS_CLIENT_HEARTBEAT) {
      this.startClientHeartBeat();
    }
  }

  /**
   * 通道接收的所有消息处理
   * @param data 接收的数据
   */
  private onMessage(data: RawData) {
    const raw = data.toString();
    const msgStr = raw.slice(WSFrame.START.length, raw.length - WSFrame.END.length);
    const msg = str2obj(msgStr);

    switch (msg.messageName) {
      case MessageName.CLIENT_HEART_RESP:
        this.sendClientHeartBeat();
        break;
      case MessageName.SERVER_HEART:
        this.sendServerHeartBeatResp();
        break;
      default:
        Logger.debug(`[WebSocketClient] onMessage, ${msgStr}`);
        this.commHandler.onCommChannelIncomMessage(msg);
        break;
    }
  }

  /**
   * 通道关闭后回调处理
   * @param code
   * @param reason
   */
  private onClose(code: number, reason: Buffer) {
    Logger.error(`[WebSocketClient] onclose, code:${code}, reason: ${reason?.toString()}`);
    this.setChannelStatus();

    if (!this.canReconnect) {
      this.isTerminate = true;
    }

    this.commHandler.onCommChannelEvent(this.channelStatus, this.isTerminate);
  }

  /**
   * 通道异常报错回调处理
   * @param err
   */
  private onError(err: Error) {
    Logger.error(`[WebSocketClient] onerror: ${err.message}`);
    this.setChannelStatus();
    this.commHandler.onCommChannelEvent(this.channelStatus);
  }

  /**
   * 设置ws通道状态
   */
  private setChannelStatus() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.channelStatus = ChannelStatus.CONNECTED;
    } else if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
      this.channelStatus = ChannelStatus.CONNECTING;
    } else {
      // 以下情况为连接断开
      // !this.ws || this.ws.readyState === WebSocket.CLOSE || this.ws.readyState === WebSocket.CLOSING
      this.channelStatus = ChannelStatus.DISCONNECTED;
    }
  }

  /**
   * 开始定时发送客户端心跳
   */
  private startClientHeartBeat() {
    clearTimeout(this.heartTimeoutTimer);
    this.heartTimeoutTimer = setTimeout(() => {
      this.sendClientHeartBeat();
    }, WS_HERRTBEAT_TIMEOUT);
  }

  /**
   * 发送客户端心跳
   */
  private sendClientHeartBeat() {
    const msg = this.buildHeartBeat(MessageName.CLIENT_HEART);
    this.sendMessage(msg);
  }

  /**
   * 响应服务端心跳
   */
  private sendServerHeartBeatResp() {
    const msg = this.buildHeartBeat(MessageName.SERVER_HEART_RESP);
    this.sendMessage(msg);
  }

  /**
   * 构建心跳消息
   * @param messageName messageName
   * @returns heartBeat message
   */
  private buildHeartBeat(messageName: string) {
    return JSON.stringify({ messageName });
  }
}
