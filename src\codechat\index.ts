import * as vscode from 'vscode';
import ChatViewProvider from './chatViewProvider';
import { IStatusBarHandler } from '../statusbar/types';
import CodeSelectionManager from './codeSelectionManager';
import CodeExceptionManager from '../codeexception/exceptionManager';
import { LoginServiceInst } from '../service/loginService';
import CodeDiffManager from '../codeDiff/codeDiffManager';
import CodeLensManager from '../codelens/codelensManager';

/**
 * 注册ActivityBar的ChatView
 * @param context Extension context
 */
export function registerChatView(
  context: vscode.ExtensionContext,
  statusHandler: IStatusBarHandler
) {
  const provider = new ChatViewProvider(context, statusHandler);
  const selectionManager = new CodeSelectionManager(context, provider);
  const codeLensManager = new CodeLensManager(context, provider);
  const exceptionManager = new CodeExceptionManager(context, provider);

  provider.setObservable(LoginServiceInst);

  /**
   * 注册WebViewProvider
   */
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(ChatViewProvider.viewType, provider, {
      webviewOptions: {
        retainContextWhenHidden: true,
      },
    })
  );

  /**
   * 注册代码块选中处理
   */
  context.subscriptions.push(selectionManager, exceptionManager);
  // context.subscriptions.push(selectionManager);
  // context.subscriptions.push(exceptionManager);
}
