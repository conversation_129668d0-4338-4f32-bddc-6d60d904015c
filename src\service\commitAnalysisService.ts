import { IsAnswerEnd, RtnCode } from '../common/constants';
import { CodeAIResponsePayload } from '../service/types/codeAI';
import { Logger } from '../utils/logger';
import { generateUUID } from '../utils/common';
import CodeAIRequestSender from './codeAIRequestSender';
import CodeAIRequestReceiver from './codeAIRequestReceiver';
import { ASYNC_MSG_RECV_TIMEOUT } from '../common/config';
import { IAnswerHandler } from './types/codeAI';
import { ChangeItem, ICommitMessageHandler } from '../workItem/types';
import { WorkItem } from '../workItem/types';
/**
 * 提交分析服务
 * 负责发送提交分析请求并处理流式响应
 */
export class CommitAnalysisService implements IAnswerHandler {
  private reqId!: string;
  private askTimeout: NodeJS.Timer | undefined;
  private handler: ICommitMessageHandler;

  public constructor(handler: ICommitMessageHandler) {
    this.handler = handler;
  }
    
  public getReqId(): string {
    return this.reqId;
  }

  /**
   * 执行提交分析
   * @param changeList 文件变更列表
   */
  public async analyzeCommit(
    workItemList: WorkItem[] | [],
    changeList: ChangeItem[]
  ): Promise<void> {
    // 生成请求ID
    this.reqId = generateUUID();
    
    Logger.info(`[CommitAnalysisService] Sending commit analysis request, reqId: ${this.reqId}`);
    
    // 注册回调处理器
    CodeAIRequestReceiver.addAnswerHandler(this);
    this.askTimeout = setTimeout(() => this.handleResvTimeout(), ASYNC_MSG_RECV_TIMEOUT);
    
    // 发送提交分析请求
    const response = await CodeAIRequestSender.sendCommitChatRequest(
      this.reqId,
      changeList,
      workItemList
    );
    
    if (!response || response.rtnCode !== RtnCode.SUCCESS) {
      const errorMsg = `Failed to send commit analysis request: ${response ? RtnCode[response.rtnCode] : 'Unknown error'}`;
      Logger.error(`[CommitAnalysisService] ${errorMsg}`);
      this.cancelReqIdTimeoutTask(this.reqId, true);
      this.handler.onTaskError(this.reqId, response?.rtnCode || RtnCode.SEND_ERROR);
      return;
    }
  }

  private handleResvTimeout() {
    if (this.reqId) {
      CodeAIRequestReceiver.deleteAnswerHandler(this.reqId);
      this.handler.onTaskError(this.reqId, RtnCode.RECV_TIMEOUT);
    }
  }

  private cancelReqIdTimeoutTask(reqId: string, isDelAnsHandler: boolean) {
    if (isDelAnsHandler && reqId) {
      CodeAIRequestReceiver.deleteAnswerHandler(reqId);
    }
    clearTimeout(this.askTimeout);
  }

  public cancelQuestion() {
    this.cancelReqIdTimeoutTask(this.reqId, true);
    this.handler.onTaskError(this.reqId, RtnCode.CANCEL);
  }

  /**
   * 停止当前分析
   */
  public stopAnalysis(): void {
    Logger.info(`[CommitAnalysisService] Stopping analysis, reqId: ${this.reqId}`);
    this.cancelReqIdTimeoutTask(this.reqId, true);
    this.handler.onTaskError(this.reqId, RtnCode.STOP_ANSWER);
  }

  // IAnswerHandler 接口实现
  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    // 只检查 reqId 是否匹配
    if (reqId !== this.reqId) {
      return;
    }

    const { isEnd = 0, answer = '', errMsg = '' } = payload || {};
    if (rtnCode !== RtnCode.SUCCESS) {
      this.cancelReqIdTimeoutTask(reqId, true);
      this.handler.onTaskError(reqId, rtnCode, errMsg);
      return;
    }

    if (isEnd === IsAnswerEnd.NO) {
      // 在流式响应过程中，不要设置 isAnalysisActive = false
      clearTimeout(this.askTimeout);
      this.handler.onAnswer(reqId, isEnd, answer, payload);
      this.askTimeout = setTimeout(() => this.handleResvTimeout(), ASYNC_MSG_RECV_TIMEOUT);
    } else if (isEnd === IsAnswerEnd.YES) {
      this.cancelReqIdTimeoutTask(reqId, true);
      this.handler.onAnswer(reqId, isEnd, answer, payload);
    }
  }
}


