import { DiffService } from "./DiffService";
import { DiffViewVerticalParams, DiffViewVerticalEventType, IDiffServiceHandler } from "../codechat/types";
import { DiffViewManager } from "./diffView";
import { DiffStatus } from "./types";

export class DiffViewHandler {
  private actionMap = new Map<DiffViewVerticalEventType, (params: DiffViewVerticalParams) => void>();
  private listener!: DiffViewManager;

  constructor(private handler: IDiffServiceHandler) {
    this.registerHandlers();
  }

  registerDiffStatusChanged() { 
    if (!this.listener) { 
      this.listener = DiffService.diffViewManager!;
      this.listener?.onDidChange((change) => {
        let status: DiffStatus = 'undecided';
        switch (change.type) {
          case 'accept':
            status = 'accepted';
            break;
          case 'reject':
            status = 'rejected';
            break;
          case 'partial_accept':
            status = 'partial_accepted';
            break;
        }
        DiffService.getInstance().updateFileStatus(change.path[0], status);
        const diffFiles = DiffService.getInstance().getDiffFiles();
        this.handler.onDiffStatusChanged(diffFiles);
      });
    }
  }

  private registerHandlers(): void {
    this.actionMap.set(
      DiffViewVerticalEventType.OPEN_DIFF_VIEW_VERTICAL,
      (params) => { 
        this.registerDiffStatusChanged();
        DiffService.getInstance().openDiffEditor(params.path, params.content);
      }
    );

    this.actionMap.set(
      DiffViewVerticalEventType.ACCEPT_FILE_DIFF,
      (params) => { 
        DiffService.getInstance().acceptFileDiffWithoutOpening(params.path, params.content);
      }
    );

    this.actionMap.set(
      DiffViewVerticalEventType.UNDO_FILE_DIFF,
      (params) => { 
        DiffService.getInstance().undoFileDiffWithoutOpening(params.path, params.content, params.originalContent);
      }
    );

    this.actionMap.set(
      DiffViewVerticalEventType.REJECT_FILE_DIFF,
      (params) => {
        DiffService.getInstance().rejectFileDiffWithoutOpening(params.path, params.content);
      }
    );
  }

  public handleDiffViewVerticalRequest(request: DiffViewVerticalParams): void {
    const handler = this.actionMap.get(request.reqType as DiffViewVerticalEventType);
    if (handler) {
      handler(request);
    }
  }
}