## v1.5.3 - 2025.06.16

支持IDE版本：1.68.1及以上

更新内容：

- 优化代码补全效果，支持python跨文件关联的代码生成

- 优化智能编程diff窗口的显示逻辑，支持撤回对变更的操作

- 问答面板上增加代码提交入口，提交代码关联工作项处支持关联多个工作项

- 加快项目索引速度

- 修复其他已知问题

  

## v1.5.2 - 2025.05.30

支持IDE版本：1.68.1及以上

更新内容：

- 支持工作项提问，试试根据工作项生成代码吧

- 支持根据变更内容生成提交信息并关联工作项

- 智能编程生成文件支持分块采纳

- 支持对项目和个人知识库进行提问

- 生成的终端命令可以一键执行

- 优化代码补全速度

- 修复已知问题

  

## v1.5.1 - 2025.05.16

支持IDE版本：1.68.1及以上

更新内容：

- 优化索引构建机制，加速索引过程

- 提升当前工程问答和目录问答效果

- 问答时调整关联文件问答的限制

- 优化了交互体验和视觉效果

  

## v1.5.0 - 2025.04.30

支持IDE版本：1.68.1及以上

更新内容：

- 新增智能编程功能，支持输入需求后AI对项目多文件进行修改

- 新增关联代码库和目录进行问答

- 优化代码补全中的手动多行补全

- 新增首页更新内容提醒

  

## v1.4.2 - 2025.03.25

支持IDE版本：1.68.1及以上

更新内容：

- 开发问答支持选择项目文件进行关联提问，快来试试吧

- 选中代码右键进行“代码优化”和“生成代码注释”支持查看diff

- 问答过程中窗口支持上下滚动

- 修复了其他已知问题

  

## v1.4.1 - 2025.03.07

支持IDE版本：1.68.1及以上

更新内容：

- 开发问答接入DeepSeek v3模型
- 优化了回答内容的输出效果，现在回答更流畅了
- 修复了其他已知问题



## v1.4.0 - 2025.02.28

支持IDE版本：1.68.1及以上

更新内容：

- 代码补全跨文件关联支持javascript语言，快来试试吧！

- 优化了整个界面的样式，CodeFree焕然一新

- 修复了其他已知问题

  

## v1.3.9 - 2025.01.17

支持IDE版本：1.68.1及以上

更新内容：

- 代码补全跨文件关联支持go语言，快来试试吧！

- 优化交互问题

- 修复了其他已知问题

  

## v1.3.8 - 2024.12.27

支持IDE版本：1.68.1及以上

更新内容：

- 优化用户异常状态时的页面状态

- 优化重新回答的场景交互

- 修复了其他已知问题

  

## v1.3.7 - 2024.11.08

支持IDE版本：1.68.1及以上

更新内容：

- 代码补全支持Java的跨项目文件关联，可以智能识别项目中不同文件之间的关联性，让您的编码过程更加流畅自然。更多语言的支持正在开发中
- 优化了代码补全的机制，更多情景下出现补全建议
- 增加了代码补全缓存机制 有效提高了代码补全效率
- 修复了其他已知问题

## v1.3.6 - 2024.08.30

支持IDE版本：1.68.1及以上

更新内容：

- 支持选择API知识库进行提问，可选择接口后直接生成代码，更多开发详情还可参考回答中的开发指南
- 知识库的回答增加相关链接
- 优化了网络有问题情况下的表现
- 修复了其他已知问题

## v1.3.5 - 2024.07.31

支持IDE版本：1.68.1及以上

更新内容：

- 支持异常报错解释功能
- 会话支持重新提问
- 优化代码补全上下文关联
- 对话界面优化
- 支持快捷指令
- 去除自然语言编程功能
- 修复了其他已知问题

## v1.3.4 - 2024.06.28

支持IDE版本：1.68.1及以上

更新内容：

- 历史会话支持显示最近更新时间
- 指令模板新增模板简介的展示
- 修复了其他已知问题

## v1.3.3 - 2024.06.14

支持IDE版本：1.68.1及以上

更新内容：

- 指令模板列表新增二开专区，可以直接查看和使用二开分类的模板
- 优化了代码补全的样式和格式
- 优化了异常回答的文字提示
- 优化回答过程中切换对话的逻辑
- 修复了其他已知问题

## v1.3.2 - 2024.05.31

支持IDE版本：1.68.1及以上

更新内容：

- 显示“我的创建”和“我的收藏”的指令模板，支持收藏模板，还可以前往网页版模板中心进行创建模板
- 历史对话记录与Jetbrains插件以及网页端的开发问答同步。注：5月31号之前的历史对话数据将会被清除
- 问答的回答支持点赞点踩，欢迎给我们反馈
- 优化了停止回答的功能
- UI界面优化
- 修复了其他已知问题

## v1.3.1 - 2024.05.17

支持IDE版本：1.68.1及以上

更新内容：

- 更新了指令模板列表，提供更丰富更场景化的开发类指令模板
- 指令模板支持查看“最近使用”的模板
- 对话框的界面优化，获得更清爽的对话体验
- 隐藏智能问答的功能
- 新增右上角帮助文档和反馈中心的跳转
- 选中代码后右键提问的优化
- 代码补全数据采集的优化
- 修复了其他已知问题

## v1.3.0 - 2024.04.30

支持IDE版本：1.68.1及以上

更新内容：

- 新增了代码优化功能，为代码提供专业的优化建议
- 新增了智能问答功能，现在插件中也可以提问通用问题和研发云使用问题了！
- 新增指令模板功能，在不会提问时，试试指令模板吧
- 优化了代码补全速度和补全质量
- 优化了页面样式
- 丰富了数据统计维度
- 更新了登录成功页
- 修复了其他已知问题

## v1.2.2 - 2024.04.26

支持IDE版本：1.68.1及以上

更新内容：

- 更新了产品标识，正式改名为研发云CodeFree
- 已正式上线插件市场
- 修复了其他已知问题

## v1.2.0 - 2024.01.25

支持IDE版本：1.68.1及以上

更新内容：

- 新增代码解释功能，选中代码后生成代码解释
- 新增代码注释功能，选中代码后生成代码注释，支持一键插入
- 新增生成单元测试功能，选中代码后生成单元测试代码，支持一键新建文件
- 修复了其他已知问题

## v1.1.0 - 2024.01.04

支持IDE版本：1.68.1及以上

更新内容：

- 新增自然语言编程功能，在新文件中使用快捷键唤起提问框进行提问
- 修复了其他已知问题

## v1.0.2 - 2023.12.08

支持IDE版本：1.68.1及以上

更新内容：

- 代码补全模型升级
- 优化版本更新流程
- 修复了其他已知问题

## v1.0.1 - 2023.11.10

支持IDE版本：1.68.1及以上

更新内容：

- 点击登录无需弹出消息气泡，直接跳转登录页面。新增左侧状态栏的登录入口和右键菜单登录入口。
- 修复了一些已知问题

## v1.0.0 - 2023.11.3

支持IDE版本：1.68.1及以上

更新内容：

- 上线了VS Code第一个插件版本，已上线研发云组件广场
- 新增代码补全功能，包括自动补全和手动触发补全
- 新增智能编程助手功能，提供问答服务，回答编程相关的问题