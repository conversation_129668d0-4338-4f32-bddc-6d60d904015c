import { AgentManager } from "../../agent/agentmanager";
import { TabbyAgentClient } from "../../agent/commclient/tabbyAgentClient";
import { CodeCompleteStatus, IsAnswerEnd, QuestionType, RtnCode } from "../../common/constants";
import Defered from "../../utils/defer";
import { Logger } from "../../utils/logger";
import { CodeCompleteEngin } from "../codeCompleteEngin";
import { AutocompleteParams, AutocompleteResult, CodeCompleteAnswer } from "../types";
import { CodeCompletionStrategy } from "./codeCompletionStrategy";
import { getGitRemoteUrls } from "../../common/globalContext";
import { generateUUID } from "../../utils/common";


export class AgentCodeCompletionStrategy implements CodeCompletionStrategy {

  private reqDeferedMap: Map<string, Defered<CodeCompleteAnswer>> = new Map();

  async getCodeCompletion(codeCompleteEngin: CodeCompleteEngin, request: AutocompleteParams): Promise<AutocompleteResult | undefined> {

    const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
    const defered = new Defered<CodeCompleteAnswer>();
    const id = TabbyAgentClient.generateUuid();
    this.reqDeferedMap.set(id, defered)

    // 开启补全状态
    codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.START, request.isAuto);
    const questionType = request.isAuto ? QuestionType.CODE_GEN : QuestionType.CODE_GEN_MANUAL;
    const stopWords = codeCompleteEngin.getStopWords(questionType, request);
    Logger.debug('[Agent Comletion Strategy] 发起补全: ' + JSON.stringify(request));

    tabbyClient?.request('provideCompletions', [{
      "id": generateUUID(),
      "filepath": request.filename,
      "language": request.language,
      "text": request.before + request.after,
      "position": request.before.length,
      "line": request.line,
      "col": request.character,
      "clipboard": "",
      "manually": !request.isAuto,
      "context": [],
      "stopWords": stopWords,
      gitUrls: await getGitRemoteUrls(),
    }, { "signal": true }],
      // agent client 对应消息 id
      id,
      (data: any) => this.handleAnswer(data))

    const curAnswer = await defered.wait
    this.reqDeferedMap.delete(curAnswer.reqId)
    const isCurReqId = id === curAnswer.reqId;

    if (isCurReqId) {
      if (curAnswer.rtnCode !== RtnCode.SUCCESS) {
        codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.ERROR, curAnswer.rtnCode);
      } else {
        codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.END);
      }
    }

    // 返回结果
    const result = codeCompleteEngin.buildCodeCompleteResult(curAnswer.answer, isCurReqId);
    return result
  }

  public handleAnswer(data: any) {
    const ans = data[1]
    const ansId = data[0]
    const defered = this.reqDeferedMap.get(ansId)

    if (defered) {

      if (ans['choices']) {
        if (ans['choices'].length > 0 && ans['choices'][0]['text']) {
          // 正确收到结果
          defered.setFinished({
            reqId: ansId,
            isEnd: IsAnswerEnd.YES,
            answer: ans['choices'][0]['text'],
            rtnCode: RtnCode.SUCCESS,
          });
        } else {
          // 无补全结果
          defered.setFinished({
            reqId: ansId,
            isEnd: IsAnswerEnd.YES,
            answer: "",
            rtnCode: RtnCode.SUCCESS,
          });

          Logger.debug('[Agent Comletion Strategy] cplService codecompeltion response: ' + data);
        }
      } else {
        // 没有收到补全结果，发送信息有问题
        defered.setFinished({
          reqId: ansId,
          isEnd: IsAnswerEnd.YES,
          answer: '',
          rtnCode: RtnCode.SEND_ERROR,
        });
        Logger.debug('[Agent Comletion Strategy] cplService codecompeltion response fail:' + JSON.stringify(data))
      }
    }
  }

  public isEnabled(): boolean {

    const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby')

    if (tabbyClient) {
      return tabbyClient.getRunning()
    }
    
    return false
  }

  cancel(): void {

  }
}