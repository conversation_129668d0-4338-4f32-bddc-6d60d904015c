import { useComposer } from '@/store/composer.js'
import { DiffFile } from "./types"
import { diffLines } from "diff"
import { computed } from 'vue'

const useModifyFile = () => {
  const statusConfig = {
    partial_accepted: 'partial_accepted', // 部分接受
    accepted: 'accepted', // 接受
    rejected: 'rejected', // 拒绝
    undecided: 'undecided', // 未操作
  }

  const composer = useComposer()

  // 接受
  function isAccepted(item: DiffFile) {
    return item.status === statusConfig.accepted
  }

  // 用户操作了
  function isOperation(item: DiffFile) {
    const arr = [statusConfig.accepted, statusConfig.rejected, statusConfig.partial_accepted]
    return arr.includes(item.status)
  }

  // 只要有一个是未操作的
  const isShowAcceptAllBtn = computed(() => {
    return composer.diffList.some((item: DiffFile) => item.status === statusConfig.undecided)
  })


  // 接收文件
  const onAccept = (item: DiffFile) => {
    composer.acceptChanged(item.path, item.afterContent)
    // 上报
    onReport(item)
  }

  // 拒绝文件
  const onRefuse = (item: DiffFile) => {
    composer.rejectChanged(item.path, item.originalContent)
  }

  // 撤回文件
  const onWithdraw = (item: DiffFile) => {
    console.log("撤回文件的接口参数", item);

    composer.withdrawChange({ path: item.path, content: item.afterContent, originalContent: item.originalContent })
  }

  // 撤回全部
  const onWithdrawAll = () => {
    composer.diffList.forEach((item: DiffFile) => onWithdraw(item))
  }

  // 接受所有
  const onAcceptAll = () => {
    const list = composer.diffList.filter((item: DiffFile) => !isOperation(item))
    list.forEach((item: DiffFile) => onAccept(item))
  }

  // 拒绝所有
  const onRefuseAll = () => {
    const list = composer.diffList.filter((item: DiffFile) => !isOperation(item))
    list.forEach((item: DiffFile) => onRefuse(item))
  }

  // 上报
  function onReport(item: DiffFile) {
    const dl = diffLines(item.originalContent, item.afterContent)

    let txt = ''
    dl.forEach(d => {
      if (d.added || d.removed) {
        txt += d.value
      }
    })
    if (txt) {
      composer.handleReportData(txt)
    }
  }

  return {
    isAccepted,
    isOperation,
    isShowAcceptAllBtn,
    composer,
    statusConfig,
    onWithdrawAll,
    onAccept,
    onRefuse,
    onWithdraw,
    onAcceptAll,
    onRefuseAll
  }
}

export default useModifyFile