import { IComposerServiceHandler } from '../codechat/types';
import {
  ProjectItem,
  ChatMsg,
  ChatMessage,
  DiffFileMsg,
  Message,
  MessageData,
  ContextInputItem,
  ComposerRequestParams,
  ideMessageTypes
} from '../composer/types';
import { AgentManager } from '../agent/agentmanager';
import { AgentHistoryUtil } from '../composer/history/AgentHistoryUtil';
import { DiffService } from '../diff/DiffService';
import { ContextInputService } from './ContextInputService';
import { IAgentMessageReceiver } from '../agent/commclient/MessageReceiver';
import { DiffMessage, DiffMsgData } from '../diff/types';
import { Conversation } from './conversation/conversation';
import { getProjectInfo } from '../utils/textEditor';
import * as vscode from 'vscode';
import { IdeProtocolClient } from './ideprotocolclient';
import { ComposerEventType } from '../codechat/types';
import { getCurrentUser } from '../common/globalContext';
import { Logger } from '../utils/logger';
import { CodeIndexService } from '../codeindex/CodeIndexService';
import { create } from 'domain';
import { getHttpServerHost } from '../utils/envUtil';

import { getGitRemoteUrls } from '../common/globalContext';
import { selectedWorkItem } from '../codechat/types';

export class ComposerService implements IAgentMessageReceiver {
  private conversations: Map<string, Conversation> = new Map();

  private ideProtocolClient: IdeProtocolClient | undefined;

  private currentConversation: Conversation | undefined;

  public constructor(private handler: IComposerServiceHandler) {
    this.initIdeProtocolClient();
  }

  private projectItem: ProjectItem | undefined;


  private initIdeProtocolClient(): void {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      this.ideProtocolClient = new IdeProtocolClient(null);
    } else {
      this.ideProtocolClient = new IdeProtocolClient(workspaceFolders[0].uri.fsPath);
    }
  }


  public startConversation(request: ComposerRequestParams) {
    const agentManager = AgentManager.getInstance();
    if (!agentManager.isAgentRunning('core')) { 
      const { reqType, dialogId } = request;
      this.handler.onComposerChatResponse(reqType, dialogId, true, '系统未加载完成，请稍等');
      return;
    }
    const { dialogId, input, contextInputItems, chatType, title, modelName, displayContent, createTime, selectedWorkItems = []} = request;
    const conversationId = dialogId;
    const conversation = new Conversation(conversationId);
    this.currentConversation = conversation;
    this.conversations.set(conversationId, conversation);
    this.projectItem = getProjectInfo(conversationId);

    // Start a coroutine to handle the conversation
    (async () => {
      conversation.setStatus('gettingContextItem');
      const contextInputService = new ContextInputService();
      const [, fileRanges] = await contextInputService.getContextCodeItemForInput(
        input,
        contextInputItems
      );
      conversation.setStatus('chatting');
      this.sendToCore(conversationId, input, chatType, fileRanges, title, modelName, displayContent, createTime, contextInputItems, selectedWorkItems);
    })();
  }

  public stopConversation(conversationId: string) {
    // this.conversations.delete(conversationId);
    const agentManager = AgentManager.getInstance();
    const agentClient = agentManager.getAgentCommClient('core');
    if (!agentClient) {
      throw new Error('核心代理客户端不可用');
    }
    agentClient.request('api/stopChat', null, null);
  }

  public onAgentMessageHandler(text: string): Promise<any> | undefined {
    if (text.includes('"messageType":"api/chat"')) {
      const message = this.parseMessage(text);
      if (message?.data.done) { 
        Logger.debug('done is true');
      }
      if (!message) {
        return Promise.resolve(null);
      }
      const conversation = this.conversations.get(message.messageId);
      if (!conversation) {
        return Promise.resolve(null);
      }
      this.handleComposerChatResponse(text, conversation);
    }

    if (text.includes('"messageType":"api/edits/getLatestInMemoryApplied"')) {
      const message = this.parseDiffFileMsg(text);
      if (!message) {
        return Promise.resolve(null);
      }
      const conversation = this.conversations.get(message.messageId);
      if (!conversation) {
        return Promise.resolve(null);
      }
      this.handleComposerDiffFileResponse(text, conversation);
    }


    const responseMap = JSON.parse(text);
    // console.log(`response raw:${JSON.stringify(responseMap)}`);
    const messageId = responseMap.messageId.toString();
    const messageType = responseMap.messageType.toString();
    const data = responseMap.data;

    if (messageType === 'indexProgress') {
      
      // 更新状态栏显示索引进度
      CodeIndexService.getInstance().onResponse(text);
    }

    if (ideMessageTypes.includes(messageType)) {
      this.ideProtocolClient?.handleMessage(text, (data: any) => {
        const message = JSON.stringify({
          messageId: messageId,
          messageType: messageType,
          data: data,
        });
        const agentClient = AgentManager.getInstance().getAgentCommClient('core');
        if (agentClient) {
          agentClient.write(message);
        } else {
          Logger.error("[ComposerService] 无法获取agent通信客户端");
        }
      });
    }

  }

  private handleStopComposerChatResponse(text: string) { 
    const conversationId = this.currentConversation?.getConversationId();
    const message = this.parseMessage(text);
    const messageData = message?.data;
    if (typeof messageData === 'string' && messageData === 'Successfully stopped') { 
      this.handler.onComposerChatResponse(ComposerEventType.COMPOSER_CHAT, conversationId!, true, '');
    }
    
  }


  private handleComposerChatResponse(data: any, conversation: Conversation) {
    const message = this.parseMessage(data);
    if (!message) {
      return Promise.resolve(null);
    }
    const content = message.data.content;
    const end = message.data.done;

    if (!conversation.hasMessage(message.messageId)) {
      conversation.addMessage(message.messageId, '');
    }
    const builder = conversation.getMessage(message.messageId);
    const conversationId = conversation.getConversationId();
    if (end) {
      if (!builder || builder.length === 0) {
        this.handler.onComposerChatResponse(
          ComposerEventType.COMPOSER_CHAT,
          conversationId,
          true,
          ''
        );
      } else {
        // this.handler.onComposerChatResponse(ComposerEventType.COMPOSER_CHAT, conversationId, true, builder);
        AgentHistoryUtil.getInstance().addHistory(this.projectItem!, [
          { role: 'assistant', content: builder },
        ]);
        this.handler.onComposerChatResponse(ComposerEventType.COMPOSER_CHAT, conversationId, true, content || '');
      }
      conversation.deleteMessageById(message.messageId);

      const agentManager = AgentManager.getInstance();
      const agentClient = agentManager.getAgentCommClient('core');

      if (!agentClient) {
        throw new Error('核心代理客户端不可用');
      }

      const diffFileMsg: DiffFileMsg = { project: this.projectItem }
      agentClient.request(
        'api/edits/getLatestInMemoryApplied',
        diffFileMsg,
        conversationId,
      );
      return Promise.resolve(null);
    }

    if (content && content.length > 0) {
      conversation.addMessage(message.messageId, builder + content);
      this.handler.onComposerChatResponse(ComposerEventType.COMPOSER_CHAT, conversationId, false, content);
    }
  }

  private async handleComposerDiffFileResponse(data: any, conversation: Conversation) {
    const diffMesssage = this.parseDiffFileMsg(data);
    if (!diffMesssage || diffMesssage.data.length === 0) {
      return;
    }

    try {
      const res = diffMesssage?.data;
      const diffService = new DiffService();
      await diffService.initialize(res);
      DiffService.setInstance(diffService);
      const conversationId = conversation.getConversationId();
      this.handler.onReportDiffFilesResponse(ComposerEventType.REPORT_DIFF_FILES, conversationId, diffService.getDiffFiles());
      this.conversations.delete(conversationId);
    } catch (e) {
      Logger.error(`[ComposerService] 处理diff文件响应错误:${JSON.stringify(e)}`);
    }
  }

  private parseMessage(input: string): Message | null {
    try {
      const jsonString = this.findJsonPart(input);
      return JSON.parse(jsonString) as Message;
    } catch (e) {
      Logger.error(`[ComposerService] 解析错误:${JSON.stringify(e)}`);
      return null;
    }
  }

  private findJsonPart(input: string): string {
    let jsonStartIndex = input.indexOf('{');
    while (jsonStartIndex !== -1) {
      try {
        const possibleJson = input.substring(jsonStartIndex);
        JSON.parse(possibleJson);
        return possibleJson;
      } catch (e) {
        jsonStartIndex = input.indexOf('{', jsonStartIndex + 1);
      }
    }
    throw new Error('未找到有效的JSON部分');
  }

  private async sendToCore(
    conversationId: string,
    message: string,
    chatType: string,
    referenceList: string[],
    title: string,
    modelName: string,
    displayContent: string,
    createTime: string,
    contextInputItems: ContextInputItem[],
    selectedWorkItems?: selectedWorkItem[]
  ) {
    const chatHistory = AgentHistoryUtil.getInstance().getHistoryByConversationId(
      this.projectItem!,
      conversationId
    );
    const historyMessages = chatHistory ? AgentHistoryUtil.getInstance().convertToChatMessages(chatHistory, this.projectItem!) : [];
    const uInfo = await getCurrentUser();
    const invokerId = uInfo.userId as string;
    const currentMessage: ChatMessage = {
      role: 'user',
      content: message,
      knowledgeBaseIds: [],
      userId: invokerId,
      project: this.projectItem!.projectId,
    }

    const composerRequest: ChatMsg = {
      project: this.projectItem!,
      messages: [...historyMessages, currentMessage],
      chatType,
      title,
      fileRanges: referenceList,
      modelName,
      oaiServerUrl: `${getHttpServerHost()}/api/acbackend/codechat/v1/completions`,
      gitUrls: await getGitRemoteUrls(),
      workItems: selectedWorkItems,
    };


    AgentHistoryUtil.getInstance().addHistory(this.projectItem!, [
      { role: 'user', content: message, displayContent, createTime, contextInputItems, selectedWorkItems},
    ]);
    Logger.info(`[ComposerService] sendtoCore: composerRequest project:${JSON.stringify(this.projectItem)}, fileRanges: ${JSON.stringify(referenceList)}, 
    modelName: ${modelName}, oaiServerUrl: ${composerRequest.oaiServerUrl}， workItems: ${JSON.stringify(selectedWorkItems)}`);

    const agentManager = AgentManager.getInstance();
    const agentClient = agentManager.getAgentCommClient('core');

    if (!agentClient) {
      throw new Error('核心代理客户端不可用');
    }

    agentClient.request('api/chat', composerRequest, conversationId);
  }

  private parseDiffFileMsg(input: string): DiffMessage | null {
    try {
      return JSON.parse(input) as DiffMessage;
    } catch (e: any) {
      Logger.error(`[ComposerService] 解析diff文件错误: ${e.message}`);
      return null;
    }
  }

}
