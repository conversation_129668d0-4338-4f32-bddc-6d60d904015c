import { ICodeAIDataReportReceiveHandler } from './types/codeAI';
import { ActivityType } from './types/dataReport';
import CodeAIRequestSender from './codeAIRequestSender';
import CodeAIRequestR<PERSON>eiver from './codeAIRequestReceiver';
import { Logger } from '../utils/logger';
import { RtnCode } from '../common/constants';
import { getEndOfLine, isMultiline } from '../utils/common';

class DataReportService implements ICodeAIDataReportReceiveHandler {
  public constructor() {
    CodeAIRequestReceiver.setDataReportReceiveHandler(this);
  }

  public onUserActivityNotify(code: number) {
    Logger.info(`[DataReportService] UserActivityNotify result code: ${code}`);
  }

  public async notifyUserActivity(
    activityType: ActivityType, 
    answer: string, 
    before?: string,
    isAuto?: boolean,
    latency?: number
  ) {
    if (!answer) {
      Logger.debug(`[DataReportService] userActivityNotify answer:${answer} is empty!`);
      return;
    }
    // const { lines, count } = this.calcCodeLines(answer, before);
    const { lines, count } = this.calcCodeLines1(answer, before);
    const sendMsgRtn = await CodeAIRequestSender.sendUserActivityNotify(
      activityType, 
      lines, 
      count,
      isAuto,
      latency
    );

    if (sendMsgRtn?.rtnCode !== RtnCode.SUCCESS) {
      Logger.debug(`[DataReportService] userActivityNotify error: ${JSON.stringify(sendMsgRtn)}`);
    }
  }

  public async notifyUserActivityByCodeLines(activityType: ActivityType, lines: number) { 
    if (!lines) { 
      Logger.debug(`[DataReportService] notifyUserActivityByCodeLines lines:${lines} is empty!`);
      return;
    }
    const count = 1;
    const sendMsgRtn = await CodeAIRequestSender.sendUserActivityNotify(activityType, lines, count);
    if (sendMsgRtn?.rtnCode !== RtnCode.SUCCESS) {
      Logger.debug(`[DataReportService] notifyUserActivityByCodeLines error: ${JSON.stringify(sendMsgRtn)}`);
    }
  }

  private calcCodeLines(answer: string, before?: string) {
    let lines = 0,
      count = 1;
    if (before) {
      const indexOfBefore = before.lastIndexOf('\n');
      const prefixLen =
        indexOfBefore !== -1 ? before.substring(indexOfBefore + 1).length : before.length;
      const indexOfAnswer = answer.indexOf('\n');
      if (indexOfAnswer !== -1) {
        const firstLineOfAnswerLen = answer.substring(0, indexOfAnswer).length;
        lines = Math.ceil((firstLineOfAnswerLen * 10) / (firstLineOfAnswerLen + prefixLen)) / 10;
        const len = answer.match(/\n/g)?.length || 0;
        lines = lines + len;
      } else {
        lines = Math.ceil((answer.length * 10) / (answer.length + prefixLen)) / 10;
      }
    } else {
      const res = answer.match(/\n/g);
      lines = res ? res.length : 1;
    }
    return {
      lines,
      count,
    };
  }

  private calcCodeLines1(answer: string, before?: string) {
    let lines = 0,
      count = 1;
    const endOfAns = getEndOfLine(answer);
    if (before) {
      const endOfBefore = getEndOfLine(before);
      const prefixs = endOfBefore ? before.split(endOfBefore) : [before];
      const prefixLen = prefixs[prefixs.length - 1].length;
      const answers = endOfAns ? answer.split(endOfAns) : [answer];
      const firstLineOfAnswerLen = answers[0].length;
      lines = Math.ceil((firstLineOfAnswerLen * 10) / (firstLineOfAnswerLen + prefixLen)) / 10;
      if (endOfAns) {
        const len = answers.length - 1;
        lines = lines + len;
      }
    } else {
      const res = endOfAns ? answer.split(endOfAns) : [answer];
      lines = res.length;
      // lines = res.length - 1;
    }
    return {
      lines,
      count,
    };
  }
}
export const DataReportServiceInst = new DataReportService();
