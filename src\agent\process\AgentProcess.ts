import { ChildProcess } from "child_process";
import { AgentConfig } from "../types";

export abstract class AgentProcess {
  protected process: ChildProcess = new ChildProcess(); // child_process.ChildProcess
  private port: number = 0;
  private status: 'running' | 'stopped' = 'running';

  public constructor() {

  }

  public getProcess(): ChildProcess {
    return this.process;
  }

  public getStatus(): 'running' | 'stopped' {
    return this.status;
  }

  public setStatus(status: 'running' | 'stopped'): void {
    this.status = status;
  }

  public getPort(): number {
    return this.port;
  }

  public setPort(port: number): void {
    this.port = port;
  }

  public isProcessRunning(): boolean {
    if (!this.process) {
      return false;
    }

    try {
      // 检查进程是否存在且可响应
      process.kill(this.process.pid!, 0);
      return !this.process.killed;
    } catch {
      return false;
    }
  }

  public checkProcessHealth(): boolean {
    return this.isProcessRunning();
  }

  public abstract buildProcess(agentPath: string, port: number, config: AgentConfig): ChildProcess;
  public abstract getAgentName(): string;
}