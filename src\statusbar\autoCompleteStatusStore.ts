import { isRunInCloudIDE } from '../adapter/common';
import { SecretStorageKey } from '../common/constants';
import { getGlobalContext } from '../common/globalContext';

export default class AutoCompleteStatusStore {
  public static async initEnabled() {
    const enabled = await this.getEnabled();

    if (enabled === undefined) {
      await this.setEnabled(true);
    }
  }

  public static async setEnabled(enabled: boolean) {
    const val = enabled ? 'true' : 'false';
    if (isRunInCloudIDE()) {
      await getGlobalContext().globalState.update(SecretStorageKey.CODECOMPLETE_AUTO_ENABLED, val);
    } else {
      await getGlobalContext().secrets.store(SecretStorageKey.CODECOMPLETE_AUTO_ENABLED, val);
    }
  }

  public static async getEnabled() {
    if (isRunInCloudIDE()) {
      return getGlobalContext().globalState.get(SecretStorageKey.CODECOMPLETE_AUTO_ENABLED);
    } else {
      return await getGlobalContext().secrets.get(SecretStorageKey.CODECOMPLETE_AUTO_ENABLED);
    }
  }

  public static async checkIfEnabled(): Promise<boolean> {
    const enabled = await this.getEnabled();

    return enabled === 'true';
  }
}
