<template>
  <div class="message-list-view-root" ref="messageListView">
    <template v-for="(item, index) in composer.messageList">
      <QuestionBubble v-if="item.role === 'user'" :item="item" />
      <AnswerBubble v-if="item.role === 'assistant'" :item="item" />
    </template>
    <PrinterBubble v-if="composer.isAnswering" :text="composer.lastAnsweringText"></PrinterBubble>
  </div>
</template>
<script setup>
import QuestionBubble from './QuestionBubble.vue';
import PrinterBubble from './PrinterBubble.vue';
import AnswerBubble from './AnswerBubble.vue';
import { useComposer } from '@/store/composer.js'
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { useChatStore } from '@/store/chat.ts'
const messageListView = ref(null)
const composer = useComposer();
const chatStore = useChatStore();


watch(() => chatStore.tab, () => {
  if (chatStore.tab === 'composer') {
    setTimeout(() => {
      composer.scrollToBottom()
    }, 300)
  }
})

function onWheel(e) {
  const event = e || window.event;
  if (event.deltaY < 0) {
    composer.isWheeling = true
  } else if (messageListView.value.scrollTop + messageListView.value.getBoundingClientRect().height + 250 >= messageListView.value.scrollHeight) {
    composer.isWheeling = false
  }
}
onMounted(() => {
  if(messageListView.value)
    messageListView.value.addEventListener('wheel', onWheel, true);
})
onBeforeUnmount(() => {
  if(messageListView.value)
    messageListView.value.removeEventListener('wheel', onWheel, true);
})


</script>

<style lang="scss">
.message-list-view-root {
  height: 100%;
  overflow-y: scroll;
}
</style>