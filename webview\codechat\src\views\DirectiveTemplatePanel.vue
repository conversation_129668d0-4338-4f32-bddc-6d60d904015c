<template>
  <div class="directive-panel-container">
    <div class="nav-header">
      <span class="nav-header__left" @click="chatStore.toggleDirectiveTemplate">
        <svg-icon name="back-arrow" size="14px" style="margin-right: 8px;"></svg-icon>
        指令模板
      </span>
      <span class="nav-header__right">
        <Toolbar @createNewConv="emit('createNewConv')" />
      </span>
    </div>
    <div class="nav-search-input">
      <!-- <svg-icon name="search" class="search-icon" size="13px" @click="onEnter"></svg-icon> -->
      <svg-icon name="search" class="close-icon" size="13px" @click="() => {
        onEnter()
      }"></svg-icon>
      <input v-model="keyword" class="search-input" @keydown.enter="onEnter" placeholder="搜索指令模板" />
    </div>
    <div class="nav-query-tabs">
      <a-tabs @change="onChangeTab" :default-active-key="currentTab">
        <a-tab-pane v-for="(item, index) of tabs" :key="item.value" :title="item.label">
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="nav-query-select">
      <!-- <select class="nav-select" :value="categoryId" @change="onChangeCategory">
        <option v-for="item in categoryList" :value="item.id" :key="item.id">
          {{ item.name }}
        </option>
      </select> -->
      <a-select v-model="categoryId" class="nav-select" @change="onChangeCategory">
        <a-option v-for="item in categoryList" :value="item.id" :key="item.id">
          {{ item.name }}
        </a-option>
      </a-select>
    </div>
    <div class="directives">
      <div v-for="item in templateData.templateList" class="directive-item" :key="item.id"
        @click="selectDirective(item)">
        <a-tooltip :content="getTemplateTooltipContent(item)" position="bl" mini :arrow-style="{ display: 'none' }">
          <div class="item-box">
            <div class="item-title">
              {{ item.name }}
            </div>
            <div class="item-content">{{ item.introduction || item.content }}</div>
            <div class="item-favor" v-if="currentTab !== 'owner'">
              <svg-icon v-if="item.favorite" class="svg-icon" name="favor-fill"
                @click.stop="toggleFavor(item)"></svg-icon>
              <svg-icon v-else class="svg-icon" name="favor" @click.stop="toggleFavor(item)"></svg-icon>
            </div>
          </div>
        </a-tooltip>
      </div>

      <div class="directives-footer">
        <div v-if="hasMoreData && !isLoadingTemplateList" class="directives-loadmore-btn" @click="onLoadMore">
          加载更多
        </div>
        <div v-if="isLoadingTemplateList" class="directives-loadmore-btn">
          加载中...
        </div>
        <!-- 没有更多数据，并且当前页不是第一页才显示 -->
        <span v-if="!hasMoreData && page.pageNum > 1">
          暂无更多数据
        </span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { WebViewReqCommand } from '@/constants/common';
import { WebviewRspCommand, PromptsEventType } from '@/constants/common';
import { useChatStore } from '@/store/chat';
import { isForbidden, showForbiddenModal } from '@/utils';
import * as sender from '@/MessageSender'

import { computed, getCurrentInstance, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import Toolbar from './Toolbar.vue';

const emit = defineEmits(['createNewConv']);

type DirectiveItem = {
  name: string;
  id: string;
  content: string;
  favorite: boolean
  introduction: string
};
type CategoryItem = {
  id: string,
  name: string
}

const app = getCurrentInstance();


const $EventBus = app?.appContext.config.globalProperties.$EventBus;

// 最近使用最大数量
const MAX_LAST_USED_SIZE = 30;
/**
 * props传参
 */
const chatStore = useChatStore();
const keyword = ref(''); // 搜索词

const defaultCategory = Object.freeze({
  id: '',
  name: '全部分类'
})
// 首分类
const tabs = [
  {
    label: '最近使用',
    value: 'lastUsed'
  },
  {
    label: '我的收藏',
    value: 'favorite'
  },
  {
    label: '我的创建',
    value: 'owner'
  },
  {
    label: '全部',
    value: 'all'
  },
  {
    label: '二开专区',
    value: 'custom'
  },
]
const currentTab = ref('all')

const templateData = reactive<{ templateList: DirectiveItem[], categoryList: CategoryItem[] }>({
  templateList: [], // 模板列表
  categoryList: [defaultCategory] // 模板分类
})

const categoryList = computed(() => {
  if (currentTab.value === 'custom')
    return templateData.categoryList.filter(item => {
      return item.name.includes('二开')
    })
  return templateData.categoryList
})

const name = ref('')
const categoryId = ref('')
const isLoadingTemplateList = ref(false)

// 分页相关
const page = reactive({
  pageNum: 1,
  pageDataCount: 10,
  totalCount: 0
})

// 是否显示加载更多
const hasMoreData = computed(() => {
  return page.totalCount > page.pageNum * page.pageDataCount
})

// 参数将被 watch，有变化，即调用查询模板
const templateQuery = computed(() => {
  const ret: any = {
    name: name.value,
    lastUsed: currentTab.value === 'lastUsed',
    categoryId: categoryId.value,
    pageDataCount: page.pageDataCount,
    reqType: PromptsEventType.GET_TEMPLATES,
  }
  if (['owner', 'favorite'].includes(currentTab.value)) {
    ret.type = currentTab.value
  } else if (['custom'].includes(currentTab.value)) {

  }
  return ret
})

onMounted(() => {
  window.addEventListener('message', listener);
  getTemplateTypes()
})

onUnmounted(() => {
  window.removeEventListener('message', listener);
})

watch(templateQuery, () => {
  page.pageNum = 1
  page.totalCount = 0
  templateData.templateList = []
  getTemplates()
}, { deep: true, immediate: true })

function onChangeTab(v: string | number) {
  currentTab.value = v as string
  if (currentTab.value === 'custom') {
    categoryId.value = categoryList.value[0] && categoryList.value[0].id
  } else {
    categoryId.value = ''
  }

}

function onChangeCategory(value: any) {
  categoryId.value = value
}

function getTemplateTypes() {
  sender.postMessage({
    command: WebViewReqCommand.PROMPTS_REQUEST,
    data: {
      reqType: PromptsEventType.GET_CATEGORIES
    }
  });
}
function getTemplates() {
  const param = {
    command: WebViewReqCommand.PROMPTS_REQUEST,
    data: {
      ...templateQuery.value,
      pageNum: page.pageNum,
    },
  }
  sender.postMessage(param);
  console.log('request', param)
}

function getTemplateTooltipContent(item: DirectiveItem) {
  const text = item.introduction || item.content
  return text.length > 300 ? text.slice(0, 300) + '...' : text
}

function toggleFavor(item: DirectiveItem) {
  console.log('toggleFavor', item)
  submitUsing(item.id, item.favorite ? 'unfavorite' : 'favorite')
  // item.favorite = !item.favorite
}

function listener({ data }: any) {
  // 只处理
  if (data.command === WebviewRspCommand.PROMPTS_RESPONSE) {
    console.log('res', data)
    if (isForbidden(data)) {
      showForbiddenModal(sender)
      return
    }

    if (!data.data) return
    if (!data.data.data) return
    if (data.data.data.code !== 0) return
    if (!data.data.data.data) return
    // 点赞操作
    const opt = data.data.data.data.operationType
    if (["favorite", "unfavorite"].includes(data.data.data.data.operationType)) {
      const templateId = data.data.data.data.templateId
      const item = templateData.templateList.find(item => item.id === templateId)
      if (item) {
        item.favorite = opt === 'favorite'
        // 如果当前在收藏页
        if (!item.favorite && currentTab.value === 'favorite') {
          templateData.templateList = templateData.templateList.filter(item => item.id !== templateId)
        }
      }
      return
    }


    const type = data.data.type
    const extData = data.data.data.data

    if (type === PromptsEventType.GET_TEMPLATES) {
      // 处理模板列表
      templateData.templateList = templateData.templateList.concat(extData.data)
      // 如果是最近使用，当总数量超出 MAX_LAST_USED_SIZE（30），强制设置为30 
      if (templateQuery.value.lastUsed) {
        page.totalCount = extData.totalCount > MAX_LAST_USED_SIZE ? MAX_LAST_USED_SIZE : extData.totalCount
      } else {
        page.totalCount = extData.totalCount
      }
      isLoadingTemplateList.value = false

      console.log('res templates', extData)
    } else if (type === PromptsEventType.GET_CATEGORIES) {
      // 模板分类
      templateData.categoryList = [defaultCategory].concat(extData)
    }
  }
}

function onLoadMore() {
  isLoadingTemplateList.value = true
  page.pageNum++
  getTemplates()
}

// 回车或者点icon
function onEnter() {
  name.value = keyword.value
}
function submitUsing(templateId: string, operationType: string = 'use') {
  console.log('submitUsing', arguments)
  sender.postMessage({
    command: WebViewReqCommand.PROMPTS_REQUEST,
    data: {
      reqType: PromptsEventType.OPERATE_TEMPLATE,
      templateId,
      operationType
    }
  });
}



function selectDirective(item: DirectiveItem) {
  // at tod
  $EventBus.emit('setFooterInputMessage', item);
  submitUsing(item.id)
  chatStore.toggleDirectiveTemplate()
}

</script>
<style lang="scss">
.directive-panel-container {
  height: 100%;
  overflow: hidden;

  .arco-tabs-tab {
    margin: 0 4px !important;
    // color: currentColor;

    &:hover {
      // color: currentColor !important;
    }

    .arco-tabs-tab-title {
      &::before {
        background-color: transparent !important;
      }

      &:hover {
        color: var(--vscode-input-foreground) !important;
      }
    }

    .arco-icon {
      color: var(--vscode-input-border) !important;
    }

    &.arco-tabs-tab-active {}
  }

  .nav-header {
    padding: 5px;
    display: flex;
    padding: 0 20px;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    border-bottom: 1px solid var(--vscode-panel-border);

    &__left {
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }

    &__right {}

    .svg-icon {
      cursor: pointer;
    }

    .nav-header__left {
      cursor: pointer;
      color: var(--vscode-input-foreground);

      &:hover {
        background-color: var(--vscode-toolbar-hoverBackground);
        outline: 4px solid var(--vscode-toolbar-hoverBackground);
        border-radius: 2px;
      }
    }

    .svg-icon:not(:last-child) {
      margin-right: 16px;
    }
  }

  .nav-query-tabs {
    box-sizing: border-box;
    padding: 0 20px;
  }

  .nav-query-select {
    margin-bottom: 12px;
    box-sizing: border-box;
    padding: 0 20px;
  }

  .nav-search-input {
    position: relative;
    margin-bottom: 16px;
    margin: 20px;
    margin-bottom: 10px;
    box-sizing: border-box;
    height: 36px;
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border, transparent);

    .search-input {
      height: 100%;
      width: 100%;
      border: 1px solid var(--vscode-input-border, transparent);
      color: var(--vscode-inputOption-activeForeground);
      background-color: var(--vscode-input-background);
      padding-left: 20px;
      padding-right: 34px;
      resize: none;

      &:focus {
        outline: 1px solid var(--vscode-input-border, transparent);
      }
    }

    .search-icon {
      cursor: pointer;
      position: absolute;
      left: 13px;
      top: calc(50% - 6.5px);
      color: var(--vscode-inputOption-activeForeground);
    }

    .close-icon {
      cursor: pointer;
      position: absolute;
      right: 13px;
      top: calc(50% - 6.5px);
      color: var(--vscode-inputOption-activeForeground);
      opacity: 0.8;
    }
  }

  .nav-class-types {
    margin-bottom: 16px;
    white-space: nowrap;
    height: 24px;
    display: flex;
    align-items: center;
    overflow: scroll;
    user-select: none;

    &::-webkit-scrollbar {
      display: none;
    }



    .text-btn:not(:last-child) {
      margin-right: 8px;
    }
  }

  .directives {
    height: calc(100% - 215px);
    overflow: auto;
    box-sizing: border-box;
    padding: 0 20px;
  }

  .directives-footer {
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    justify-content: center;

    .directives-loadmore-btn {
      padding: 8px 10px;
      display: inline-block;
      border: 1px solid var(--vscode-input-border);
      border-radius: 3px;
      cursor: pointer;

      &:hover {
        border: 1px solid var(--vscode-textLink-foreground);
      }
    }
  }

  .directive-item {
    position: relative;
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 12px;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);

    &:hover {
      outline: 1px solid var(--vscode-textLink-foreground);
      outline-offset: -1px;
    }

    .item-box {
      width: 100%;
      height: 100%;
    }

    .item-title {
      font-size: 14px;
      width: 100%;
      margin-bottom: 6px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .item-content {
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-size: 14px;
      opacity: 0.7;
    }
  }



  .nav-select {
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border);
    width: 100%;
    font-size: 16px;
    background-color: var(--vscode-list-inactiveSelectionBackground);

    &:focus {
      border-color: var(--vscode-textLink-foreground);
      outline: none;
      /* 取消默认的外边框 */
      box-shadow: 0 0 0 0.2rem var(--vscode-list-inactiveSelectionBackground);
      /* 添加一个淡蓝色的边框阴影 */
    }
  }
}

.item-favor {
  cursor: pointer;
  position: absolute;
  right: 14px;
  top: 8px;
}

.arco-tabs-nav {
  &::before {
    background-color: var(--vscode-input-border);
  }
}
</style>
