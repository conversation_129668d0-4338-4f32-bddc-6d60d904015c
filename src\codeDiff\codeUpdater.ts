// import { createPatch } from 'diff';
// import * as chalk from 'chalk';
import CodeFragmentLocator from './codeFragmentLocator';
export default class CodeUpdater {
  private sourceCode: string;
  private locator: CodeFragmentLocator;

  constructor(sourceCode: string) {
    this.sourceCode = sourceCode;
    this.locator = new CodeFragmentLocator(sourceCode);
  }

  /**
   * 更新代码片段
   */
  updateFragment(
    fragment: string,
    optimizedCode: string
  ): { updatedCode: string } {
    // 定位代码片段
    const location = this.locator.locate(fragment);
    let updatedCode: string;
    if (!location) {
      updatedCode = this.sourceCode + '\n' + optimizedCode;
    } else {
      // 应用缩进
      const indentedCode = this.applyIndentation(
        optimizedCode,
        location.indentation
      );

      // 更新代码
      const sourceLines = this.sourceCode.split('\n');
      const updatedLines = [
        ...sourceLines.slice(0, location.startLine),
        ...indentedCode.split('\n'),
        ...sourceLines.slice(location.endLine + 1)
      ];

      updatedCode = updatedLines.join('\n');

      // 生成差异
      // const diff = this.generateDiff(updatedCode);
    }
    return { updatedCode };
  }

  /**
   * 应用缩进
   */
  private applyIndentation(code: string, indentation: string): string {
    const lines = code.split('\n');
    // 获取第一行的缩进量
    const firstLineIndentation = lines[0].match(/^\s*/)?.[0].length ?? 0;
    return lines
      .map((line) => {
        // 移除每行的前导空白
        const trimmed = line.replace(/^\s+/, '');
        if (trimmed) {
          // 计算每行相对于第一行的缩进差值
          const currentIndentation = line.match(/^\s*/)?.[0].length ?? 0;
          // 确保额外的缩进不为负值
          const additionalIndent = Math.max(currentIndentation - firstLineIndentation, 0);
          return indentation + ' '.repeat(additionalIndent) + trimmed;
        }
        return line;
      })
      .join('\n');
  }

  /**
   * 生成差异
   */
  // private generateDiff(newCode: string): string {
  //   const patch = createPatch(
  //     'file.ts',
  //     this.sourceCode,
  //     newCode,
  //     '',
  //     '',
  //     { context: 3 }
  //   );

  //   return this.formatDiff(patch);
  // }

  /**
   * 格式化差异
   */
  // private formatDiff(diff: string): string {
  //   return diff
  //     .split('\n')
  //     .map(line => {
  //       if (line.startsWith('+')) {
  //         return chalk.green(line);
  //       }
  //       if (line.startsWith('-')) {
  //         return chalk.red(line);
  //       }
  //       if (line.startsWith('@')) {
  //         return chalk.cyan(line);
  //       }
  //       return line;
  //     })
  //     .join('\n');
  // }
}
