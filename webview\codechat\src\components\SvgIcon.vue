<template>
  <svg aria-hidden="true" class="svg-icon" :width="props.width || props.size" :height="props.height || props.size">
    <use :xlink:href="symbolId" fill="currentColor" />
  </svg>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  const props = defineProps({
    prefix: {
      type: String,
      default: 'icon',
    },
    name: {
      type: String,
      required: true,
    },
    size: {
      type: String,
      default: '1em',
    },
    width: {
      type: String,
      default: '',
    },
    height: {
      type: String,
      default: '',
    }
  });

  const symbolId = computed(() => `#${props.prefix}-${props.name}`);
</script>
