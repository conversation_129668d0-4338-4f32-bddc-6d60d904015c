import { Disposable, TextDocumentChangeEvent, window, workspace } from 'vscode';
import DocumentTextChangeContent from './documentTextChangeContent';
import tryApplyPythonIndentExtensionFix from './pythonIndentExtensionFix';
import { CodeCompleteEnginInst } from '../../codeCompleteEngin';

let shouldComplete = false;
let change = false;

function onChange(): void {
  change = true;
}

function onTextSelectionChange(): void {
  if (change) {
    shouldComplete = true;
    change = false;
  } else {
    shouldComplete = false;
  }
}

export function getShouldComplete(): boolean {
  return shouldComplete;
}

export function initTracker(): Disposable {
  return Disposable.from(
    workspace.onDidChangeTextDocument(({ contentChanges, document }: TextDocumentChangeEvent) => {
      const currentPosition = window.activeTextEditor?.selection.active;
      const relevantChange = contentChanges.find(
        ({ range }) => currentPosition && range.contains(currentPosition)
      );
      const contentChange = new DocumentTextChangeContent(document, relevantChange);
      const changeHappened =
        (contentChange.hasContentChange() && contentChange.isNotIndentationChange()) ||
        contentChange.isIndentOutChange();

      if (changeHappened) {
        onChange();
        tryApplyPythonIndentExtensionFix(contentChange);
        CodeCompleteEnginInst.clearReqParamKeyAnswerCache();
      }
    }),
    window.onDidChangeTextEditorSelection(onTextSelectionChange)
  );
}
