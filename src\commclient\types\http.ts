export class HttpError extends Error {
  public status: number;

  public code: number | undefined = undefined;

  public constructor(status: number, message?: string, code?: number) {
    super(message);
    this.status = status;
    this.code = code;
  }
}

export class HttpResponse<T> {
  public code: number | undefined = undefined;

  public optResult: number | undefined = undefined;

  public data: T | undefined = undefined;

  public message: string | undefined = undefined;

  public msg: string | undefined = undefined;
}

export interface IHttpResponseHandler {
  handleResponseError(error: HttpError): void;
}
