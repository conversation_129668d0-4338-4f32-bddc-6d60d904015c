CodeFree is an AI tool provided by China Telecom's SRDCloud.It is a comprehensive solution that leverages AI capabilities to assist in code development. This tool is integrated on the IDE side to implement features such as code completion, code generation, code optimization, and programming assistance. Moreover, it seamlessly integrates with various existing capabilities within the SRDCloud, extending the development workflow into the IDE. The ultimate goal is to enhance the efficiency of code writing and development by bringing the entire development process into the IDE environment.  

The features we currently support include:

- Code Completion: Use the Tab key to select when the code auto-completion appears. 
- Code Q&A: Open the programming assistant page using the shortcut Alt+Shift+K. 
- Natural Language Programming: Invoke the natural language query box using the shortcut Alt+Shift+A. 
- Code Explanation: Right-click on the selected code and choose “SRD Codefree” > “Code Explanation”. 
- Code Annotation: Right-click on the selected code and choose “SRD  Codefree” > “Code Annotation”. 
- Generate Unit Tests: Right-click on the selected code and choose “SRD  Codefree” > “Generate Unit Tests.”
- Code Optimization：Right-click on the selected code and choose “SRD  Codefree” > “Code Optimization.”
-  Error message explanation: Click "CodeFree One-Click Fix" at the error location.
-  Knowledge Base: Select a  srdcloud knowledge base to ask questions, and code can be generated directly based on the API documentation within the knowledge base.
-  AI Developer：View modifications of multiple project files after sending requests in AI Developer mode

## 研发云CodeFree AI编程助手

研发云CodeFree是中国电信研发云的AI工具。CodeFree AI编程工具是使用AI能力协助用户进行代码开发的综合解决方案。这个工具在IDE侧实现了代码补全、编程助手等功能，更进一步和研发云各种已有的能力拉通，实现将研发流程下沉到IDE，以提高代码编写开发效率的目标。

我们目前支持的功能有：

代码补全：当补全代码出现后，用tab键选择

开发问答：使用快捷键Alt+Shift+K打开编程助手页面

代码解释：选中代码后右键，选择“研发云codefree”>“代码解释”

代码注释：选中代码后右键，选择“研发云codefree”>“代码注释”

生成单元测试：选中代码后右键，选择“研发云codefree”>“生成单元测试”

代码优化：选中代码后右键，选择“研发云codefree”>“生成优化建议”

异常报错解释：在报错处点击“CodeFree一键修复“

知识库：选择研发云的知识库进行提问，可根据知识库中的API文档直接生成代码

智能编程：在智能编程模式下发送需求后查看项目多文件的修改

## 主要功能Features

### 代码补全 Code Completion

代码补全是本插件主要功能之一，通过本功能用户可以在编写代码过程中得到代码提示从而加快代码编写速度。代码补全功能包括触发、选择、确认和取消。具体功能如下：

- 自动补全：在IDE输入代码时自动触发的补全
- 手动补全：在代码中合适的位置，通过按组合键发起的补全，默认快捷键为ctrl+enter
- 确认选择：当补全代码出现后，用tab键选择
- 禁用补全：默认使用ctrl+shift+alt+o禁用代码补全

### 开发问答Code Q&A

开发问答，是以技术问答的形式，向用户提供开发和技术方面的建议。用户通过编程助手问答，可以在不离开IDE的情况下，获得解决当前代码工程的编程上的关键帮助，从而提升开发的质量和效率。具体操作如下：

- 点击页面侧边栏的图标或使用快捷键Alt+Shift+K打开编程助手页面
- 输入框输入问题，默认使用enter发送问题
- AI生成问题答案，其中代码部分单独列出，并可单独复制
- 查看历史问题，问题和答案均可复制
- 新建会话，自动保存当前会话
- 可以使用指令模板辅助提问

- 支持重新回答

### 代码解释Code Explanation

支持代码解释功能，选中代码后执行“代码解释”的命令，AI将为您详细解析代码，让您轻松跨越语言障碍，快速了解代码的功能逻辑，提高开发效率。具体操作如下：

- 在编辑器中选中代码后点击右键，选择“代码解释”功能
- 在对话框中查看生成结果

### 生成代码注释Code Annotation

支持生成代码注释功能，为选中的代码一键生成函数注释及行间注释，节省编写注释的时间，使代码变得清晰易读。具体操作如下：

- 在编辑器中选中代码后点击右键，选择“生成代码注释”功能
- 在对话框中查看生成结果

### 生成单元测试Generate Unit Tests

支持生成单元测试功能，为选中的代码生成单元测试，同时支持一键生成新文件，节省宝贵的开发时间，使开发过程更加高效。具体操作如下：

- 在编辑器中选中代码后点击右键，选择“生成单元测试”功能
- 在对话框中查看生成结果

### 代码优化Code Optimization

支持代码优化功能，为选中的代码生成优化建议，提升代码质量。具体操作如下：

- 在编辑器中选中代码后点击右键，选择“生成单元测试”功能
- 在对话框中查看生成结果

### 异常报错解释Explanation of Exception Error

支持对报错信息进行分析和提供修复建议，具体操作如下：

- 在编辑器中的报错处点击前方按钮或鼠标悬浮框中的QuickFix，选择“CodeFree一键修复“
- 在问题面板报错处右键或点击前方按钮，选择“CodeFree一键修复“
- 在对话框中查看生成结果

### 知识库提问Knowledge Base Inquiry

支持对研发云上您有权限的知识库进行提问，具体操作如下：

- 在输入框上方选择知识库，输入问题进行提问
- 当提问到知识库中的接口时，知识库提供相关的备选API可以进行选择
- 选择后可在对话框中直接生成调用代码

### 智能编程AI Developer

CodeFree智能编程（CodeFree AI Developer）作为新一代AI开发助手，具备跨文件代码协作功能和多文件代码修改的能力，通过人机协作模式帮助开发者完成开发工作，涵盖需求开发、缺陷修复、测试用例生成及规模化代码重构等场景。

- 多文件修改：当把需求发送给CodeFree智能编程之后，CodeFree会返回回答。回答中可能会包含对项目中多个文件的修改。具体的修改信息体现在输入框上方出现的小工作区内
- 审查和采纳：可以点击工作区的每一个文件查看CodeFree修改的详细内容，点击工作区中的“接受变更”或“撤销变更”的按钮进行整个文件的变更操作

## 版本更新Update

版本更新以及历史更新内容见https://www.srdcloud.cn/composq/AI

## 帮助文档Help

[帮助文档](https://www.srdcloud.cn/helpcenter/content?id=1189244999559761920)

## 使用反馈Feedback

使用中有任何问题，欢迎进入反馈QQ群862534630向我们提问。