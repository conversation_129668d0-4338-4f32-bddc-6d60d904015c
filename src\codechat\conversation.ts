import { ChatTips, PromptRoleType, ChatMessageType } from '../common/constants';
import { generateUUID } from '../utils/common';
import { formatDate } from '../utils/time.utils';
import { DialogInfo } from '../service/types/chatHistory';

/**
 * 会话中的消息
 */
export interface ChatMessage {
  // 提问消息id: [reqId]_[PromptRoleType.USER], 回答消息id:[reqId]_[PromptRoleType.ASSISTANT]
  id: string;
  role: PromptRoleType;
  content: string;
  createTime: string;
  type?: ChatMessageType;
  isErrMsg?: boolean;
}

export interface ChatMessageSimple {
  subService?: string;
  role?: string;
  content?: MultiMediaContent[],
  reqId?: string;
  reqTime?: string;
  modelName?: string;
  feekback?: string;
  children?: ChatMessageSimple[];
  errMsg?: string;
  customData?: any;
}

export interface MultiMediaContent {
  type: string;
  text?: string;
  image_url?: string;
  knowledge_base_id?: number;
  file_path?: string;
}

/**
 * 会话
 */
export class Conversation {
  public title: string;

  public id: string;

  public subService: string;

  public messages: ChatMessage[] = [];

  public questions: ChatMessageSimple = {};

  public createTime: string;

  public updateTime: string;

  public isNewConversation: boolean;

  public constructor(conversation: DialogInfo, messages?: ChatMessage[], questions?: ChatMessageSimple) {
    this.id = conversation.dialogId || generateUUID();
    this.title = conversation.title || ChatTips.CONVERSATION_TITLE;
    this.subService = conversation.subService || 'assistant';
    this.createTime = conversation.createTime || formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss');
    this.updateTime = conversation.updateTime || formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss');
    this.isNewConversation = conversation.isNewConversation || false;
    if (messages) {
      this.messages = messages;
    }
    if (questions) {
      this.questions = questions;
    }
  }

  /**
   * 新增或修改会话中的消息
   * @param message
   */
  public addOrUpdateMessage(message: ChatMessage) {
    const idx = this.messages.findIndex(msg => msg.id === message.id);

    if (idx === -1) {
      // 首次有提问时，更新会话标题
      if (this.messages.length === 0 && this.title === ChatTips.CONVERSATION_TITLE) {
        //2025.02.25修改，兼容关联文件问答上下文数组
        this.title = message.content;
      }

      this.messages.push(message);
    } else {
      this.messages.splice(idx, 1, message);
    }
  }

  /**
   * 删除会话中的消息
   * @param msgId
   */
  public removeMessage(msgId: string) {
    const idx = this.messages.findIndex(msg => msg.id === msgId);

    if (idx > -1) {
      this.messages.splice(idx, 1);
    }
  }

  /**
   * 通过id获取会话中的消息内容
   * @param msgId
   * @returns
   */
  public getMessage(msgId: string) {
    return this.messages.find(msg => msg.id === msgId);
  }

  public getConversationSubService() {
    return this.subService;
  }
}
