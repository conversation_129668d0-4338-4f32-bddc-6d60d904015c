import { getJavaHomePath } from './java/JavaHome';
import * as path from 'path';

type SupportedLanguage = 'javascript' | 'go' | 'java';
// | 'c'
// | 'cpp'
// | 'csharp'
// | 'kotlin'
// | 'python'
// | 'rust'
// | 'javascriptreact'
// | 'typescript'
// | 'typescriptreact';

export type LanguageIdentifier = SupportedLanguage | (string & {});

export const SUPPORTED_LANGUAGES: LanguageIdentifier[] = [
  'javascript',
  'go',
  'java',
  // 'c',
  // 'cpp',
  // 'csharp',
  // 'kotlin',
  // 'python',
  // 'rust',
  // 'javascriptreact',
  // 'typescript',
  // 'typescriptreact',
];

export function isSupportedLanguage(lang: LanguageIdentifier) {
  return SUPPORTED_LANGUAGES.includes(lang);
}

type LanguageItem = {
  languageId: LanguageIdentifier;
  families?: string[];
  fileExts: string[];
};

const SupportLanguagesList: LanguageItem[] = [
  {
    languageId: 'javascript',
    fileExts: ['.js', '.cjs', '.mjs'],
  },
  { languageId: 'go', fileExts: ['.go'] },
  { languageId: 'java', fileExts: ['.java'] },
  // {
  // 	languageId: 'cpp',
  // 	fileExts: ['.c', '.cc', '.cpp', '.cxx', '.hh', '.h', '.hpp', '.ino', '.m', '.pc', '.pcc'],
  // },
  // {
  // 	languageId: 'csharp',
  // 	fileExts: ['.cs', '.cln', '.aspx'],
  // },
  // {
  // 	languageId: 'kotlin',
  // 	fileExts: ['.kt, .kts, .ktm'],
  // },
  // { languageId: 'python', fileExts: ['.py'] },
  // {
  // 	languageId: 'rust',
  // 	fileExts: ['.rs', '.rs.in'],
  // },
  // {
  // 	languageId: 'javascriptreact',
  // 	fileExts: ['.jsx'],
  // },
  // { languageId: 'typescript', fileExts: ['.ts', '.mts'] },
  // {
  // 	languageId: 'typescriptreact',
  // 	fileExts: ['.tsx'],
  // },
];

const retrieveCache = new Map<string, string>();

/**
 * Infer the language of a file based on its filename.
 *
 * @param filename - The filename to infer the language from.
 * @returns The inferred language or undefined if the language could not be inferred.
 */
export function inferLanguage(filename: string): string {
  const extname = path.extname(filename);

  if (retrieveCache.has(extname)) {
    return retrieveCache.get(extname)!;
  }

  const found = SupportLanguagesList.find(item =>
    item.fileExts.some(ext => filename.endsWith(ext))
  );

  const languageId = found?.languageId || '';

  retrieveCache.set(extname, languageId);

  return languageId;
}

export function getSDKPath(languageId: string): string | undefined {
  if (languageId === 'java') {
    return getJavaHomePath();
  }
  return undefined;
}

export enum SupportedLanguages {
  JAVA = 'java',
  GO = 'go',
  JAVASCRIPT = 'javascript',
}
