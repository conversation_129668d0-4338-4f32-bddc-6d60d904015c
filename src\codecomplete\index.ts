import * as vscode from 'vscode';
import { SrdCommand } from '../common/constants';
import { selectionHandler, handleImports } from './selectionHandler';
import { registerInlineProvider } from './inlineSuggestions/registerInlineProvider';
import { FileWatchManager } from './registerWorkspaceProvider';

/**
 * 注册代码补全
 */
export async function registerCodeComplete(context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.commands.registerTextEditorCommand(SrdCommand.COMPLETION_IMPORTS, selectionHandler),
    vscode.commands.registerTextEditorCommand(SrdCommand.HANDLE_IMPORTS, handleImports)
  );
  const fileWatchManager = new FileWatchManager(context);
  await fileWatchManager.init(context, '**/*');

  context.subscriptions.push(await registerInlineProvider());

  context.subscriptions.push(fileWatchManager);
}
