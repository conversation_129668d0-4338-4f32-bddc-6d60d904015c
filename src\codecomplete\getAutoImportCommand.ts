import * as vscode from 'vscode';
import { AutocompleteResult, ResultEntry, SuggestionTrigger } from './types';
import { SrdCommand } from '../common/constants';

export default function getAutoImportCommand(
  result: ResultEntry,
  response: AutocompleteResult | undefined,
  position: vscode.Position,
  suggestionTrigger?: SuggestionTrigger,
  suggestText = '',
  isAuto?: boolean
): vscode.Command {
  return {
    arguments: [
      {
        currentCompletion: result.new_prefix,
        currentAnswer: suggestText + result.answer,
        completions: response?.results,
        position,
        oldPrefix: response?.old_prefix,
        suggestionTrigger,
        isAuto,
      },
    ],
    command: SrdCommand.COMPLETION_IMPORTS,
    title: 'accept completion',
  };
}
