export interface ComponentDTO {
  atomsharestate: number;
  atomyPublishstate: boolean;
  componentid: string;
  latestVersion: string;
  name: string;
  needCredentials: number;
  quality: number;
  rank: number;
  resourceType: number;
  shareMode: number;
  useCount: number;
}

export interface ComponentDetail {
  canDel: boolean;
  canPublish: boolean;
  componentDTO: ComponentDTO;
  contributors: number;
  dependentReposCount: number;
  dependentsCount: number;
  description: string;
  downloadUrl: string;
  forks: number;
  gitHub: string;
  homepage: string;
  quality: number;
  resourceType: number;
  stars: number;
}

export interface UpgradeVerResult {
  hasNewVersion: boolean;
  latestVersion?: string;
}
