import fetch, { RequestInit, Headers, BodyInit } from 'node-fetch';
import AbortController from 'abort-controller';
import { Logger } from '../utils/logger';
import { HTTP_TIMEOUT, TEMPLATES_PATH } from '../common/config';
import { HttpError, HttpResponse, IHttpResponseHandler } from './types/http';
import { getCurrentUser } from '../common/globalContext';
import { HttpStatusCode } from '../common/constants';
import { getHttpProxy } from './httpProxy';
import { getHttpServerHost } from '../utils/envUtil';

export default class HttpClient {
  private responseHandler: IHttpResponseHandler | undefined;
  private serviceType: string | undefined;

  public constructor(handler?: IHttpResponseHandler, type?: string) {
    this.responseHandler = handler;
    this.serviceType = type;
  }

  public async requestByPost<T>(
    path: string,
    body: BodyInit | null | undefined,
    options: RequestInit = {}
  ): Promise<T | undefined> {
    options.body = body;
    return await this.request('post', path, options);
  }

  public async requestByGet<T>(
    path: string,
    params: Record<string, string>,
    options: RequestInit = {}
  ): Promise<T | undefined> {
    const urlParams = new URLSearchParams(params).toString();
    const mergedPath = `${path}${urlParams ? '?' + urlParams : ''}`;

    return await this.request('get', mergedPath, options);
  }

  public async requestByDelete<T>(
    path: string,
    params: Record<string, string>,
    options: RequestInit = {}
  ): Promise<T | undefined> {
    const urlParams = new URLSearchParams(params).toString();
    const mergedPath = `${path}${urlParams ? '?' + urlParams : ''}`;

    return await this.request('delete', mergedPath, options);
  }

  public async request<T>(
    method: string,
    path: string,
    options: RequestInit = {}
  ): Promise<T | undefined> {
    const controller = new AbortController();
    const timeout = setTimeout(() => {
      controller.abort();
    }, HTTP_TIMEOUT);

    const url = `${getHttpServerHost()}${path}`;
    const request = await this.buildRequest(method, options, controller);
    Logger.debug(`[HttpClient] HTTPS Request: ${method} ${url} ${JSON.stringify(request)}`);

    return new Promise((resolve, reject) => {
      fetch(url, request)
        .then(response => {
          if (!response.ok) {
            throw new HttpError(response.status, response.statusText);
          }

          if (response.headers.get('Content-Type')?.includes('application/json')) {
            return response.json();
          }

          return response.text();
        })
        .then(body => {
          if (typeof body === 'string') {
            resolve(body as T);
          } else {
            const httpResp = body as HttpResponse<T>;

            if (httpResp.code || httpResp.optResult) {
              throw new HttpError(
                HttpStatusCode.OK,
                httpResp.message || httpResp.msg,
                httpResp.code || httpResp.optResult
              );
            }

            if (httpResp.data !== undefined) {
              if (url.includes(TEMPLATES_PATH)) {
                resolve(httpResp as T);
              } else {
                resolve(httpResp.data);
              }
            } else {
              resolve(httpResp as T);
            }
          }
        })
        .catch(error => {
          if (error.name === 'AbortError') {
            Logger.error('[HttpClient] Request timeout or aborted');
          } else {
            Logger.error(`[HttpClient] Request error: ${error.status}, ${error.message}`);
          }

          resolve(error);
          this.responseHandler?.handleResponseError(error);
        })
        .finally(() => {
          clearTimeout(timeout);
        });
    });
  }

  private async buildRequest(method: string, options: RequestInit, controller: AbortController) {
    const userInfo = await getCurrentUser();

    let preHeaders: { userId?: string, sessionId?: string } = {
      userId: userInfo.userId || '',
      sessionId: userInfo.sessionId || '',
    }
    if (this.serviceType) {
      delete preHeaders.sessionId;
    }

    const headers = new Headers({
      ...preHeaders,
      ...(options.headers || {}),
    });

    if (headers.get('Content-Type')?.includes('application/json') && options.body) {
      options.body = JSON.stringify(options.body);
    }

    const { agent, rejectUnauthorized } = getHttpProxy();

    return {
      method: method,
      headers: headers,
      signal: controller.signal,
      body: options.body,
      agent,
      rejectUnauthorized,
    };
  }

}
