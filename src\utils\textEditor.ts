import { window, TextDocument, workspace, Uri, Position, Range } from 'vscode';
import languages from '../common/languages';
import { MAX_PROMPT_SIZE } from '../common/config';
import { getCodeAIConfig } from '../common/globalContext';
import { getEndOfLine } from './common';
import * as vscode from 'vscode';
import * as path from 'path';
import { Logger } from './logger';
import { generateUUID } from './common';

type KnownLanguageType = keyof typeof languages;

/**
 * 获取tab占位数
 * @returns
 */
export function getTabSize(): number {
  const tabSize = window.activeTextEditor?.options.tabSize;
  if (typeof tabSize !== 'number') {
    return 4;
  }
  return tabSize;
}

/**
 * 获取工程目录下文件名及后缀
 * @param document
 * @returns
 */
export function getFileNameWithExtension(document: TextDocument): string {
  const { languageId, uri } = document;

  if (!document.isUntitled) {
    return formatFileName(uri);
  }

  const extension = getLanguageFileExtension(languageId);

  if (extension) {
    return formatFileName(uri).concat(extension);
  }

  return formatFileName(uri);
}

/**
 * 截取相对工程目录路径，并转换文件分隔符
 * @param fileName
 * @returns
 */
function formatFileName(uri: Uri): string {
  const path = uri.path;
  const folder = workspace.getWorkspaceFolder(uri);

  if (folder) {
    const index = path.indexOf(folder.name);

    if (index > -1) {
      return path.slice(index - 1);
    }
  }

  return path;
}

/**
 * 获取编辑语言文件后缀
 * @param languageId
 * @returns
 */
function getLanguageFileExtension(languageId: string): string | undefined {
  return languages[languageId as KnownLanguageType];
}

/**
 * 获取当前文档在光标前后字符
 * @param document
 * @param position
 */
export function getDocumentPrefixAndSuffix(document: TextDocument, position: Position, snippetsCharacterLimit?: string) {
  const offset = document.offsetAt(position);
  const total = document.getText().length;
  const { beforeStartOffset, afterEndOffset } = getStartEndOffset(total, offset, snippetsCharacterLimit!);
  const beforeStart = document.positionAt(beforeStartOffset);
  const afterEnd = document.positionAt(afterEndOffset);

  return {
    prefix: document.getText(new Range(beforeStart, position)),
    suffix: document.getText(new Range(position, afterEnd)),
  };
}

/**
 * 获取prefix，suffix字符数量
 * @param max
 * @param offset
 * @returns
 */
function getStartEndOffset(max: number, offset: number, snippetsCharacterLimit: string) {
  let beforeStartOffset = 0;
  let afterEndOffset = max;

  const inputCharLimit = getCodeAIConfig()?.InputCharacterLimit;
  const maxPromptLimit = (inputCharLimit ? parseInt(inputCharLimit) : MAX_PROMPT_SIZE) - parseInt(snippetsCharacterLimit);
  const maxSuffix = maxPromptLimit / 8;
  const maxPrefix = maxSuffix * 7;

  if (max > maxPromptLimit) {
    beforeStartOffset = offset - maxPrefix;
    let interval = -beforeStartOffset;
    if (interval > 0) {
      beforeStartOffset = 0;
      afterEndOffset = offset + maxSuffix + interval;
    } else {
      afterEndOffset = offset + maxSuffix;
      interval = afterEndOffset - max;

      if (interval > 0) {
        afterEndOffset = max;
        beforeStartOffset = offset - maxPrefix - interval;
        if (beforeStartOffset < 0) {
          beforeStartOffset = 0;
        }
      }
    }
  }

  return {
    beforeStartOffset,
    afterEndOffset,
  };
}

/**
 * 提取markdown第一段代码块内容
 * @param text
 * @returns
 */
export function extractFirstCodeBlock(text = ''): string {
  let extractText = text;

  const matches = text.match(/```/g);
  if (!matches) {
    extractText = '';
  } else if (matches.length === 1) {
    extractText += '```';
  }

  const codeMatches = extractText.match(/```(.*?)```/s);
  extractText = codeMatches?.[1] || '';

  // 使用换行符分割代码块，并移除第一行（语言标识）
  const endOfLine = getEndOfLine(extractText);
  if (endOfLine) {
    extractText = removeFirstLine(extractText, endOfLine);
  }

  return extractText;
}

/**
 * 去掉首行代码块内容，即语言标识
 * @param text
 * @param separator
 * @returns
 */
function removeFirstLine(text = '', separator: string) {
  const extractArr = text.split(separator);

  if (extractArr.length > 1) {
    extractArr.splice(0, 1);
    return extractArr.join(separator);
  }

  return '';
}

export function getCurrentFileEOL() {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return;
  }
  const document = editor.document;
  return document.eol === vscode.EndOfLine.LF ? 'LF' : 'CRLF';
}

export function getProjectInfo(windowId?: string) {
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (!workspaceFolders || workspaceFolders.length === 0) {
    Logger.debug(`[textEditor] getProjectInfo no workspaceFolder`);
    return {
      projectPath: '',
      projectId: '',
      windowId: windowId || generateUUID(),
    };
  }

  // 获取第一个工作区文件夹
  const workspaceFolder = workspaceFolders[0];

  return {
    // 获取项目绝对路径
    projectPath: workspaceFolder.uri.fsPath,
    // 获取项目名称（文件夹名）
    projectId: path.basename(workspaceFolder.uri.fsPath),
    windowId: windowId || generateUUID(),
  };
}
export async function getFileTextByPath(path: string, startLine?: number, endLine?: number) {
  try {
    // 打开文件并获取 TextDocument 对象
    const document = await vscode.workspace.openTextDocument(vscode.Uri.file(path));
    // 获取文件内容
    let text = '';
    if (startLine && endLine) {
      const range = new vscode.Range(
        new vscode.Position(startLine, 0),
        new vscode.Position(endLine, document.lineAt(endLine).text.length)
      );
      text = document.getText(range);
    } else {
      text = document.getText();
    }
    return text;
  } catch (error) {
    Logger.error(`[CodeAIRequestSender] error: ${error}`);
    return undefined;
  }
}


