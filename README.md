### SecIdea

<!-- Plugin description start -->

### 简介

<br/>
海云智码是一款用于协助开发者提高研发效能和代码安全及规范的智能编码工具。海云智码将SAST（静态应用程序安全测试）和SCA（软件成分分析）技术与人工智能大语言模型进行深度融合，帮助开发者更高效便捷地开发出安全可靠的代码。
<br/>

**研发效能**

- **生成建议代码：** 编码时短暂停顿，海云智码会生成建议代码。编写注释描述想开发的功能，海云智码可以根据注释内容补全代码帮助用户快速开发。
- **AI智能问答：** 通过自然语言向语言模型提问代码开发或代码安全的相关问题。问答时可通过'@'引用当前代码项目的文件、函数或引入外部文档，进行更精准的提问，回答的内容也会更符合开发需求。
- **函数注释和行间注释：** 对方法、函数或类生成代码注释，可识别代码中的复杂逻辑逐行添加注释。
- **单元测试：** 对函数或方法一键生成单元测试代码。
- **代码解释：** 一键生成对函数或方法代码逻辑和作用的解释。
- **调优建议：** 选择一个函数或方法，提供代码优化建议。

**代码安全及规范**

- **代码扫描：** 检测代码中的代码安全和代码规范问题，同时识别代码项目中的开源组件漏洞。
- **误报判断：** 检测时AI自动对缺陷进行误报判断，被判断为误报的缺陷会进行标记。
- **漏洞解释：** 根据实际代码缺陷的上下文内容生成AI缺陷解释，详细解释漏洞的形成原因。
- **漏洞修复：** 可对检测到的代码漏洞一键生成修复方案，供开发者做修复参考。
  <br/>

兼容IDE包括：JetBrains系列（包括IntelliJ IDEA，Android Studio，CLion，GoLand，PhpStorm，PyCharm，WebStorm）和Visual Studio Code。

支持Java、C/C++、Kotlin、Go、C#、Python、PHP、Ruby等主流语言。

<br/>
<!-- Plugin description end -->