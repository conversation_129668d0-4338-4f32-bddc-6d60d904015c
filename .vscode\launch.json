// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Extension",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--enable-proposed-api Secidea.Secidea-c10"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}",
			"trace": true
		},
		{
			"name": "Extension Tests",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionTestsPath=${workspaceFolder}/out/test/suite/index"
			],
			"outFiles": [
				"${workspaceFolder}/out/**/*.js",
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "tasks: watch-tests"
		}
	]
}