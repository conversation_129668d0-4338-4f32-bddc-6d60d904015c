import * as vscode from 'vscode';
import { SrdCommand } from '../common/constants';

// 定义一个代码动作提供者类
export default class ExceptionCodeAcitonProvider implements vscode.CodeActionProvider {

  public provideCodeActions(document: vscode.TextDocument, range: vscode.Range | vscode.Selection, context: vscode.CodeActionContext, token: vscode.CancellationToken): vscode.ProviderResult<(vscode.Command | vscode.CodeAction)[]> {
    const codeActions: vscode.CodeAction[] = [];
    const fixActionTitle = 'CodeFree一键修复';
    let fixAction: vscode.CodeAction | null = null;
    // 遍历所有诊断信息
    for (const diagnostic of context.diagnostics) {
      if (!fixAction) { 
        fixAction = new vscode.CodeAction(fixActionTitle, vscode.CodeActionKind.QuickFix);
        fixAction.command = {
            command: SrdCommand.EXCEPTION_FIX,
            title: fixActionTitle,
            arguments: [document, diagnostic, context.diagnostics]
        };
      }
      fixAction.diagnostics?.push(diagnostic); 
        
    }
    if (fixAction) {
      codeActions.push(fixAction);
    }
    
    return codeActions;
  }
}