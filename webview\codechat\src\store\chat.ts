import { FIRST_PROMPT, MaxQuestionLength, WebViewReqCommand } from '@/constants/common';

import { defineStore } from 'pinia';
import { ActivityType, Conv, Msg } from '@/types';
import * as sender from '@/MessageSender'
import { normalizeLineEndings } from '@/utils';

export const useChatStore = defineStore({
  id: 'chat',
  state: () => {
    return {
      promptView: '', // ''， 'conversation', 'directiveTemplate
      tab: 'chat', // chat or composer 
      selectedCodeInfo: { // 选中的代码信息
        code: '',
        filePath: '',
        startLine: 0,
        endLine: 0
      } as any,
      conversations: [] as any, // 会话列表
      currentConv: {} as Partial<Conv>, // 开发问答 - 当前会话
      inputFocusing: false, // 输入框是否激活
      answering: false, // 开发问答-回答中
      answeringReqId: '',
      codeFreeLoginStatus: false, // 登录状态
      lastTemplateId: '',
      lastParentReqId: '',
      isReAskReqId: '',
      stopChatParam: {} as any,
      disabledInputBox: false,
      helpNotes: '', // help new ''
      isAskingCode: false, // 来自代码的提问
      libs: [], // 知识库列表
      config: {
        /*
         "host": "https://dev.srdcloud.cn",
          "ClientUrlSubPath": "/smartassist/plugin",
          "InputCharacterLimit": "32000",
          "SnippetsCharacterLimit": "4000",
        */
        ChatCharacterLimit: 60000,
        PrintCacheConfig: JSON.stringify({
          printInterval: 30, // 打字机打印分片时间间隔（初始值30ms）
          chunkSize: 1, // 分片大小（初始值：1）
          preCacheChunkCount: 0, // 预缓存分片个数（0）
          enableConfig: true, //是否启用打字机相应配置（初始值：是）
        }),
      } as any, // 登录时候获取的配置
      isEnd: true,
      endBy: '', // 标记由哪里停止的, 空字符串表示自然停止，stopBtn 为点按钮，createBtn 为创建会话按钮
      currentLib: { // 当前选中知识库
        id: '',
        name: '',
        lastTime: 0,
      },
      recentlyUsedFiles: [] as any[],
      fileTree: [] as any[],
      lastQuote: [] as any[],
      host: 'https://www.srdcloud.cn',
      chatAPIMap: {} as any, // 用于标记api类型消息是否可以点
      online: true,
      historyOnline: true,
      pendingList: [
        // {
        //   role: 'user',
        //   msg: '1+1的结果是多少'
        // },
        // {
        //   role: 'assistant',
        //   msg: '1+1是一个数学算式，在二进制中等于10，在n进制（n≥3）中等于2。',
        //   code: -1
        // },
        // {
        //   role: 'user',
        //   msg: '1+1的结果是多少',
        //   code: -1

        // },
        // {
        //   role: 'assistant',
        //   msg: '1+1是一个数学算式，在二进制中等于10，在n进制（n≥3）中等于2。',
        //   code: -1
        // }
      ] as any[],
      printIndex: 0,
      lastIsEndAnswerReqId: '', // isEnd之后更新
      stopPrintPositionMap: {
        // 'xxx(reqId)': 233(index),
      } as any,
      workItems: [] as any[],
      workItemError: ''
    };
  },
  actions: {
    updateConvTitle(id: string | number, title: string) {
      for (const item of this.conversations) {
        if (item.id === id) {
          item.title = title;
          break
        }
      }
    },
    toggleConversation() {
      if (this.promptView === 'conversation') {
        this.promptView = ''
      } else {
        this.promptView = 'conversation'
        sender.postMessage({
          command: WebViewReqCommand.CONVERSATION_REFRESH,
          data: {
            pageNum: 1
          }
        })
      }
    },
    toggleDirectiveTemplate() {
      if (this.promptView === 'directiveTemplate') {
        this.promptView = ''
      } else {
        this.promptView = 'directiveTemplate'
      }
    },

    getChatContext() {
      if (!this.currentConv.questions || !this.currentConv.questions.children) return {}
      const tree = JSON.parse(JSON.stringify(this.currentConv.questions))
      pruneTreeByReqId(tree, this.lastParentReqId)
      return tree

      function pruneTreeByReqId(tree: any, targetReqId: string) {
        // 如果当前节点的 reqId 等于目标值，则保留该节点及其子树
        if (tree.reqId === targetReqId && tree.role !== 'user') {
          return true;
        }

        // 递归处理当前节点的每个子节点
        if (tree.children && tree.children.length > 0) {
          // 遍历当前节点的 children 数组
          for (let i = 0; i < tree.children.length; i++) {
            const child = tree.children[i];
            if (pruneTreeByReqId(child, targetReqId)) {
              // 如果子节点应保留，则保留该子节点，并删除其他子节点
              tree.children = [child];
              return true;
            }
          }
        }

        // 如果当前节点不是目标节点或者没有子节点需要保留，则返回 false
        return false;
      }

    },

    setFeedback(reqId: string, feedback: string) {
      dfs([this.currentConv.questions])
      function dfs(children: any[]) {
        if (!children.length) return
        for (const child of children) {
          if (child.reqId === reqId && child.role !== 'user') {
            child.feedback = feedback
          }
          dfs(child.children)
        }
      }
    },
    setDialog(systemPrompt: any, subService: string) {
      this.currentConv.systemPrompt = systemPrompt
      this.currentConv.lastAnswer = ''
      this.currentConv.subService = subService
      console.log('setDialog', this.currentConv)
    },
    setLastAnswer(text: string) {
      this.currentConv.lastAnswer = text
    },
    setSubService(subService: string) {
      this.currentConv.subService = subService
    },
    /** 检查登录状态 */
    handleCheckIfLogin() {
      sender.postMessage({
        command: WebViewReqCommand.CHECK_IF_LOGIN,
      });
    },
    /** 上报采纳代码记录 */
    handleReportData(code: string) {
      if (!code) return
      code = normalizeLineEndings(code)

      console.log('handleReportData', code)
      sender.postMessage({
        command: WebViewReqCommand.DATA_REPORT,
        data: {
          activityType: ActivityType.CODE_CHAT_ACCEPTED,
          answer: code,
        },
      });
    },
    /** 上报生成代码记录 */
    handleChatGenReportData(code: string) {
      if (!code) return
      code = normalizeLineEndings(code)
      sender.postMessage({
        command: WebViewReqCommand.DATA_REPORT,
        data: {
          activityType: ActivityType.CODE_CHAT_GEN,
          answer: code,
        },
      });
    },
    // 向文件中插入代码
    handleInsertCode(code: string) {
      console.log('插入code', code)
      sender.postMessage({
        command: WebViewReqCommand.INSERT_CODE,
        data: {
          code,
        },
      });
    },
    // 执行代码
    execCode(code: string) {
      sender.postMessage({
        command: WebViewReqCommand.INVOKE_TERMINAL_CAPABILITY,
        data: {
          command: code,
          reqType: 'executecommand'
        }
      })
    },
    showDiff(param: any) {
      console.log('showDiff', param)
      sender.postMessage({
        command: "view-diff",
        data: {
          filePath: param.filePath, // 绝对路径
          originalContent: param.originalContent, // 提问代码块内容
          generatedContent: param.generatedContent, // 模型生成内容
          startLine: param.startLine, // 非必填
          endLine: param.endLine, // 非必填
        }
      })
    },
    // 新建文件（插入单元测试内容）
    handleInsertUittest(code: string) {
      console.log('新建文件', code)
      sender.postMessage({
        command: WebViewReqCommand.INSERT_UNITTEST,
        data: {
          code,
        },
      });
    },
    // 获取选中的代码片段
    handleRetriveCodeSelection() {
      sender.postMessage({
        command: WebViewReqCommand.RETRIVE_CODE_SELECTION,
      });
    },
    // 编辑会话标题
    handleEditConv(id: string, title: string) {
      sender.postMessage({
        command: WebViewReqCommand.CONVERSATION_EDIT_TITLE,
        data: {
          dialogId: id,
          title,
        },
      });
      console.log('handleEditConv', id, title)

    },
    // 删除会话
    handleDeleteConv(id: string) {
      console.log('删除', id);
      sender.postMessage({
        command: WebViewReqCommand.CONVERSATION_REMOVE,
        data: {
          dialogId: id,
        },
      });
    },
    // 取消回答
    handleCancelChatRequest() {
      if (!this.answering) return

      const system = (() => {
        if (!this.currentConv.systemPrompt)
          return FIRST_PROMPT
        return JSON.stringify(this.currentConv.systemPrompt)
      })()
      const stopParams = {
        dialogId: this.currentConv.id,
        subService: (this.currentConv as any).subService,
        system,
        answer: ((this.currentConv as any).lastAnswer || '').slice(0, this.stopPrintPositionMap[this.lastParentReqId] || 1000000),
        templateId: this.lastTemplateId,
        // files: 
        ...this.stopChatParam
      }
      if (this.currentLib.id !== '') {
        stopParams.kbId = +this.currentLib.id
      }
      if (this.lastQuote.length) {
        stopParams.quote = JSON.parse(JSON.stringify(this.lastQuote))
      }
      console.log('STOP_CHAT_REQUEST', stopParams);
      sender.postMessage({
        command: WebViewReqCommand.STOP_CHAT_REQUEST,
        data: {
          ...stopParams
        }
      });

    },
    // 登录
    handleSubmitLogin() {
      sender.postMessage({
        command: WebViewReqCommand.LOGIN,
      });
      console.log('点击按钮发起登录', {
        command: WebViewReqCommand.LOGIN,
      })
    },
    /** 开发问答 - 设置当前会话 */
    handleSetCurrentConv(currentConv: Partial<Conv>) {
      this.currentConv = currentConv;
    },
    openFile(file: any) {
      sender.postMessage({
        command: WebViewReqCommand.OPEN_TEXT_DOCUMENT,
        data: {
          filePath: file.path,
        }
      })
    }
  },
  getters: {
    codeSizeLimit(state) {
      return state.config.ChatCharacterLimit - MaxQuestionLength
    },
    PrintCacheConfig(state) {
      return JSON.parse(state.config.PrintCacheConfig)
    }
  }

});
