<template>
  <div class="c-tab-list">
    <div v-for="(item) in list" :key="item.value" class="c-tab-item" :class="{ actived: item.value === current }"
      @click="onClickItem(item)">
      {{ item.label }}
    </div>
  </div>

</template>
<script setup lang="ts">
import { computed, reactive } from 'vue';
interface Tab {
  label: string,
  value: string
}

const emit = defineEmits(['change']);

const props = defineProps<{ list: Tab[], current?: string }>();

function onClickItem(item: Tab) {
  emit('change', item.value)
}

</script>

<style lang="scss">
.c-tab-list {
  display: flex;
  border-bottom: 1px solid var(--vscode-input-border);
  margin-bottom: 16px;

  .c-tab-item {
    margin-right: 10px;
    box-sizing: border-box;
    padding: 10px 0;
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }

    &.actived {
      color: var(--vscode-textLink-foreground);
      font-weight: bold;
      border-bottom: 2px solid var(--vscode-textLink-foreground);
    }
  }
}
</style>