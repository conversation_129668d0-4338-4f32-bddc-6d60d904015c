import * as fs from 'fs';
import * as path from 'path';
import * as lockfile from 'proper-lockfile';
import { Logger } from '../../utils/logger';
import * as os from 'os';

// 配置参数
interface FileLockOptions {
  maxWaitSeconds?: number;       // 最大等待时间（默认120秒）
  lockExpireMinutes?: number;    // 锁过期时间（默认30分钟）
  retryIntervalMs?: number;      // 重试间隔（默认500ms）
  taskTimeoutSeconds?: number;   // 任务超时时间（默认0表示无超时）
}

interface LockMeta {
  pid: number;
  expires: number;
  acquiredAt: number;
}

export class FileLockUtil {
  // 跟踪进程中的所有活动锁
  private static activeLocks = new Map<string, () => Promise<void>>();
  private static isExitHooksRegistered = false;

  private static DEFAULT_OPTIONS: Required<FileLockOptions> = {
    maxWaitSeconds: 120,
    lockExpireMinutes: 30,
    retryIntervalMs: 500,
    taskTimeoutSeconds: 0  // 默认无超时
  };

  // 在类加载时注册一次退出钩子
  static {
    FileLockUtil.registerExitHooks();
  }

  /**
   * 执行带文件锁的任务（有返回值）
   */
  static async execute<T>(
    lockFilePath: string,
    task: () => Promise<T>,
    options?: FileLockOptions
  ): Promise<T> {
    const mergedOptions = { ...this.DEFAULT_OPTIONS, ...options };

    let release: (() => Promise<void>) | null = null;
    let timeoutId: NodeJS.Timeout | null = null;

    try {
      // 确保锁目录存在
      await this.ensureLockDir(lockFilePath);

      // 原子性确保锁文件存在
      await this.ensureLockFile(lockFilePath);

      const startTime = Date.now();
      const maxWaitMs = mergedOptions.maxWaitSeconds * 1000;

      while (Date.now() - startTime < maxWaitMs) {
        try {
          // 尝试获取系统级文件锁
          release = await lockfile.lock(lockFilePath, {
            retries: {
              retries: 3,  // 减少单次尝试的重试次数，而在外层循环中控制总尝试时间
              factor: 1.5,
              minTimeout: mergedOptions.retryIntervalMs,
              maxTimeout: mergedOptions.retryIntervalMs * 3
            },
            stale: mergedOptions.lockExpireMinutes * 60 * 1000,
            onCompromised: (err) => {
              Logger.warn(`[FileLockUtil] [PID:${process.pid}] 锁可能已被破坏: ${lockFilePath}, ${err}`);
            }
          });

          // 全局跟踪锁
          FileLockUtil.activeLocks.set(lockFilePath, release);

          // 写入锁元信息（进程ID+获取时间+过期时间）
          await this.writeLockMeta(lockFilePath, mergedOptions.lockExpireMinutes);

          // 创建任务超时保护
          let timeoutPromise: Promise<T> | null = null;
          if (mergedOptions.taskTimeoutSeconds > 0) {
            timeoutPromise = new Promise<T>((_, reject) => {
              timeoutId = setTimeout(() => {
                reject(new Error(`任务执行超时(${mergedOptions.taskTimeoutSeconds}秒): ${lockFilePath}`));
              }, mergedOptions.taskTimeoutSeconds * 1000);
            });
          }

          // 执行任务（带可选的超时）
          const startTaskTime = Date.now();

          const result = await (timeoutPromise
            ? Promise.race([task(), timeoutPromise])
            : task());

          const taskDuration = Date.now() - startTaskTime;
          

          return result;
        } catch (err) {
          const error = err as Error & { code?: string };

          if (error.code === 'ELOCKED') {

            // 尝试清理过期锁
            const cleaned = await this.cleanStaleLock(lockFilePath, mergedOptions.lockExpireMinutes);
            if (cleaned) {
              continue;
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, mergedOptions.retryIntervalMs));
            continue;
          }

          Logger.error(`[FileLockUtil] [PID:${process.pid}] 锁错误: ${error.message}`);
          throw error;
        }
      }

      Logger.error(`[FileLockUtil] [PID:${process.pid}] 等待锁超时: ${lockFilePath}`);
      throw new Error(`等待${mergedOptions.maxWaitSeconds}秒后无法获取锁: ${lockFilePath}`);
    } finally {
      // 清理超时定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // 清理锁资源
      if (release) {

        // 从活动锁中移除
        FileLockUtil.activeLocks.delete(lockFilePath);

        try {
          await release();
          await this.removeLockMeta(lockFilePath);
        } catch (err) {
          const error = err as Error;
          Logger.warn(`[FileLockUtil] [PID:${process.pid}] 锁释放警告: ${error.message}`);
          // 不再抛出异常，防止覆盖任务中的错误
        }
      }
    }
  }

  /**
   * 执行无返回值的任务
   */
  static async executeVoid(
    lockFilePath: string,
    task: () => Promise<void>,
    options?: FileLockOptions
  ): Promise<void> {
    await this.execute(lockFilePath, task, options);
  }

  /**
   * 释放所有当前进程持有的锁 - 从deactivate()调用
   */
  static async releaseAllLocks(): Promise<void> {
    const lockCount = FileLockUtil.activeLocks.size;
    if (lockCount === 0) {
      return;
    }

   

    const releasePromises: Array<Promise<void>> = [];
    const lockPaths: string[] = [];

    for (const [lockPath, releaseFn] of FileLockUtil.activeLocks.entries()) {
      lockPaths.push(lockPath);

      const releasePromise = (async () => {
        try {
          await FileLockUtil.removeLockMeta(lockPath);
        } catch (err) {
          const error = err as Error;
          Logger.warn(`[FileLockUtil] [PID:${process.pid}] 释放锁 ${lockPath} 出错: ${error.message}`);
          // 继续处理其他锁，不抛出异常
        }
      })();

      releasePromises.push(releasePromise);
    }

    // 等待所有释放操作完成后再清空映射
    await Promise.allSettled(releasePromises);

    // 清理已经处理过的锁
    for (const lockPath of lockPaths) {
      FileLockUtil.activeLocks.delete(lockPath);
    }
  }

  //============== 私有方法 ==============//

  /**
   * 确保锁目录存在
   */
  private static async ensureLockDir(lockFilePath: string): Promise<void> {
    const dir = path.dirname(lockFilePath);
    try {
      await fs.promises.mkdir(dir, { recursive: true, mode: 0o755 });
    } catch (err) {
      const error = err as NodeJS.ErrnoException;
      // 如果目录已存在，忽略错误
      if (error.code !== 'EEXIST') {
        Logger.error(`[FileLockUtil] [PID:${process.pid}] 创建锁目录失败: ${dir}, ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * 确保锁文件存在（原子操作）
   */
  private static async ensureLockFile(lockFilePath: string): Promise<void> {
    try {
      // 以 wx 标志打开文件，如果文件不存在则创建，如果存在则失败
      const fileHandle = await fs.promises.open(lockFilePath, 'wx').catch((err: NodeJS.ErrnoException) => {
        // 如果文件已存在，直接返回
        if (err.code === 'EEXIST') {
          return null;
        }
        throw err;
      });

      // 如果成功创建了文件，关闭它
      if (fileHandle) {
        await fileHandle.close();
      }
    } catch (err) {
      const error = err as Error;
      throw error;
    }
  }

  /**
   * 写入锁元数据
   */
  private static async writeLockMeta(
    lockFilePath: string,
    expireMinutes: number
  ): Promise<void> {
    try {
      const now = Date.now();
      const meta: LockMeta = {
        pid: process.pid,
        acquiredAt: now,
        expires: now + expireMinutes * 60 * 1000
      };

      const metaJson = JSON.stringify(meta, null, 2);
      await fs.promises.writeFile(lockFilePath + '.meta', metaJson, 'utf8');
    } catch (err) {
      const error = err as Error;
      Logger.warn(`[FileLockUtil] [PID:${process.pid}] 写入锁元数据失败: ${lockFilePath}.meta, ${error.message}`);
      // 继续执行，不因元数据写入失败而中断主流程
    }
  }

  /**
   * 移除锁元数据
   */
  private static async removeLockMeta(lockFilePath: string): Promise<void> {
    try {
      await fs.promises.unlink(lockFilePath + '.meta');
    } catch (err) {
      const error = err as NodeJS.ErrnoException;
      // 如果文件不存在，忽略错误
      if (error.code !== 'ENOENT') {
        Logger.warn(`[FileLockUtil] [PID:${process.pid}] 移除锁元数据失败: ${lockFilePath}.meta, ${error.message}`);
      }
    }
  }

  /**
   * 清理过期锁，返回是否成功清理
   */
  private static async cleanStaleLock(
    lockFilePath: string,
    expireMinutes: number
  ): Promise<boolean> {
    try {
      const metaPath = lockFilePath + '.meta';

      try {
        // 首先尝试读取元数据文件内容
        const metaContent = await fs.promises.readFile(metaPath, 'utf8');
        const meta = JSON.parse(metaContent) as LockMeta;

        // 检查是否过期
        if (Date.now() > meta.expires) {

          // 尝试解锁
          await this.forceUnlock(lockFilePath);
          await this.removeLockMeta(lockFilePath);

          return true;
        }
      } catch (readErr) {
        const err = readErr as NodeJS.ErrnoException;

        // 如果元数据文件不存在，检查锁文件的修改时间
        if (err.code === 'ENOENT' || err instanceof SyntaxError) {
          try {
            const stats = await fs.promises.stat(lockFilePath + '.lock');
            if (Date.now() - stats.mtimeMs > expireMinutes * 60 * 1000) {

              // 尝试解锁
              await this.forceUnlock(lockFilePath);
              return true;
            }
          } catch (statErr) {
            // 忽略锁文件不存在的错误
            const statError = statErr as NodeJS.ErrnoException;
            if (statError.code !== 'ENOENT') {

            }
          }
        } else {
          Logger.error(`[FileLockUtil] [PID:${process.pid}] 读取锁元数据出错: ${err.message}`);
        }
      }

      return false;
    } catch (err) {
      const error = err as Error;
      return false;
    }
  }

  /**
   * 强制解锁
   */
  private static async forceUnlock(lockFilePath: string): Promise<void> {
    try {
      await lockfile.unlock(lockFilePath);
    } catch (unlockErr) {
      try {
        // 如果解锁失败，尝试删除锁文件
        await fs.promises.unlink(lockFilePath + '.lock');
      } catch (unlinkErr) {
        const error = unlinkErr as NodeJS.ErrnoException;
        if (error.code !== 'ENOENT') {
          Logger.error(`[FileLockUtil] [PID:${process.pid}] 删除锁文件失败: ${error.message}`);
        }
      }
    }
  }

  /**
   * 注册进程退出钩子（只执行一次）
   */
  private static registerExitHooks(): void {
    if (FileLockUtil.isExitHooksRegistered) {
      return;
    }

    const cleanup = async (signal: string): Promise<void> => {
      try {
        await FileLockUtil.releaseAllLocks();
      } catch (err) {
        const error = err as Error;
        Logger.error(`[FileLockUtil] [PID:${process.pid}] 释放锁失败: ${error.message}`);
      } finally {
        // 确保进程退出
        process.exit(signal === 'uncaughtException' ? 1 : 0);
      }
    };

    // 捕获退出信号
    process.on('SIGINT', () => cleanup('SIGINT'));
    process.on('SIGTERM', () => cleanup('SIGTERM'));

    // 捕获未处理异常
    process.on('uncaughtException', async (err) => {
      Logger.error(`[FileLockUtil] [PID:${process.pid}] 未捕获的异常: ${err.stack || err.message}`);
      await cleanup('uncaughtException');
    });

    FileLockUtil.isExitHooksRegistered = true;
  }
}