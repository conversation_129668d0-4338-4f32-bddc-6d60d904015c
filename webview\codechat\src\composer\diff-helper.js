import { diffLines } from 'diff'
function ignoreTag(text) {
    var div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
export function hasDiffOf(code = '') {
    return code.includes('<<<<<<< SEARCH') && code.includes('>>>>>>> REPLACE')
}


export function highlightDiffCode(code = '') {
    const lines = code.split('\n')

    const sIndex = lines.findIndex(line => line.includes('<<<<<<< SEARCH'))
    const eIndex = lines.findIndex(line => line.includes('>>>>>>> REPLACE'))
    const dIndex = lines.findIndex(line => line.includes('======='))
    const rawLines = lines.slice(sIndex + 1, dIndex)
    const newLines = lines.slice(dIndex + 1, eIndex)
    
    let resultLines = diffLines(rawLines.join('\n'), newLines.join('\n'))
    let lineCount = 0

    resultLines = resultLines.map(item => {
        return {
            ...item,
            value: ignoreTag(item.value)
        }
    }).map(item => {
        if (item.count === 1) {
            lineCount += 1

            if (item.added) return `<span class="composer-diff-line composer-diff-line-addition">${item.value}</span>`
            else if (item.removed) return `<span class="composer-diff-line  composer-diff-line-deletion">${item.value}</span>`
            else return `<span class="composer-diff-line">${item.value}</span>`
        } else {
            let splitLines = item.value.split('\n')
            if (item.added) splitLines = splitLines.map(value => `<span class="composer-diff-line composer-diff-line-addition">${value}</span>`)
            else if (item.removed) splitLines = splitLines.map(value => `<span class="composer-diff-line composer-diff-line-deletion">${value}</span>`)
            else splitLines = splitLines.map(value => `<span class="composer-diff-line">${value}</span>`)
            lineCount += splitLines.length

            return splitLines.join('')
        }
    })

    return {
        value: resultLines.join(''),
        lineCount
    }
}
