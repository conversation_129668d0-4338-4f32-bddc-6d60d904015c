<template>
  <div class="diff-file-list-root">
    <a-collapse>
      <a-collapse-item key="1">
        <template #header>
          <div style="display: flex; align-items: center;">
            修改文件列表
          </div>

        </template>
        <template #extra>
          <template v-if="isShowAcceptAllBtn">
            <span class="diff-button diff-right-spacing" @click.stop="onAcceptAll">
              接受全部
            </span>
            <span class="diff-button" @click.stop="onRefuseAll">
              拒绝全部
            </span>

          </template>
          <!-- 撤回全部 -->
          <span v-else class="diff-button" @click.stop="onWithdrawAll">
            撤回全部
          </span>
        </template>
        <div class="file-list">
          <div @click="onDiff(item)" v-for="(item, index) in composer.diffList" :key="item.path + index"
            class="file-list__item">
            <span style="display: flex;align-items: center;">
              {{ getFileNameFromPath(item.path) }}
              <!-- 未操作 -->
              <svg-icon class="diff-status-icon" v-if="!isOperation(item)" name="check-undone"></svg-icon>
              <!-- 接受 -->
              <svg-icon class="diff-status-icon" v-else-if="isAccepted(item)" name="check-done"></svg-icon>
              <!-- 部分接受 -->
              <svg-icon class="diff-status-icon" v-else-if="item.status === statusConfig.partial_accepted"
                name="check-partial-acceptance"></svg-icon>
              <!-- 拒绝 -->
              <svg-icon class="diff-status-icon" v-else name="check-refuse" color="#F25858"></svg-icon>
            </span>
            <div>
              <template v-if="!isOperation(item)">
                <span class="diff-button diff-right-spacing" @click.stop="onAccept(item)">
                  接受变更
                </span>
                <span class="diff-button" @click.stop="onRefuse(item)">
                  拒绝变更
                </span>
              </template>

              <span v-else class="diff-button" @click.stop="onWithdraw(item)">
                撤回
              </span>
            </div>
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
  </div>
</template>
<script setup>
import { computed, onMounted } from 'vue'
import { getFileNameFromPath } from '@/utils/index'
import { diffLines } from 'diff'
import useModifyFile from '@/hooks/useModifyFile'

const { isAccepted, isOperation, composer, statusConfig, isShowAcceptAllBtn,
  onAccept, onRefuse, onWithdraw, onWithdrawAll, onAcceptAll, onRefuseAll } = useModifyFile()

function onDiff(item) {
  composer.showDiff(item.path, item.afterContent)
}

onMounted(() => {
  setTimeout(() => {
    composer.diffList.forEach(item => onDiff(item))
    console.log(composer.diffList, "composer.diffListcomposer.diffList");

  })
})
</script>
<style lang="scss">
.diff-file-list-root {
  background-color: var(--color-bg-2);

  .diff-status-icon {
    margin-left: 5px;
  }

  .diff-button {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }

  .diff-text {
    color: rgb(var(--primary-6));
    cursor: auto;
  }

  .diff-right-spacing {
    margin-right: 10px;
  }

  .arco-collapse-item-content {
    padding-left: 0px !important;
    padding-right: 0px !important;

  }

  .arco-collapse-item-content-box {
    background-color: var(--color-bg-2);
  }

  .file-list {
    .file-list__item {
      padding: 0 13px;
      height: 36px;
      display: flex;
      background-color: var(--color-bg-2);
      cursor: pointer;

      justify-content: space-between;
      align-items: center;

      &:hover {
        background-color: var(--vscode-textCodeBlock-background);

      }
    }
  }

}
</style>