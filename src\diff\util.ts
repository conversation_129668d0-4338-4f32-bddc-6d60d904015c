import * as vscode from 'vscode';

let _workspaceDirectories: string[] | undefined;

/**
 * 获取工作区的根目录路径列表（仅在第一次时初始化缓存）
 */
function getWorkspaceDirectories(): string[] {
  if (_workspaceDirectories === undefined) {
    _workspaceDirectories =
      vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath) || [];
  }
  return _workspaceDirectories;
}

/**
 * 对外暴露的获取工作区目录函数（可直接导出用）
 */
export function getWorkspaceDirs(): string[] {
  return getWorkspaceDirectories();
}

export async function getProjectPathWithRetry(
  maxRetries = 3,
  timeout = 100
) {
  let retryCount = 0;

  while (retryCount < maxRetries) {
    try {
      const dirs = await getWorkspaceDirs();
      if (dirs !== undefined && dirs.length > 0 && typeof dirs[0] === 'string') {
        // Normalize the path to use forward slashes
        const projectPath = dirs[0]
          // Replace all backslashes with forward slashes
          .replace(/\\/g, '/')
          // Remove trailing slash if exists
          .replace(/\/+$/, '');

        return projectPath;
      }
    } catch (error) {
      retryCount++;
      if (retryCount === maxRetries) {
        // console.error('Failed to get workspace dirs after', maxRetries, 'attempts:', error);
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, timeout));
    }
  }
}