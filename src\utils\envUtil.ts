import { getAddress } from '../settings/settingsGetter';
/**
 * 服务器URL工具类
 * 根据isSec环境变量决定使用哪个服务器地址
 */

const HTTP_SERVER_HOST = process.env.HTTP_SERVER_HOST;
const WS_SERVER_HOST = process.env.WS_SERVER_HOST;

/**
 * 获取HTTP服务器地址
 * @returns HTTP服务器地址
 */
export function getHttpServerHost(): string | undefined {
  // 如果isSec未定义或为true，使用安全环境地址
  return process.env.ISSEC !== 'false' ? getAddress() : HTTP_SERVER_HOST;
}

/**
 * 获取WebSocket服务器地址
 * @returns WebSocket服务器地址
 */
export function getWsServerHost(): string | undefined {
  // 如果isSec未定义或为true，使用安全环境地址
  return process.env.ISSEC !== 'false' ? getAddress() : WS_SERVER_HOST;
} 