import HttpClient from '../commclient/httpClient';
import { RtnCode, UploadType } from '../common/constants';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { FileResp, UploadResult } from './types/file';
import { File, FormData, Blob as NodeBlob } from 'node-fetch';
import { DOWNLOAD_FILE_PATH, UPLOAD_FILE_PATH } from '../common/config';
import { HttpError } from '../commclient/types/http';
import { Logger } from '../utils/logger';
import { Uri, workspace } from 'vscode';
import { getHttpServerHost } from '../utils/envUtil';

/**
 * 文件服务
 */
export default class FileService implements IAnswerHandler {
  private type: UploadType = UploadType.HTTP;

  private httpClient: HttpClient | undefined;

  /**
   * 通过哪种方式上传
   * @param type
   */
  public constructor(type?: UploadType) {
    if (type) {
      this.type = type;
    }

    if (this.type === UploadType.HTTP) {
      this.httpClient = new HttpClient();
    }
  }

  /**
   *
   * @param localPath
   */
  public async uploadFile(localPath: string): Promise<UploadResult> {
    const uri = Uri.file(localPath);
    const buffer = await workspace.fs.readFile(uri);
    const blob = new NodeBlob([buffer], { type: this.getMimeType(localPath) });
    const fileName = uri.path.slice(uri.path.lastIndexOf('/') + 1);
    const file = new File([blob], fileName);

    switch (this.type) {
      case UploadType.HTTP:
        return await this.uploadFileByHttp(file);
      default:
        return {
          code: RtnCode.UPLOAD_FAIL,
        };
    }
  }

  /**
   * 通过http上传
   */
  public async uploadFileByHttp(file: any): Promise<UploadResult> {
    if (!this.httpClient) {
      return {
        code: RtnCode.UPLOAD_FAIL,
      };
    }

    const formData = new FormData();
    formData.append('upfile', file);

    const result = await this.httpClient.requestByPost<FileResp>(UPLOAD_FILE_PATH, formData, {});

    if (!result || result instanceof HttpError || !result.url) {
      Logger.error(`[FileService] uploadFileByHttp failed, ${JSON.stringify(result)}`);

      return {
        code: RtnCode.UPLOAD_FAIL,
      };
    }

    Logger.debug(`[FileService] result: ${JSON.stringify(result)}`);

    return {
      code: RtnCode.SUCCESS,
      path: `${getHttpServerHost()}${DOWNLOAD_FILE_PATH}${result.url}`,
    };
  }

  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    throw new Error('Method not implemented.');
  }

  public getReqId(): string {
    throw new Error('Method not implemented.');
  }

  /**
   * 通过文件后缀获取mimetype，可参考https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types
   * @param path
   * @returns
   */
  private getMimeType(path: string) {
    const format = path.slice(path.lastIndexOf('.') + 1);

    switch (format) {
      case 'png':
      case 'webp':
      case 'apng':
      case 'avif':
      case 'gif':
        return `image/${format}`;
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'ico':
        return 'image/vnd.microsoft.icon';
      case 'svg':
        return 'image/svg+xml';
      case 'tif':
      case 'tiff':
        return 'image/tiff';
      default:
        return undefined;
    }
  }
}
