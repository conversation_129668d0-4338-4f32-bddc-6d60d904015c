declare module '*.vue' {
  import type { ComponentOptions } from 'vue';
  const Component: ComponentOptions;
  export default Component;
}

declare module '@/store/composer' {
  // 这里定义模块的导出类型
  const useComposer: any
}

declare module '*.md' {
  export default string;
}

declare module '*.scss'

declare global {
  import '@arco-design/web-vue/es/components';
}
declare const acquireVsCodeApi: Function;
declare module 'markdown-it-texmath';
declare module 'katex';

declare interface Window { ideQuery: (types: any) => any; }
