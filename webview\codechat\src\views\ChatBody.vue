<template>
  <div class="chat-body" ref="chatBodyDom">
    <!-- 不处于会话中，未登录 -->

    <!-- 处于会话中，未登录 -->

    <!-- 处于会话中，已登录 -->

    <!-- 不处于会话中，已登录 -->

    <!-- 不处于会话中，未登录 -->
     
    <!-- /help指令 -->

    <LogoBubble v-if="showFistPrompt && !chatStore.helpNotes" @shortcut="shortcut" @submitMsg="submitMsg"></LogoBubble>
    <HelpBubble v-if="chatStore.helpNotes === 'help'"></HelpBubble>
    <NewBubble v-if="chatStore.helpNotes === 'new'"/>
    <MessageItem v-if="showMsgItem" :parent="tree" :children="[tree]" @onLike="onLike" @onUnlike="onUnlike"
      @onReGen="onReGen" @onSelectApi="onSelectApi" />
    <PendingList v-if="chatStore.pendingList.length" />

    <CodeLoadingBubble :silent="!chatStore.PrintCacheConfig.enableConfig" logo class="code-loading-bb" v-if="chatStore.answering && props.answeringText" ref="loadingBubble"
      :content="props.answeringText" @line-print="onPrinting" @finish-print="emit('finishPrint')">
    </CodeLoadingBubble>

    <!-- 保持在对底部 -->
    <LoginBubble v-if="showLoginTips" />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, nextTick, onMounted, watch, getCurrentInstance, onUnmounted } from 'vue';
import CodeLoadingBubble from '@/components/CodeLoadingBubble.vue';
import { ChatBodyProps } from '@/types/index';
import { WebViewReqCommand, WebviewRspCommand } from '@/constants/common';
import { useChatStore } from '@/store/chat';
import LoginBubble from '@/components/LoginBubble.vue';
import MessageItem from './MessageItem.vue'
import HelpBubble from '@/components/HelpBubble.vue';
import NewBubble from '@/components/NewBubble.vue';
import LogoBubble from '@/components/LogoBubble.vue';
import PendingList from '@/components/PendingList.vue'
import * as sender from '@/MessageSender'

const props = defineProps<ChatBodyProps>();
const emit = defineEmits(['finishPrint', 'onReGen', 'shortcut', 'submitMsg', 'onSelectApi']);
const loadingBubble = ref<null | InstanceType<typeof CodeLoadingBubble>>(null);
const chatBodyDom = ref<null | HTMLElement>(null);
// const scrollToBottomStatus = ref(true);
const willScrollTobottom = ref(true); // 是否可以自动否滚动到底部

const chatStore = useChatStore()


const curConv = computed(() => {
  return chatStore.currentConv
})

function shortcut(message: number) {
  emit('shortcut', message)
}
function submitMsg(message: string) {
  emit('submitMsg', message)
}

const tree = computed(() => {
  let tree = (curConv.value.questions)
  return tree
}) as any

const showMsgItem = computed(() => {
  return tree.value && Object.keys(tree.value).length
})

const showFistPrompt = computed(() => {
  if(chatStore.isAskingCode) return false
  if (!chatStore.currentConv.isNewConversation) return false
  if (showMsgItem.value) return false
  if (chatStore.helpNotes) return false
  const questions = chatStore.currentConv.questions
  if (!questions) return true
  if (questions.content && questions.content.length) return false
  if (chatStore.pendingList.length) return false
  return true
});

const showLoginTips = computed(() => {
  return (showMsgItem.value || chatStore.pendingList.length) && !chatStore.codeFreeLoginStatus
})
function onPrinting() {
  nextTick(() => {
    if (chatBodyDom.value) {
      scrollToBottom()
    }
  });
}


watch(() => [chatStore.lastParentReqId, showFistPrompt.value, showLoginTips.value], scrollToBottom, { immediate: true, deep: true })

function scrollToBottom() {
  nextTick(() => {
    if (chatBodyDom.value && willScrollTobottom.value) {
      chatBodyDom.value.scrollTop = chatBodyDom.value.scrollHeight;
    }
  });
}
function setWillScrollTobottom(value = true) {
  willScrollTobottom.value = value
}
// 添加滚动监听
function listenScroll() {
  chatBodyDom?.value?.addEventListener(
    'wheel',
    (e: WheelEvent) => {
      if (!chatBodyDom.value) return;
      const event: WheelEvent = e || window.event;
      if (event.deltaY < 0) {
        willScrollTobottom.value = false;
      } else if(chatBodyDom.value.scrollTop + chatBodyDom.value.getBoundingClientRect().height + 250 >= chatBodyDom.value?.scrollHeight) {
        willScrollTobottom.value = true;
      }
    },
    true
  );
}

const listener = ({ data }: any) => {

  if (data.command === WebviewRspCommand.FEEDBACK_CONVERSATION_RESPONSE) {
    console.log('FEEDBACK_CONVERSATION_RESPONSE', data)
    if (data.data && data.data.code === 0) {
      const reqId = data.data.data && data.data.data.reqId
      const feedback = data.data.data && data.data.data.feedback

      console.log('点成功了', reqId, feedback)
      chatStore.setFeedback(reqId, feedback)
    }
  }
}


onMounted(() => {
  nextTick(() => {
    listenScroll();
  });

  window.addEventListener('message', listener);

  const observer = new MutationObserver(() => {
    if(chatBodyDom.value) {
      if(chatStore.answering)
      scrollToBottom()
    }
  });
  const config = { childList: true, subtree: true };
  if(chatBodyDom.value) {
    observer.observe(chatBodyDom.value, config);
  }
})
onUnmounted(() => {
  window.removeEventListener('message', listener)
})
function onLike(item: any) {
  console.log('onLike', item)
  console.log('onLike', {
    dialogId: curConv.value.id,
    feedback: 'like',
    reqId: item.reqId,
    type: 0
  })
  sender.postMessage({
    command: WebViewReqCommand.CONVERSATION_FEEDBACK,
    data: {
      dialogId: curConv.value.id,
      feedback: 'like',
      reqId: item.reqId,
      type: 0
    }
  })

}

function onUnlike(item: any) {
  console.log('onUnlike', {
    dialogId: curConv.value.id,
    feedback: 'unlike',
    reqId: item.reqId,
    type: 0
  })
  sender.postMessage({
    command: WebViewReqCommand.CONVERSATION_FEEDBACK,
    data: {
      dialogId: curConv.value.id,
      feedback: 'unlike',
      reqId: item.reqId,
      type: 0
    }
  })

}

function onReGen(item: any) {
  console.log('onReGen', item)
  emit('onReGen', item)
}
function onSelectApi(item: any) {
  emit('onSelectApi', item)
}

defineExpose({
  scrollToBottom,
  onPrinting,
  setWillScrollTobottom
});
</script>

<style lang="scss">
.chat-body {
  height: 100%;
  overflow: auto;
}
</style>
