import {
  authentication,
  AuthenticationProvider,
  AuthenticationProviderAuthenticationSessionsChangeEvent,
  AuthenticationSession,
  Disposable,
  Event,
  EventEmitter,
  SecretStorage,
  commands,
  window,
} from 'vscode';
import { once, EventEmitter as Emitter } from 'events';
import SrdSession, { UserInfo } from './srdSession';
import {
  APP_KEY,
  APP_NAME,
  RtnCode,
  SecretStorageKey,
  SrdCommand,
  SrdContextKey,
} from '../common/constants';
import { IServiceObserver, IServiceObservable, EventType } from '../service/types/login';
import { clearAuthToken, getAuthToken, setAuthToken } from '../common/globalContext';
import { isRunInCloudIDE } from '../adapter/common';
import { Logger } from '../utils/logger';

const LOGIN_HAPPENED_EVENT = 'loginHappened';

/**
 * 请求AuthenticationProvider登录
 * @param createIfNone
 * @returns
 */
export async function getAuthenticationSession(createIfNone?: boolean) {
  return await authentication.getSession(APP_KEY, [], {
    createIfNone,
  });
}

export default class SrdAuthenticationProvider
  implements AuthenticationProvider, Disposable, IServiceObserver {
  public static readonly id: string = APP_KEY;

  public static readonly label: string = APP_NAME;

  private readonly secretStorage: SecretStorage;

  private initializedDisposable: Disposable[] = [];

  private myOnDidChangeSessions =
    new EventEmitter<AuthenticationProviderAuthenticationSessionsChangeEvent>();

  private onDidLogin = new Emitter();

  private serviceObservable: IServiceObservable | undefined;

  private previousToken: string | undefined;

  private checkUpdateTimer: NodeJS.Timeout | null = null;
  private lastCheckTime: number = 0;
  private readonly CHECK_INTERVAL = 1000; // 最小检查间隔 1 秒

  public constructor(secretStorage: SecretStorage) {
    this.secretStorage = secretStorage;
  }

  /**
   * session变更时监听事件
   */
  public get onDidChangeSessions(): Event<AuthenticationProviderAuthenticationSessionsChangeEvent> {
    return this.myOnDidChangeSessions.event;
  }

  /**
   * 设置被观察者，即LoginService
   * @param observable
   */
  public setObservable(observable: IServiceObservable): void {
    this.serviceObservable = observable;
    this.serviceObservable.registerObserver(this);
  }

  /**
   * 取消vscode注册
   */
  public dispose(): void {
    this.initializedDisposable?.forEach(disposable => disposable.dispose());
    this.serviceObservable?.unregisterObserver(this);
  }

  /**
   * 获取当前已登录用户会话
   * @returns
   */
  public async getSessions() {
    this.ensureInitialized();
    const token = await getAuthToken();

    return token ? [new SrdSession(token)] : [];
  }

  /**
   *  This function is called after `this.getSessions` is called and only when:
   * `this.getSessions` returns nothing but `createIfNone` was set to `true` in `vscode.authentication.getSessions`
   * `vscode.authentication.getSessions` was called with `forceNewSession: true`
   * The end user initiates the "silent" auth flow via the Accounts menu
   */
  public async createSession(): Promise<AuthenticationSession> {
    this.ensureInitialized();

    const previousSession = (await this.getSessions())[0];
    Logger.debug(`[createSession] previousSession: ${previousSession}`);

    this.serviceObservable?.handleEvent(EventType.LOGIN);

    const result = await this.waitForLogin();

    if (!result) {
      window.showInformationMessage(process.env.ISSEC !== 'false' ? '已取消海云安登录认证' : '已取消研发云登录认证');
      return new SrdSession();
    } else if (typeof result === 'number') {
      return new SrdSession();
    }

    // const session = new SrdSession(result);
    // await setAuthToken(session.accessToken);

    this.previousToken = await getAuthToken();

    const session = new SrdSession(result);
    await setAuthToken(session.accessToken);
    Logger.debug(`[createSession] session: ${JSON.stringify(session)}`);

    // 云IDE环境需要手动触发session更新事件，且不需要弹提示语
    if (isRunInCloudIDE()) {
      this.checkForUpdates();
    } else {
      window.showInformationMessage(process.env.ISSEC !== 'false' ? '海云安登录认证成功' : '研发云登录认证成功');
      // this.checkForUpdates();
    }

    return session;
  }

  /**
   * 点击注销时调用
   */
  public async removeSession(): Promise<void> {
    await this.serviceObservable?.handleEvent(EventType.LOGOUT);
    await this.removeTokenAndSession();

    // 在菜单栏重新出现登录提示
    getAuthenticationSession();
  }

  /**
   * 清除Session以及当前的SecretStore
   */

  public async removeTokenAndSession() {
    this.myOnDidChangeSessions.fire({
      removed: [(await this.getSessions())[0]],
      added: [],
      changed: [],
    });
    this.previousToken = await getAuthToken();
    await clearAuthToken();

    // const currentSessions = await this.getSessions();

    // // 先保存 token
    // this.previousToken = await getAuthToken();

    // // 清除 token
    // await clearAuthToken();

    // // 只有在确实有 session 时才触发事件
    // if (currentSessions && currentSessions.length > 0) {
    //   // 使用 setTimeout 避免同步事件堆积
    //   setTimeout(() => {
    //     this.myOnDidChangeSessions.fire({
    //       removed: [currentSessions[0]],
    //       added: [],
    //       changed: [],
    //     });
    //   }, 0);
    // }
  }

  /**
   * 监听登录事件
   * @param eventType
   * @param data
   */
  public async onServiceChanged(eventType: EventType, data: unknown) {
    let contextActivated = false;

    switch (eventType) {
      case EventType.LOGIN_SUCCESS:
        this.onDidLogin.emit(LOGIN_HAPPENED_EVENT, data as UserInfo);
        contextActivated = true;
        break;
      case EventType.LOGIN_CANCELED:
        await this.removeTokenAndSession();
        this.onDidLogin.emit(LOGIN_HAPPENED_EVENT, null);
        break;
      case EventType.LOGIN_FAILED:
      case EventType.LOGIN_EXPIRED:
        await this.removeTokenAndSession();
        this.onDidLogin.emit(LOGIN_HAPPENED_EVENT, data);
        commands.executeCommand(SrdCommand.SHOW_LOGIN_FAILED, data);
        getAuthenticationSession();
        break;
      default:
        break;
    }

    // 设置插件激活状态，作为快捷键触发条件，配置见package.json
    commands.executeCommand('setContext', SrdContextKey.ACTIVATED, contextActivated);
  }

  /**
   * 注册Session、SecretStore变化的监听事件
   */
  private ensureInitialized(): void {
    if (this.initializedDisposable.length === 0) {
      if (!isRunInCloudIDE()) {
        // This onDidChange event happens when the secret storage changes in _any window_ since
        // secrets are shared across all open windows.
        this.initializedDisposable.push(
          this.secretStorage.onDidChange(e => {
            if (e.key === SecretStorageKey.AUTH_TOKEN) {
              void this.checkForUpdates();
            }
          })
        );
      }

      // This fires when the user initiates a "silent" auth flow via the Accounts menu.
      this.initializedDisposable.push(
        authentication.onDidChangeSessions(e => {
          if (e.provider.id === SrdAuthenticationProvider.id) {
            void this.checkForUpdates();
          }
        })
      );
    }
  }

  /**
   * 处理session变更
   * @returns
   */
  private async checkForUpdates(): Promise<void> {
    // 防止频繁调用
    const now = Date.now();
    if (now - this.lastCheckTime < this.CHECK_INTERVAL) {
      if (this.checkUpdateTimer) {
        clearTimeout(this.checkUpdateTimer);
      }
      this.checkUpdateTimer = setTimeout(() => {
        this.checkForUpdates();
      }, this.CHECK_INTERVAL);
      return;
    }
    this.lastCheckTime = now;

    const added: AuthenticationSession[] = [];
    const removed: AuthenticationSession[] = [];
    const changed: AuthenticationSession[] = [];

    const previousToken = this.previousToken;
    const session = (await this.getSessions())[0];

    if (session?.accessToken && !previousToken) {
      added.push(session);
    } else if (!session?.accessToken && previousToken) {
      removed.push(new SrdSession(previousToken));
    } else if (session?.accessToken !== previousToken) {
      changed.push(session);
    } else {
      return;
    }

    // 使用 queueMicrotask 延迟事件触发
    queueMicrotask(() => {
      this.myOnDidChangeSessions.fire({ added, removed, changed });
    });
  }
  // private async checkForUpdates(): Promise<void> {
  //   const added: AuthenticationSession[] = [];
  //   const removed: AuthenticationSession[] = [];
  //   const changed: AuthenticationSession[] = [];

  //   // const previousToken = await getAuthToken();
  //   const previousToken = this.previousToken;
  //   const session = (await this.getSessions())[0];

  //   if (session?.accessToken && !previousToken) {
  //     added.push(session);
  //   } else if (!session?.accessToken && previousToken) {
  //     removed.push(new SrdSession(previousToken));
  //   } else if (session?.accessToken !== previousToken) {
  //     changed.push(session);
  //   } else {
  //     return;
  //   }

  //   this.myOnDidChangeSessions.fire({ added: added, removed: removed, changed: changed });
  // }

  /**
   * 等待登录流程结束
   * @returns
   */
  private async waitForLogin(): Promise<UserInfo | RtnCode> {
    return (await once(this.onDidLogin, LOGIN_HAPPENED_EVENT))[0];
  }
}
