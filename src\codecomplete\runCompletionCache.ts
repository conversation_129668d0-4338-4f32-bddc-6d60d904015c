import { getEndOfLine } from '../utils/common';
import { Logger } from '../utils/logger';
import { AutocompleteResult, ResultEntry } from './types';

export default class RunCompletionCache {
  private static cachedData: ResultEntry[] = [];

  public static isMatchCache(prefix: string) {
    const isMatch = !!this.cachedData.find(result => this.isValidResult(prefix, result));

    if (!isMatch) {
      this.clearCache();
    }

    return isMatch;
  }

  public static setItems(results: ResultEntry[]) {
    results.reverse().forEach(result => {
      const endOfLine = getEndOfLine(result.new_prefix);

      // 不存在且是自动补全的结果才缓存
      if (
        !this.cachedData.find(c => c.new_prefix === result.new_prefix) &&
        result.answer.indexOf(endOfLine) === -1
      ) {
        this.cachedData.unshift(result);
      }
    });
  }

  public static getMatchItem(prefix: string): AutocompleteResult | undefined {
    const toDelete: ResultEntry[] = this.cachedData.filter(
      result => !this.isValidResult(prefix, result)
    );
    const matchedResult = this.cachedData.find(result => this.isValidResult(prefix, result));

    this.deleteItems(toDelete);

    if (matchedResult) {
      const newAnswer = matchedResult.new_prefix.substring(prefix.length);

      Logger.info(
        `[RunCompletionCache] getMatchItem, match catched answer:${newAnswer}, cachedData.length: ${this.cachedData.length}`
      );

      return {
        old_prefix: prefix,
        results: [
          {
            new_prefix: matchedResult.new_prefix,
            answer: newAnswer,
            old_suffix: '',
            new_suffix: '',
          },
        ],
      };
    }

    return undefined;
  }

  public static deleteItems(results: ResultEntry[]) {
    results.forEach(result => {
      const index = this.cachedData.indexOf(result);
      this.cachedData.splice(index, 1);
    });
  }

  public static clearCache() {
    this.cachedData = [];
  }

  private static isValidResult(prefix: string, result: ResultEntry) {
    const endOfLine = getEndOfLine(prefix);

    return (
      result.new_prefix.startsWith(prefix) &&
      result.new_prefix.replace(prefix, '').indexOf(endOfLine) === -1
    );
  }
}
