import { QuestionType } from '../../common/constants';
import { CodeAIRequestPromptChat, CodeAIResponsePayload } from './codeAI';
import { KbQuoteParams, RelatedFile, selectedWorkItem } from '../../codechat/types'
import { ImportSnippet } from '../../codecomplete/types';

export interface IQuestionTaskEvent {
  onAnswer(reqId: string, isEnd: number, answer: string, payload?: CodeAIResponsePayload): void;
  onTaskError(reqId: string, rtnCode: number, errMsg?: string): void;
}

export interface AskQuestionResult {
  reqId?: string;
  rtnCode: number;
}

export interface AskQuestionParams {
  questionType: QuestionType;
  question?: string;
  prompts?: CodeAIRequestPromptChat[];
  fileName?: string;
  language?: string;
  prefix?: string;
  suffix?: string;
  manualType?: number;
  stopWords?: string[];
  dialogId?: string;
  questionAskType?: string;
  parentReqId?: string;
  templateId?: string;
  kbId?: number;
  quote?: KbQuoteParams[];
  importSnippets?: ImportSnippet[];
  relatedFiles?: RelatedFile[];
  selectedWorkItems?: selectedWorkItem[];
}
