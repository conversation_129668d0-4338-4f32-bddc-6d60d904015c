import {
  Disposable,
  ExtensionContext,
  TextEditorSelectionChangeEvent,
  commands,
  window,
} from 'vscode';
import { CodeSelectionEventType, ICodeSelectionHandler } from './types';
import { ChatMessageType, ChatMessageTypeDesc, SrdCommand } from '../common/constants';
import EditorUtil from './editorUtil';
import { debounce } from '../utils/common';
import * as vscode from 'vscode';

/**
 * 选中代码，触发代码解释、代码注释、代码单元测试流程管理
 */
export default class CodeSelectionManager implements Disposable {
  private context: ExtensionContext;

  private selectionHandler: ICodeSelectionHandler;

  private disposable: Disposable;

  public constructor(context: ExtensionContext, selectionHandler: ICodeSelectionHandler) {
    this.context = context;
    this.selectionHandler = selectionHandler;
    this.disposable = Disposable.from(...this.registerCommands(), this.registerSelectionChange());
  }

  public dispose() {
    this.disposable.dispose();
  }

  /**
   * 注册代码解释、代码注释、单元测试指令
   * @returns
   */
  private registerCommands(): Disposable[] {
    const disposable: Disposable[] = [];
    disposable.push(
      commands.registerCommand(SrdCommand.CODE_SELECTION_EXPLAIN, () => {
        this.sendCodeSelectionMessage(ChatMessageType.EXPLAIN);
      }),
      commands.registerCommand(SrdCommand.CODE_SELECTION_COMMENT, () => {
        this.sendCodeSelectionMessage(ChatMessageType.COMMENT);
      }),
      commands.registerCommand(SrdCommand.CODE_SELECTION_UNITTEST, () => {
        this.sendCodeSelectionMessage(ChatMessageType.UNITTEST);
      }),
      commands.registerCommand(SrdCommand.CODE_SELECTION_OPTIMIZATION, () => {
        this.sendCodeSelectionMessage(ChatMessageType.OPTIMIZATION);
      }),
      commands.registerCommand(SrdCommand.START_CHAT, () => {
        this.startFileChat(ChatMessageType.QA_RELATED_FILES);
      }),
    );

    return disposable;
  }

  /**
   * 注册代码选中变更监听事件
   * @returns
   */
  private registerSelectionChange(): Disposable {
    return window.onDidChangeTextEditorSelection(debounce(e => this.handleSelectionChange(e), 500));
  }


  /**
   * 发送代码片断给webview处理相应命令
   * @param type
   */
  private sendCodeSelectionMessage(type: ChatMessageType) {
    const activeEditor = vscode.window.activeTextEditor;
    if (!activeEditor) {
      vscode.window.showErrorMessage('没有打开的编辑器');
      return;
    }
    const filePath = activeEditor.document.uri.fsPath;
    let startLine = undefined, endLine = undefined;
    const code = EditorUtil.getCurrentSelectedCode();
    if (code) {
      startLine = EditorUtil.getCurrentSelectedCodeLines().startLine;
      endLine = EditorUtil.getCurrentSelectedCodeLines().endLine;
    }
    if (!code) {
      window.showInformationMessage('请先选择代码片断', '关闭');
    } else {
      this.selectionHandler.handleCodeSelectionEvent(CodeSelectionEventType.CODE_SELECTION_ASKED, {
        code,
        type,
        filePath,
        startLine,
        endLine
      });
    }
  }

  private startFileChat(type: ChatMessageType) {
    const activeEditor = vscode.window.activeTextEditor;
    if (!activeEditor) {
      vscode.window.showErrorMessage('没有打开的编辑器');
      return;
    }
    const filePath = activeEditor.document.uri.fsPath;
    const code = EditorUtil.getCurrentSelectedCode();
    let startLine = undefined, endLine = undefined;
    if (code) {
      startLine = EditorUtil.getCurrentSelectedCodeLines().startLine;
      endLine = EditorUtil.getCurrentSelectedCodeLines().endLine;
    }
    this.selectionHandler.handleCodeSelectionEvent(CodeSelectionEventType.CODE_SELECTION_ASKED, {
      code,
      type,
      filePath,
      startLine,
      endLine
    });

  }

  /**
   * 处理所选代码的变更事件
   * @param event
   */
  private handleSelectionChange(event: TextEditorSelectionChangeEvent) {
    const { textEditor, selections } = event;

    if (
      textEditor?.document.uri.toString() === window.activeTextEditor?.document.uri.toString() &&
      textEditor?.document.uri.scheme === 'file' &&
      selections.length > 0
    ) {
      const filePath = textEditor.document.uri.fsPath;
      const code = EditorUtil.buildSelectedCode(textEditor, selections[0]);
      this.selectionHandler.handleCodeSelectionEvent(
        CodeSelectionEventType.CODE_SELECTION_CHANGED, {
        code,
        filePath
      });
    }
  }
}
