import {
  CancellationToken,
  Command,
  commands,
  Disposable,
  InlineCompletionList,
  Position,
  SelectedCompletionInfo,
  SnippetString,
  TextDocument,
  TextEditor,
  window,
  Range,
} from 'vscode';
import { AutocompleteResult, ResultEntry, SuggestionTrigger } from './types';
import getAutoImportCommand from './getAutoImportCommand';
import SrdInlineCompletionItem from './inlineSuggestions/srdInlineCompletionItem';
import { escapeTabStopSign } from '../utils/common';
import runCompletion from './runCompletion';
import { SrdCommand, SrdContextKey } from '../common/constants';
import { Logger } from '../utils/logger';

// this will track only the suggestion which is "extending" the completion popup selected item,
// i.e. it is relevant only for case where both are presented popup and inline
let currentLookAheadSuggestion: SrdInlineCompletionItem | undefined | null;

export function clearCurrentLookAheadSuggestion(): void {
  currentLookAheadSuggestion = undefined;
}

export async function initTabOverride(): Promise<Disposable> {
  return Disposable.from(await enableTabOverrideContext(), registerTabOverride());
}

window.onDidChangeTextEditorSelection(clearCurrentLookAheadSuggestion);

// "look a head " suggestion
// is the suggestion witch extends te current selected intellisense popup item
// and queries codeCompleteEngin with the selected item as prefix untitled-file-extension
export async function getLookAheadSuggestion(
  document: TextDocument,
  { range, text }: SelectedCompletionInfo,
  position: Position,
  cancellationToken: CancellationToken,
  isAuto: boolean
): Promise<InlineCompletionList | undefined> {
  const textAtRange = document.getText(range);
  Logger.debug(
    `[getLookAheadSuggestion] request from [${position.line},${position.character}], textAtRange:${textAtRange}, text: ${text}`
  );

  const curSuggestText = text.substring(textAtRange.length);
  const response = await runCompletion({
    document,
    position: range.end,
    isAuto,
    currentSuggestionText: curSuggestText,
    retry: {
      cancellationToken,
    },
  });

  const result = findMostRelevantSuggestion(response, text);
  const completion =
    result &&
    response &&
    new SrdInlineCompletionItem(
      result.new_prefix.replace(response.old_prefix, text),
      result,
      range.with({
        // end: range.end.translate(0, curSuggestText.length + result.answer.length),
        end: range.end.translate(0, 0),
      }),
      getAutoImportCommand(result, response, position, SuggestionTrigger.LookAhead, curSuggestText, isAuto)
    );

  currentLookAheadSuggestion = completion;

  return completion ? new InlineCompletionList([completion]) : undefined;
}

function findMostRelevantSuggestion(
  response: AutocompleteResult | null | undefined,
  currentSelectedText: string
): ResultEntry | undefined {
  return response?.results.find(({ new_prefix, answer }) => {
    const before = new_prefix.substring(0, new_prefix.length - answer.length);

    return before.endsWith(currentSelectedText);
  });
}

function registerTabOverride(): Disposable {
  // TODO: tab-override未能生效
  return commands.registerTextEditorCommand(SrdCommand.TAB_OVERRIDE, (textEditor: TextEditor) => {
    if (!currentLookAheadSuggestion) {
      void commands.executeCommand('acceptSelectedSuggestion');
      return;
    }

    const { range, insertText, command } = currentLookAheadSuggestion;
    if (range && insertText && command) {
      void textEditor
        .insertSnippet(new SnippetString(escapeTabStopSign(insertText as string)), range)
        .then(() => executeSelectionCommand(command));
    }
  });
}

function executeSelectionCommand(command: Command): void {
  void commands.executeCommand(command.command, command.arguments?.[0]);
}

async function enableTabOverrideContext(): Promise<Disposable> {
  await commands.executeCommand('setContext', SrdContextKey.TAB_OVERRIDE, true);
  return {
    dispose() {
      void commands.executeCommand('setContext', SrdContextKey.TAB_OVERRIDE, undefined);
    },
  };
}
