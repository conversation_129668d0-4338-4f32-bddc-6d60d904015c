<template>
  <div class="footer-box-wrapper">
    <MenuOptions :answering="chatStore.answering" />
    <div class="footer-box" ref="footerBox">
      <!-- 快捷指令菜单 -->
      <a-dropdown popup-container=".app-footer" position="top" @select="onSelectDropMenu"
        class="footer-dropdown-menu quick-menu">
        <div class="dropdown-menu-div" ref="dropdownMenu"></div>
        <template #content>
          <a-doption v-for="(item, index) in dropMenuOptions" :key="item.label" :value="item.value" :title="item.title">
            <div style="display: flex;justify-content: space-between;">
              <span>
                {{ item.label }}
              </span>
              <span>
                {{ item.title }}
              </span>
            </div>
          </a-doption>
        </template>
      </a-dropdown>
      <LibSelect />
      <div class="session-input">
        <textarea :maxlength="MaxQuestionLength" rows="1" ref="messageInput" :disabled="disabledInputBox"
          v-model="message" class="message-input" :class="{ disabledBox: disabledInputBox }"
          placeholder="请输入您的问题，按Enter键发送" @keydown="onEnterKeyDown" @focus="onFocusClick" @input="onInput"
          @blur="onBlur" />
        <a-tooltip content="发送" position="top" mini :arrow-style="{ display: 'none' }">
          <svg-icon name="send" class="send-icon" :style="disabledInputBox ? ({ cursor: 'not-allowed' }) : {}"
            size="13px" @click="submitMessage"></svg-icon>
        </a-tooltip>
      </div>
      <div class="disable-cover" v-if="disabledInputBox">

      </div>
    </div>
  </div>

</template>

<script lang="ts" setup>
import { ref, readonly, computed, getCurrentInstance, watch } from 'vue';
import LibSelect from '@/components/LibSelect.vue';
import MenuOptions from '@/components/MenuOptions.vue';
import { useChatStore } from '@/store/chat';
import {
  ChatMessageType,
  ChatMessageTypeDesc,
  ChatMessageTypeEnDesc,
  MaxQuestionLength
} from '@/constants/common';
import { toast } from '@/utils';

const footerBox = ref(null)
const messageInput = ref(null)

const dropMenuOptions = readonly(
  [
    {
      title: ChatMessageTypeDesc[ChatMessageType.HELP],
      label: ChatMessageTypeEnDesc[ChatMessageType.HELP],
      value: ChatMessageType.HELP,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.EXPLAIN],
      label: ChatMessageTypeEnDesc[ChatMessageType.EXPLAIN],
      value: ChatMessageType.EXPLAIN,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.UNITTEST],
      label: ChatMessageTypeEnDesc[ChatMessageType.UNITTEST],
      value: ChatMessageType.UNITTEST,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.COMMENT],
      label: ChatMessageTypeEnDesc[ChatMessageType.COMMENT],
      value: ChatMessageType.COMMENT,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.OPTIMIZATION],
      label: ChatMessageTypeEnDesc[ChatMessageType.OPTIMIZATION],
      value: ChatMessageType.OPTIMIZATION,
    },

  ]);

const app = getCurrentInstance();
const $EventBus = app?.appContext.config.globalProperties.$EventBus;

$EventBus.on('setFooterInputMessage', setFooterInputMessage);
$EventBus.on('clearMessageInputAt', clearMessageInputAt);



const emit = defineEmits(['submitMsg', 'shortcut']);
const chatStore = useChatStore();
const message = ref('');
const dropdownMenu = ref<null | HTMLElement>(null);
const templateId = ref('')

const disabledInputBox = computed(() => {
  return !chatStore.codeFreeLoginStatus || chatStore.disabledInputBox || chatStore.answering // || !chatStore.online
})

function clearMessageInputAt() {
  if (message.value === '@') {
    message.value = ''
    setInputHeight()
  }
}

function setFooterInputMessage(item: any) {
  templateId.value = item.id
  message.value = item.content
  setInputHeight()
}
function setInputHeight() {
  if (message.value && message.value.length > MaxQuestionLength) {
    message.value = message.value.slice(0, MaxQuestionLength)
  }
  setTimeout(() => {
    if (messageInput.value) {
      (messageInput.value as any).style.height = '24px';
      (messageInput.value as any).style.height = `${(messageInput.value as any).scrollHeight}px`;
    }
  });

}

/** 代码问答 - 发送信息 */
function submitMessage() {
  chatStore.helpNotes = ''
  if (disabledInputBox.value) return
  if (!chatStore.codeFreeLoginStatus) return toast('未登录或登录失效，请先登录再使用')
  if (!message.value.trim()) {
    return toast('请输入您的问题，按Enter键提交问题，按Ctrl+Enter键换行');
  }
  if (templateId.value) {
    chatStore.lastTemplateId = templateId.value
  }
  templateId.value = ''
  emit('submitMsg', message.value);

  message.value = '';
  setInputHeight()
}

function insertNewLine(event: any) {
  const textarea = event.target;
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const text = textarea.value;
  // 在光标位置插入换行符
  message.value = text.substring(0, start) + '\n' + text.substring(end);
  // 将光标移动到新行的开头
  setTimeout(() => {
    textarea.selectionStart = textarea.selectionEnd = start + 1;
  })
  // 阻止默认的换行行为
  event.preventDefault();
  setInputHeight()

}
/* 代码问答 - enter */
function onEnterKeyDown(event: KeyboardEvent) {
  // 检查是否按下了 Shift + Enter 组合键
  if (event.shiftKey && event.key === 'Enter') {
    insertNewLine(event);
    return
  }
  // 检查是否按下了 Ctrl + Enter 组合键
  else if (event.ctrlKey && event.key === 'Enter') {
    insertNewLine(event);
    return
  }
  // 检查是否按下了 Command + Enter 组合键（兼容 Mac）
  else if (event.metaKey && event.key === 'Enter') {
    insertNewLine(event);
    return
  }
  if (event.keyCode == 13 || event.key === '13') {
    if (event.ctrlKey) {
      message.value = message.value + '\n';
    } else {
      event.preventDefault();
      submitMessage();
    }
  }
}

/** 代码问答 - 点击获焦 */
function onFocusClick() {
  chatStore.inputFocusing = true
  activeDropdown();
}

function onInput(ev: any) {
  setInputHeight();
  activeDropdown()
}

/**
 * 代码问答 - 激活快捷菜单
 */
function activeDropdown() {
  if (message.value === '/') {
    message.value = '';
    dropdownMenu.value && (dropdownMenu.value as any).click();
  } else if (message.value === '@') {
    const refAddButton = document.querySelector('#ref-add-button') as any
    if (refAddButton) {
      refAddButton.click()
    }
  }
}

function onBlur() {
  chatStore.inputFocusing = false
  setInputHeight();
}

/** 代码问答 - 选择快捷指令 */
function onSelectDropMenu(askType?: string | number | Record<string, any> | undefined) {
  if (templateId.value) {
    chatStore.lastTemplateId = templateId.value
  }
  templateId.value = ''
  emit('shortcut', askType)
}

watch(disabledInputBox, (val) => {
  if (!val) {
    setTimeout(() => {
      if (messageInput.value) {
        (messageInput.value as any).focus()
      }
    })
  }
}, { immediate: true })
</script>

<style lang="scss">
.footer-box-wrapper {
  position: relative;
}

.footer-box {
  position: relative;
  box-sizing: border-box;
  margin: 12px 20px 20px 20px;
  padding: 12px;
  color: var(--vscode-icon-foreground);
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-code-border);

  // overflow: hidden;
  border-radius: 4px;

  &:hover {
    border: 1px solid #307cfb;

    .send-icon {
      color: #307cfb;
    }
  }

  .disabledBox {
    cursor: not-allowed;
  }

  .disable-cover {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: var(--cf-input-disabled-cover-color);
    opacity: 0.4;
    cursor: not-allowed;
  }

  .session-input {
    position: relative;
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-none-color);
    margin-top: 10px;
    margin-right: -10px;

  }

  .message-input {
    padding-right: 30px;
    height: 24px;
    width: 100%;
    max-height: 350px;
    overflow-y: auto;
    border: none;
    // border: 1px solid var(--vscode-none-color, transparent);
    color: var(--vscode-inputOption-activeForeground);
    background-color: var(--vscode-input-background);
    resize: none;

    &:focus {
      outline: none;
      // outline: 1px solid var(--vscode-none-color, transparent);
    }
  }

  .send-icon {
    position: absolute;
    right: 20px;
    bottom: 10px;
    cursor: pointer;
    color: var(--vscode-code-border);
    // color: #307cfb; 

  }

  .answering-line {
    position: absolute;
    z-index: 9999;
    top: 0;
    left: 0;
    width: 20px;
    height: 2px;
    background-color: var(--vscode-textLink-activeForeground);
    animation: answeringline infinite 4s ease-in-out;
  }

  @keyframes answeringline {
    0% {
      left: -20px;
    }

    50% {
      width: 40px;
    }

    100% {
      left: calc(100% + 20px);
    }
  }
}

.dropdown-menu-div {
  width: 100%;
  position: absolute;
  top: 2px;
}

.footer-dropdown-menu {
  width: 100%;
  padding: 0 20px;

  .arco-dropdown {
    box-shadow: 0 4px 10px #0000001a;
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-inputOption-activeForeground);
    border: 1px solid var(--vscode-none-color);
    margin-bottom: 15px;
  }

  &.quick-menu {
    .arco-dropdown {
      margin-bottom: 8px;
    }
  }
}

.arco-dropdown-option-content {
  width: 100% !important;
}
</style>
