import { ProjectItem, ChatMessage } from '../types';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import { Logger } from '../../utils/logger';
import { ContextInputItem } from '../types';
import { selectedWorkItem } from '../../codechat/types';
import { setPathPermissions } from '../../utils/fileSystemUtils';

interface ProjectHistory {
  projectPath: string;
  windows: WindowHistory[];
}

interface WindowHistory {
  windowId: string;
  histories: ChatHistory[];
}

/**
 * 消息历史
 */
export interface ChatHistory {
  messages: Message[];
  sequence: number;
}

export interface Message {
  role: string;
  content: string;
  displayContent?: string;
  createTime?: string;
  contextInputItems?: ContextInputItem[];
  selectedWorkItems?: selectedWorkItem[];
}

/**
 * 功能：持久化agent所有的历史上下文
 * 为后面做历史消息功能预留
 */
export class AgentHistoryUtil {
  private historyDir = path.join(os.homedir() || '', '.codefree', 'plugin', 'history');
  private historyFile = path.join(this.historyDir, 'chat_history_v1.json');
  // 单例模式
  private static instance: AgentHistoryUtil | null;

  public static getInstance(): AgentHistoryUtil {
    if (AgentHistoryUtil.instance == null) {
      AgentHistoryUtil.instance = new AgentHistoryUtil();
      // 确保目录和文件存在
      AgentHistoryUtil.instance.ensureDirectoryExists();
    }
    return AgentHistoryUtil.instance;
  }

  /**
   * 确保历史记录目录存在
   */
  private ensureDirectoryExists(): void {
    // --- Directory Handling ---
    if (!fs.existsSync(this.historyDir)) {
      try {
        fs.mkdirSync(this.historyDir, { recursive: true });
      } catch (mkdirError: any) {
        if (mkdirError.code === 'EACCES' || mkdirError.code === 'EPERM') {
          Logger.error(`Permission denied creating directory ${this.historyDir}: ${mkdirError.message}`);
        } else {
          Logger.error(`Failed to create directory ${this.historyDir}: ${mkdirError.message}`);
        }
        return; // Stop if directory creation fails
      }
      setPathPermissions(this.historyDir, 'directory'); // Best effort
    } else {
      setPathPermissions(this.historyDir, 'directory'); // Best effort for existing dir
    }

    // --- File Handling ---
    if (!fs.existsSync(this.historyFile)) {
      try {
        fs.writeFileSync(this.historyFile, '[]', 'utf-8');
      } catch (writeFileError: any) {
        if (writeFileError.code === 'EACCES' || writeFileError.code === 'EPERM') {
          Logger.error(`Permission denied writing initial history file ${this.historyFile}: ${writeFileError.message}`);
        } else {
          Logger.error(`Failed to write initial history file ${this.historyFile}: ${writeFileError.message}`);
        }
        return; // Stop if initial file creation fails
      }
      setPathPermissions(this.historyFile, 'file'); // Best effort
    } else {
      setPathPermissions(this.historyFile, 'file'); // Best effort for existing file
    }
  }

  /**
   * 添加新的对话记录
   * @param project 当前项目
   * @param messages 消息列表
   * @return 使用的会话ID
   */
  public addHistory(project: ProjectItem, messages: Message[]): string {
    // 确保目录存在
    this.ensureDirectoryExists();

    const allProjects: ProjectHistory[] = this.readHistoryFile();
    const projectPath = project.projectPath || '';
    const windowId = project.windowId;
    const newMessages: Message[] = messages.map(msg => ({
      role: msg['role'] || '',
      content: msg['content'] || '',
      displayContent: msg['displayContent'],
      createTime: msg['createTime'],
      contextInputItems: msg['contextInputItems'],
      selectedWorkItems: msg['selectedWorkItems'],
      // displayContent: msg['displayContent'] || '',
      // createTime: msg['createTime'] || '',
      // contextInputItems: msg['contextInputItems'] || []
    }));

    const projectIndex = allProjects.findIndex(p => p.projectPath === projectPath);
    if (projectIndex !== -1) {
      const project = allProjects[projectIndex];
      const windows = [...project.windows];
      const windowIndex = windows.findIndex(w => w.windowId === windowId);

      if (windowIndex !== -1) {
        const window = windows[windowIndex];
        const histories = [...window.histories];
        const maxSequence = Math.max(...histories.map(h => h.sequence), 0);
        histories.push({ messages: newMessages, sequence: maxSequence + 1 });
        windows[windowIndex] = { ...window, histories };
      } else {
        windows.push({
          windowId,
          histories: [{ messages: newMessages, sequence: 1 }]
        });
      }
      allProjects[projectIndex] = { ...project, windows };
    } else {
      allProjects.push({
        projectPath,
        windows: [{
          windowId,
          histories: [{ messages: newMessages, sequence: 1 }]
        }]
      });
    }

    try {
      fs.writeFileSync(this.historyFile, JSON.stringify(allProjects));
    } catch (error: any) {
      if (error.code === 'EACCES' || error.code === 'EPERM') {
        Logger.error(`Permission denied writing history file ${this.historyFile}: ${error.message}`);
      } else {
        Logger.error(`写入历史记录文件失败 ${this.historyFile}: ${error.message}`);
      }
      // Depending on desired behavior, you might want to return null or throw
    }
    return windowId;
  }

  private readHistoryFile(): ProjectHistory[] {
    try {
      const content = fs.readFileSync(this.historyFile, 'utf-8');
      return content ? JSON.parse(content) : [];
    } catch {
      return [];
    }
  }

  /**
   * 获取指定窗口的所有消息
   * @param project 当前项目
   * @param conversationId 会话ID
   * @return 按序号排序的聊天记录列表
   */
  public getHistoryByConversationId(
    project: ProjectItem,
    windowId: string
  ): ChatHistory[] {
    const projectPath = project.projectPath;
    if (!projectPath) {
      return [];
    }
    const projectHistory = this.getProjectHistory(projectPath);
    if (!projectHistory) {
      return [];
    }
    const window = projectHistory.windows.find(w => w.windowId === windowId);
    if (!window) {
      return [];
    }
    return (window.histories || []).sort((a, b) => a.sequence - b.sequence);
  }

  private getProjectHistory(projectPath: string): ProjectHistory | null {
    try {
      const fileContent = fs.readFileSync(this.historyFile, 'utf-8');
      if (fileContent.length > 0) {
        const allProjects: ProjectHistory[] = JSON.parse(fileContent);
        return allProjects.find(project => project.projectPath === projectPath) || null;
      }
      return null;
    } catch (error) {
      Logger.error(`读取项目历史失败:${error}`);
      return null;
    }
  }

  /**
   * 删除指定窗口的指定序号历史记录
   * @param projectPath 项目路径
   * @param windowId 窗口ID
   * @param sequence 序号
   */
  public removeHistoryBySequence(projectPath: string, windowId: string, sequence: number): void {
    try {
      const fileContent = fs.readFileSync(this.historyFile, 'utf-8');
      let allProjects: ProjectHistory[] = fileContent.length > 0
        ? JSON.parse(fileContent)
        : [];

      const projectIndex = allProjects.findIndex(p => p.projectPath === projectPath);
      if (projectIndex !== -1) {
        const project = allProjects[projectIndex];
        let windows = [...project.windows];

        const windowIndex = windows.findIndex(w => w.windowId === windowId);
        if (windowIndex !== -1) {
          const window = windows[windowIndex];
          const histories = window.histories.filter(h => h.sequence !== sequence);

          if (histories.length === 0) {
            windows.splice(windowIndex, 1);
          } else {
            windows[windowIndex] = { ...window, histories };
          }

          if (windows.length === 0) {
            allProjects.splice(projectIndex, 1);
          } else {
            allProjects[projectIndex] = { ...project, windows };
          }
        }
      }

      fs.writeFileSync(this.historyFile, JSON.stringify(allProjects));
    } catch (error: any) {
      if (error.code === 'EACCES' || error.code === 'EPERM') {
        Logger.error(`Permission denied removing history sequence (write op) ${this.historyFile}: ${error.message}`);
      } else {
        Logger.error(`删除历史记录失败:${error.message}`);
      }
    }
  }

  /**
   * 清理指定窗口的所有历史记录
   * @param windowId 窗口ID
   * @returns 清理成功返回true，失败返回false
   */
  public clearWindowIdHistory(windowId: string): boolean {
    try {
      const fileContent = fs.readFileSync(this.historyFile, 'utf-8');
      let allProjects: ProjectHistory[] = fileContent.length > 0
        ? JSON.parse(fileContent)
        : [];

      // 遍历所有项目，删除指定窗口
      allProjects = allProjects
        .map(project => {
          // 过滤掉指定的窗口
          const filteredWindows = project.windows.filter(w => w.windowId !== windowId);
          return {
            ...project,
            windows: filteredWindows
          };
        })
        .filter(project => project.windows.length > 0);

      fs.writeFileSync(this.historyFile, JSON.stringify(allProjects));
      return true; // 操作成功
    } catch (error: any) {
      if (error.code === 'EACCES' || error.code === 'EPERM') {
        Logger.error(`Permission denied clearing window history (write op) ${this.historyFile}: ${error.message}`);
      } else {
        Logger.error(`清理窗口历史记录失败:${error.message}`);
      }
      return false; // 操作失败
    }
  }

  /**
   * 清理所有历史记录
   */
  public clearHistory(): void {
    try {
      fs.writeFileSync(this.historyFile, '[]');
    } catch (error: any) {
      if (error.code === 'EACCES' || error.code === 'EPERM') {
        Logger.error(`Permission denied clearing all history ${this.historyFile}: ${error.message}`);
      } else {
        Logger.error(`清理所有历史记录失败 ${this.historyFile}: ${error.message}`);
      }
    }
  }

  /**
   * 将ChatHistory转换为ChatMessage列表
   */
  public convertToChatMessages(
    histories: ChatHistory[],
    project: ProjectItem,
  ): ChatMessage[] {
    return histories.flatMap(chatHistory =>
      chatHistory.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        project: project.projectId
      }))
    );
  }

  public convertHistoryToMessagesByWindowId(project: ProjectItem, windowId: string) {
    const histories = this.getHistoryByConversationId(project, windowId);
    if (!histories) {
      return [];
    }
    return this.convertToChatMessages(histories, project);
  }
}
