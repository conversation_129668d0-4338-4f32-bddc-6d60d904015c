import { RtnCode } from "@/constants/common";
import { Message, Modal } from "@arco-design/web-vue";

interface OPT {
  [key: string]: string;
}
export function dateFormat(date: Date, fmt: string = 'YYYY-mm-dd HH:MM:SS'): string {
  if (isNaN(date.getTime())) return '';
  let ret;
  const opt: OPT = {
    'Y+': date.getFullYear().toString(), // 年
    'm+': (date.getMonth() + 1).toString(), // 月
    'd+': date.getDate().toString(), // 日
    'H+': date.getHours().toString(), // 时
    'M+': date.getMinutes().toString(), // 分
    'S+': date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (const k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k as keyof OPT] : opt[k as keyof OPT].padStart(ret[1].length, '0')
      );
    }
  }
  return fmt;
}

export function hashCode(str: string) {
  let hash = 0, i, chr;
  if (str.length === 0) return hash;
  for (i = 0; i < str.length; i++) {
    chr = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + chr;
    hash |= 0; // Convert to 32bit integer
  }
  return hash;
};


// 获取assets静态资源
export const getAssetsFile = (url: string) => {
  return new URL(`/src/assets/images/${url}`, import.meta.url).href
}

export function toast(content: string, position = 'bottom') {
  return Message.info({
    content,
    position: position as any
  })
}
const ForbbidenInstance = {
  show: false
}

export function showForbiddenModal(sender: any) {
  if (ForbbidenInstance.show) return
  ForbbidenInstance.show = true
  Modal.info({
    title: '账号被禁用通知',
    content: '该账号已经被禁用，请联系管理员解禁后继续使用。',
    maskClosable: false,
    hideCancel: true,
    okText: '确定',
    onOk(e) {
      ForbbidenInstance.show = false
    },
  });
}
/**
 * 是否账号被禁用
 * @param payload {code: 403} or {data:{code: 6}}
 */
export function isForbidden(payload: any) {
  return false

  // const isForbiddenCode = (code: number) => {
  //   return code === RtnCode.FORBBIDEN_HTTP || code === RtnCode.FORBBIDEN_WS
  // }
  // if (payload) {
  //   if (isForbiddenCode(payload.code)) {
  //     return true
  //   }
  //   if (payload.data && isForbiddenCode(payload.data.code)) {
  //     return true
  //   }
  //   if (payload.data && payload.data.data && isForbiddenCode(payload.data.data.code)) {
  //     return true
  //   }
  // }
  // return false
}

export const FileTypeIcon: any = {
  file: 'icon-generic',
  java: 'icon-java',
  js: 'icon-js',
  go: 'icon-go',
  ts: 'icon-ts',
  py: 'icon-py',
  folder: 'icon-folder-new',
  workItem: 'icon-workitem',
  codebase: 'codebase-icon',
  lib: 'icon-knowledgebasequestion'
}


export function getFileNameFromPath(path: string) {
  // 匹配最后一个路径分隔符（/ 或 \）之后的所有字符
  const regex = /[\\\/]([^\\\/]+)$/;
  const match = path.match(regex);
  return match ? match[1] : path;
}


export function getFolderNameFromPath(path: string) {
  const cleanPath = path.replace(/[\\/]+$/, ''); // 兼容 \ 和 / 
  const parts = cleanPath.split(/[\\/]/); // 同时处理Windows和Unix路径
  return parts.pop() || ''; // 防止空路径
}

export function normalizeLineEndings(text: string): string {
  // 统一换行符为 \n
  return text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
}