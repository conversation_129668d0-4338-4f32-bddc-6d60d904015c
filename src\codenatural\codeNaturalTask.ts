import { Position, Range, TextEditor, window, TextDocumentContentChangeEvent } from 'vscode';
import EditorHand<PERSON> from './editorHandler';
import { ChatMessageType, RtnCode, RtnMessage } from '../common/constants';
import { getDocumentPrefixAndSuffix, getFileNameWithExtension } from '../utils/textEditor';
import {
  CodeNaturalDataType,
  CodeNaturalEventType,
  ICodeNaturalEnginHandler,
  ICodeNaturalEventHandler,
  CodeNaturalTaskState,
  CodeNaturalTaskType,
} from './types';
import CodeNaturalEngin from './codeNaturalEngin';
import { IStatusBarHandler } from '../statusbar/types';
import { Logger } from '../utils/logger';
import { DataReportServiceInst } from '../service/dataReportService';
import { ActivityType } from '../service/types/dataReport';

/**
 * 一个自然语言请求任务
 */
export default class CodeNaturalTask implements ICodeNaturalEnginHandler, ICodeNaturalEventHandler {
  private currentState: CodeNaturalTaskState = CodeNaturalTaskState.Init;

  private editorHandler: EditorHandler | undefined;

  private editor: TextEditor;

  private reqId = '';

  private wholeAnswer = '';

  private question = '';

  private startPosition: Position;

  private endPosition: Position | undefined;

  private codeNaturalEngin: CodeNaturalEngin;

  private eventHandler: ICodeNaturalEventHandler;

  private type: CodeNaturalTaskType;

  private hasValidCodeAfterAnswer = false;

  public constructor(
    eventHandler: ICodeNaturalEventHandler,
    statusBarHandler: IStatusBarHandler,
    editor: TextEditor,
    type: CodeNaturalTaskType,
    question: string
  ) {
    this.eventHandler = eventHandler;
    this.editor = editor;
    this.type = type;
    this.question = question;
    this.startPosition = editor.selection.active;
    this.codeNaturalEngin = new CodeNaturalEngin(this, statusBarHandler);
  }

  public getEditor() {
    return this.editor;
  }

  public setEditor(editor: TextEditor) {
    this.editor = editor;
    this.editorHandler?.setEditor(editor);
  }

  public getCurrentState() {
    return this.currentState;
  }

  public getStartPosition() {
    return this.startPosition;
  }

  public getRange() {
    if (this.startPosition && this.endPosition) {
      return new Range(this.startPosition, this.endPosition);
    }
  }

  public getReqId() {
    return this.reqId;
  }

  public getType() {
    return this.type;
  }

  public hasValidCode() {
    return this.hasValidCodeAfterAnswer;
  }

  /**
   * 开始提问
   * @param request
   */
  public startQuestion() {
    this.initParams();
    const document = this.editor.document;
    const { prefix, suffix } = getDocumentPrefixAndSuffix(document, this.startPosition);
    const filename = getFileNameWithExtension(document);

    const reqId = this.codeNaturalEngin.askQuestion({
      question: this.question,
      filename,
      language: document.languageId,
      prefix,
      suffix,
      manualType: ChatMessageType.MANUAL_GENERATE,
    });

    if (reqId) {
      this.reqId = reqId;
      this.editorHandler = new EditorHandler(this, this.reqId, this.editor, this.startPosition);
      this.switchState(CodeNaturalTaskState.WaitingResp);
    }

    return reqId;
  }

  /**
   * 收到自然语言编程回答
   * @param reqId
   * @param isEnd
   * @param answer
   * @param code
   */
  public onCodeNaturalResponse(reqId: string, isEnd: number, answer: string, code: number): void {
    if (reqId !== this.reqId) {
      return;
    }

    if (code !== RtnCode.SUCCESS) {
      this.switchState(CodeNaturalTaskState.OnFinished);
      this.handleTaskError(reqId, code);
      return;
    }

    if (
      !this.editorHandler ||
      this.currentState === CodeNaturalTaskState.Init ||
      this.currentState > CodeNaturalTaskState.OnAcceptOrReject
    ) {
      return;
    }

    this.wholeAnswer += answer;

    if (this.currentState === CodeNaturalTaskState.WaitingResp) {
      this.editorHandler.addCodeSnippet(isEnd, answer);
      this.editorHandler.startDisplayCodeSnippet();
      this.switchState(CodeNaturalTaskState.OnAnswering);
    } else if (this.currentState === CodeNaturalTaskState.OnAnswering) {
      this.editorHandler.addCodeSnippet(isEnd, answer);
    }
  }

  /**
   * 处理下层事件
   * @param eventType
   * @param params
   */
  public handleEvent(eventType: CodeNaturalEventType, params: unknown): void {
    switch (eventType) {
      case CodeNaturalEventType.TASK_ON_ANSWER_END: {
        Logger.debug(`[CodeNaturalTask] TASK_ON_ANSWER_END, params: ${JSON.stringify(params)}`);
        const data = params as { reqId: string; endPosition: Position; answer: string };

        if (data.reqId === this.reqId) {
          this.endPosition = data.endPosition;

          //code_chat_gen dataReport
          DataReportServiceInst.notifyUserActivity(ActivityType.CODE_CHAT_GEN, this.wholeAnswer);

          this.wholeAnswer += data.answer;
          this.switchState(CodeNaturalTaskState.OnAcceptOrReject);
          this.setHasValidCode();

          this.eventHandler.handleEvent(eventType, {
            reqId: data.reqId,
            range: this.getRange(),
            wholeAnswer: this.wholeAnswer,
          });
        }

        break;
      }
      default:
        this.eventHandler.handleEvent(eventType, params);
        break;
    }
  }

  /**
   * 获取数据
   * @param dataType
   * @param params
   */
  public getEventData(dataType: CodeNaturalDataType, params: unknown) {
    throw new Error('Method not implemented.');
  }

  /**
   * 接受回答
   */
  public async acceptAnswer() {
    if (this.currentState !== CodeNaturalTaskState.OnAcceptOrReject || !this.editorHandler) {
      return;
    }

    await this.editorHandler.acceptCodeSnippet(this.wholeAnswer, this.endPosition);

    //code_chat_accepted dataReport
    const wholeAnswer = this.wholeAnswer.slice(0, this.wholeAnswer.length - 1);
    DataReportServiceInst.notifyUserActivity(ActivityType.CODE_CHAT_ACCEPTED, wholeAnswer);

    this.switchState(CodeNaturalTaskState.OnFinished);
    this.handleEvent(CodeNaturalEventType.TASK_ON_FINISHED, { reqId: this.reqId });
  }

  /**
   * 拒绝回答
   */
  public async rejectAnswer() {
    if (this.currentState !== CodeNaturalTaskState.OnAcceptOrReject || !this.editorHandler) {
      return;
    }

    await this.editorHandler.rejectCodeSnippet(this.endPosition);

    this.switchState(CodeNaturalTaskState.OnFinished);
    this.handleEvent(CodeNaturalEventType.TASK_ON_FINISHED, { reqId: this.reqId });
  }

  /**
   * 重新生成回答
   */
  public async regenerateAnswer() {
    if (this.currentState !== CodeNaturalTaskState.OnAcceptOrReject || !this.editorHandler) {
      return;
    }

    const oldReqId = this.reqId;
    await this.editorHandler.rejectCodeSnippet(this.endPosition);
    this.switchState(CodeNaturalTaskState.Init);

    // 重新生成
    const reqId = this.startQuestion();

    if (reqId) {
      this.handleEvent(CodeNaturalEventType.TASK_ON_REGENERATE, {
        oldReqId,
        newReqId: reqId,
      });
    }
  }

  /**
   * 收到回答后取消提问
   */
  public async cancelQuestion() {
    if (this.currentState !== CodeNaturalTaskState.OnAcceptOrReject || !this.editorHandler) {
      return;
    }

    await this.editorHandler.rejectCodeSnippet();
    this.switchState(CodeNaturalTaskState.OnFinished);
    this.handleEvent(CodeNaturalEventType.TASK_ON_FINISHED, { reqId: this.reqId });
  }

  /**
   * 回答显示完以后，手动修改了回答区域内容，需要更新回答与结束位置
   */
  public updateContentAfterAnswerEnd(contentChanges: readonly TextDocumentContentChangeEvent[]) {
    if (this.currentState !== CodeNaturalTaskState.OnAcceptOrReject || !this.editorHandler) {
      return;
    }

    contentChanges.forEach(content => {
      const { range, text, rangeLength } = content;
      const answerRange = this.getRange();

      if (answerRange?.contains(range) && answerRange?.end.isAfter(range.end)) {
        const offset = this.editor.document.offsetAt(range.start);
        const offsetAfterAnswer = this.wholeAnswer.slice(offset + rangeLength);
        const offsetBeforeAnswer = this.wholeAnswer.slice(0, offset);

        this.wholeAnswer = `${offsetBeforeAnswer}${text}${offsetAfterAnswer}`;
        const startOffset = this.editor.document.offsetAt(this.startPosition);
        this.endPosition = this.editor.document.positionAt(startOffset + this.wholeAnswer.length);
      }
    });
  }

  /**
   * 恢复回答状态
   */
  public recoveryAnswer() {
    switch (this.currentState) {
      case CodeNaturalTaskState.OnAnswering:
      case CodeNaturalTaskState.OnAcceptOrReject:
        this.editorHandler?.recoveryCodeSnippet();
        break;
      default:
        break;
    }
  }

  /**
   * 切换状态
   * @param state
   */
  private switchState(state: CodeNaturalTaskState) {
    this.currentState = state;
  }

  /**
   * 初始化私有变量
   */
  private initParams() {
    this.wholeAnswer = '';
    this.endPosition = this.startPosition;
    this.editorHandler = undefined;
    this.hasValidCodeAfterAnswer = false;
  }

  /**
   * 回答结束后是否生成了有效代码
   */
  private setHasValidCode() {
    if (this.currentState !== CodeNaturalTaskState.OnAcceptOrReject) {
      return;
    }

    this.hasValidCodeAfterAnswer =
      this.wholeAnswer.length > 0 &&
      !this.getRange()?.isEmpty &&
      this.editor.document.getText(this.getRange()) !== '\n';
  }

  /**
   * 处理异常展示效果
   * @param reqId
   * @param code
   */
  private handleTaskError(reqId: string, code: RtnCode) {
    switch (code) {
      case RtnCode.INVALID_QUESTION:
        this.showErrorMessage(code);
        break;
      case RtnCode.INVALID_ANSWER:
        this.editorHandler?.rejectCodeSnippet();
        this.showErrorMessage(code);
        break;
      default:
        break;
    }

    this.handleEvent(CodeNaturalEventType.TASK_ON_ERROR, { reqId, code });
  }

  /**
   * 异常提示气泡
   * @param code
   * @returns
   */
  private showErrorMessage(code: RtnCode) {
    const msg = RtnMessage[code];

    if (!msg) {
      return;
    }

    window.showInformationMessage(`自然语言提问失败，原因：${msg}`, '关闭');
  }
}
