import { Range, Selection, TextEditor, ViewColumn, window, workspace } from 'vscode';
import * as vscode from 'vscode';

export default class EditorUtil {
  /**
   * 获取当前选中代码块
   */
  public static getCurrentSelectedCode() {
    const editor = window.activeTextEditor;
    const selection = editor?.selection;

    return this.buildSelectedCode(editor, selection);
  }

  public static getCurrentSelectedCodeLines() {
    const editor = window.activeTextEditor;
    const selection = editor?.selection;
    let startLine = 0, endLine = 0;
    if (!editor || !selection) {
      return { startLine, endLine }
    }

    startLine = selection.start.line;
    endLine = selection.end.line;

    return { startLine, endLine }
  }

  /**
 * 设置选中的代码块
 */
  public static setLineCodeSelected(start: number, end: number) {
    const editor = window.activeTextEditor;

    if (!editor) {
      return;
    }

    const lineRange = new vscode.Range(start, 0, end, Number.MAX_VALUE);

    editor.selection = new vscode.Selection(lineRange.start, lineRange.end);
  }

  /**
   * 取消选中的代码块
   */
  public static cancelSelectedCode() {
    const editor = window.activeTextEditor;
    const selection = editor?.selection;

    if (!editor || !selection) {
      return;
    }

    const range = new Range(selection.anchor, selection.active);

    if (range.isEmpty) {
      return;
    }

    editor.selection = new vscode.Selection(selection.start, selection.start);
  }


  /**
   * 构建代码
   * @param editor
   * @param selection
   * @returns
   */
  public static buildSelectedCode(editor?: TextEditor, selection?: Selection) {
    if (!editor || !selection) {
      return '';
    }

    let start = selection.anchor;
    let end = selection.active;

    if (selection.isReversed) {
      start = selection.active;
      end = selection.anchor;
    }

    const range = new Range(start, end);

    if (range.isEmpty) {
      return '';
    }

    let rangeText = editor.document.getText(range);
    if (!/\n$/.test(rangeText)) {
      rangeText = `${rangeText}\n`;
    }

    return `\`\`\`${editor.document.languageId}\n${rangeText}\`\`\``;
  }

  /**
   * 插入代码到编辑器
   * @param code
   * @param isNewFile
   */
  public static async insertTextEditor(code: string, isNewFile?: boolean) {
    const activeEditor = window.activeTextEditor;

    if (isNewFile) {
      const document = await workspace.openTextDocument({
        content: code,
        language: activeEditor?.document?.languageId,
      });

      return await window.showTextDocument(document, ViewColumn.Beside);
    }

    if (activeEditor) {
      const selection = activeEditor.selection;
      const range = new Range(selection.start, selection.end);

      activeEditor.edit(editBuilder => {
        editBuilder.replace(range, code);
      });
    }
  }
}
