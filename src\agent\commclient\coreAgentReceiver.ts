import { IAgentMessageReceiver } from './MessageReceiver';
import { Logger } from '../../utils/logger';

/**
 * Core Agent 消息接收器
 * 处理来自 Core Agent 的消息
 */
export class CoreAgentMessageReceiver implements IAgentMessageReceiver {
  // 可以注入依赖服务
  constructor(private readonly serviceId?: string) { }

  /**
   * 处理 Agent 消息
   * @param text 消息内容 JSON 字符串
   */
  public async onAgentMessageHandler(text: string): Promise<any> {
    if (!text || !text.trim()) {
      return;
    }

    try {
      // 解析消息
      const message = JSON.parse(text);
      const messageId = message.messageId?.toString();
      const messageType = message.messageType?.toString();
      const data = message.data;

      // 记录消息信息
      if (messageType === 'indexProgress') {
        Logger.info(`[CoreAgent] 索引进度: ${JSON.stringify(data)}`);
      } else {
        Logger.info(`[CoreAgent] 消息类型: ${messageType}, 数据: ${JSON.stringify(data)}`);
      }

      // 根据消息类型处理不同的消息
      switch (messageType) {
        case 'completion':
          this.handleCompletionMessage(data);
          break;
        case 'indexProgress':
          this.handleIndexProgressMessage(data);
          break;
        case 'error':
          this.handleErrorMessage(data);
          break;
        case 'status':
          this.handleStatusMessage(data);
          break;
        default:
          // 处理其他类型的消息
          this.handleGenericMessage(messageType, data);
          break;
      }
    } catch (error) {
      Logger.error(`[CoreAgent] 处理消息时出错: ${error}`);
    }
  }

  /**
   * 处理补全消息
   */
  private handleCompletionMessage(data: any): void {
    // 实现补全消息的处理逻辑
    Logger.debug(`[CoreAgent] 处理补全消息: ${JSON.stringify(data)}`);
    // 可以触发事件或调用其他服务
  }

  /**
   * 处理索引进度消息
   */
  private handleIndexProgressMessage(data: any): void {
    // 实现索引进度消息的处理逻辑
    Logger.debug(`[CoreAgent] 索引进度: ${data.progress}%, 状态: ${data.status}`);
    // 可以更新UI或通知其他组件
  }

  /**
   * 处理错误消息
   */
  private handleErrorMessage(data: any): void {
    // 实现错误消息的处理逻辑
    Logger.error(`[CoreAgent] 错误: ${data.message}, 代码: ${data.code}`);
    // 可以显示错误通知或记录错误
  }

  /**
   * 处理状态消息
   */
  private handleStatusMessage(data: any): void {
    // 实现状态消息的处理逻辑
    Logger.debug(`[CoreAgent] 状态更新: ${data.status}`);
    // 可以更新UI状态或通知其他组件
  }

  /**
   * 处理通用消息
   */
  private handleGenericMessage(messageType: string, data: any): void {
    // 处理其他类型的消息
    Logger.debug(`[CoreAgent] 收到消息类型: ${messageType}, 数据: ${JSON.stringify(data)}`);
    // 可以根据需要分发到其他处理程序
  }
} 