@use 'sass:meta';

/** webview页面根节点会根据vscode主题更换4种样式 */
/** 代码块使用highlight.js格式化，选取适合的主题样式适用vscode主题色，视情况更换 */
.vscode-dark {
  @include meta.load-css('highlight.js/styles/atom-one-dark.css');
}

.vscode-light {
  @include meta.load-css('highlight.js/styles/atom-one-light.css');
}

.vscode-high-contrast {
  @include meta.load-css('highlight.js/styles/github-dark.css');
}

.vscode-high-contrast-light {
  @include meta.load-css('highlight.js/styles/github.css');
}
