import { IAgentMessageReceiver } from './MessageReceiver';
import { Logger } from '../../utils/logger';
import * as net from 'net';
import * as fs from 'fs';
import { ComposerService } from '../../composer/ComposerService';

export class AgentCommClient {
  protected writer: any = null;

  protected reader: any = null;

  //private process: any = null;

  protected responseListeners = new Map<string, (data: any) => void>();

  private useTcp = false;

  private hasListener = false;

  protected messageQueue: string[] = []; // Message queue

  protected queueLock = new Object(); // For message queue synchronization

  private isRunning = true;

  private reconnectDelay = 5000;

  protected isConnected = false;

  protected generatorTypes: string[] = [];

  private excludeMessageTypeList = ['getLastModified', 'listWorkspaceContents'];

  public constructor(protected process: any, protected messageReceiver: IAgentMessageReceiver) {
    process.on('exit', (code: number, signal: NodeJS.Signals | null) => {
      this.shutdown();
    });
    this.initializeConnection();
  }

  public request(
    messageType: string,
    data: any,
    messageId: string | null,
    onResponse?: (data: any) => void
  ): void {
    const id = messageId || AgentCommClient.generateUuid();
    const message = JSON.stringify({
      messageId: id,
      messageType: messageType,
      data: data,
    });
    if (onResponse) {
      this.responseListeners.set(id, onResponse);
    }
    this.write(message);
  }

  public close(): void {
    try {
      this.isRunning = false;

      synchronized(this.queueLock, () => {
        this.messageQueue = [];
        this.responseListeners.clear();
      });

      this.closeResources();
    } catch (e) {
      Logger.error('Error during close {e}');
    }
  }

  public shutdown(): void {
    try {
      this.close();
    } catch (e) {
      Logger.error('Error during shutdown: {e}');
    }
  }

  private isValidJson(json: string): boolean {
    try {
      JSON.parse(json);
      return true;
    } catch (e) {
      return false;
    }
  }

  public write(message: string): void {
    synchronized(this.queueLock, () => {
      if (this.isConnected && this.writer) {
        try {
          this.writer.write(message + '\r\n');
          this.writer.flush();
        } catch (e) {
          Logger.warn(`Error writing to Continue core: ${String(e)}`);
          this.messageQueue.push(message);
          this.handleConnectionFailure();
        }
      } else {
        this.messageQueue.push(message);
        if (!this.isConnected) {
          this.initializeConnection();
        }
      }
    });
  }

  private handleConnectionFailure(): void {
    synchronized(this.queueLock, () => {
      this.isConnected = false;
      this.closeResources();
      // Reconnect in new thread to avoid blocking
      setTimeout(() => {
        this.initializeConnection();
      }, 0);
    });
  }

  private processMessageQueue(): void {
    synchronized(this.queueLock, () => {
      if (this.isConnected && this.writer) {
        const iterator = this.messageQueue.values();
        for (const message of iterator) {
          try {
            this.writer.write(message + '\r\n');
            this.writer.flush();
            this.messageQueue = this.messageQueue.filter(m => m !== message);
          } catch (e) {
            Logger.error(`Failed to process queued message: ${e}`);
            this.handleConnectionFailure();
            break;
          }
        }
      }
    });
  }

  private initializeConnection(): void {
    while (this.isRunning && !this.isConnected) {
      try {
        Logger.debug('Attempting to establish connection...');
        if (this.useTcp && this.messageReceiver instanceof ComposerService) {
          this.initTcpConnection();
        } else {
          this.initProcessConnection();
        }

        this.isConnected = true;
        Logger.debug('Connection established successfully');

        this.startReading();
        this.processMessageQueue();
        break;
      } catch (e) {
        Logger.error(`Connection attempt failed:${e}`);
        setTimeout(() => { }, this.reconnectDelay);
      }
    }
  }

  public async handleMessage(json: string): Promise<void> {
    try {
      if (!this.isValidJson(json)) {
        return;
      }

      const responseMap = JSON.parse(json);
      const messageId = responseMap.messageId.toString();
      const messageType = responseMap.messageType.toString();
      const data = responseMap.data;
      // Log message info
      if (messageType === 'indexProgress') {
        Logger.debug(`[AgentCommClient] 索引进度: ${JSON.stringify(data)}`);
      } else if (messageType === 'api/chat') { 
        if (data.done) { 
          Logger.info(`[AgentCommClient] Received messageType: ${messageType}, messageId: ${messageId}, message: ${JSON.stringify(data)}`);
        }
      } else {
        if (this.excludeMessageTypeList.includes(messageType)) {
          Logger.info(`[AgentCommClient] Received messageType: ${messageType}, messageId: ${messageId}`);
        } else { 
          Logger.info(`[AgentCommClient] Received messageType: ${messageType}, messageId: ${messageId}, message: ${JSON.stringify(data)}`);
        }
      }

      // 等待异步消息处理完成
      await this.messageReceiver.onAgentMessageHandler(json);

      // Handle response listeners
      const listener = this.responseListeners.get(messageId);
      if (listener) {
        listener(data);
        if (this.generatorTypes.includes(messageType)) {
          const done = (data as { done?: boolean }).done;
          if (done === true) {
            this.responseListeners.delete(messageId);
          }
        } else {
          this.responseListeners.delete(messageId);
        }
      }
    } catch (error) {
      Logger.error(`Error in async message handling: ${error}`);
    }
  }

  private initTcpConnection(): void {
    const socket = new net.Socket();
    socket.connect(3000, '127.0.0.1');
    this.writer = {
      write: (data: string) => socket.write(data),
      flush: () => { },
    };
    this.reader = socket;
  }

  private initProcessConnection(): void {
    try {
      this.writer = {
        write: (data: string) => {
          if (this.process && this.process.stdin && !this.process.stdin.destroyed) {
            return this.process.stdin.write(data);
          } else {
            Logger.error('Process stdin is not available');
            return false;
          }
        },
        flush: () => {
          if (this.process && this.process.stdin && !this.process.stdin.destroyed) {
            this.process.stdin.uncork();
          }
        }
      };

      if (this.process && this.process.stdout) {
        this.reader = this.process.stdout;
      } else {
        Logger.error('Process stdout is not available');
        throw new Error('Process stdout is not available');
      }

      if (this.process) {
        this.process.on('error', (err: Error) => {
          Logger.error(`Process error: ${err.message}`);
          this.handleConnectionFailure();
        });

        this.process.on('exit', (code: number) => {
          Logger.warn(`Process exited with code: ${code}`);
          this.handleConnectionFailure();
        });

        if (this.process.stdin) {
          this.process.stdin.on('error', (err: Error) => {
            Logger.error(`Process stdin error: ${err.message}`);
            this.handleConnectionFailure();
          });
        }

        if (this.process.stdout) {
          this.process.stdout.on('error', (err: Error) => {
            Logger.error(`Process stdout error: ${err.message}`);
            this.handleConnectionFailure();
          });
        }
      } else {
        throw new Error('Process is not available');
      }
    } catch (e) {
      Logger.error(`Failed to initialize process: ${e}`);
      throw e;
    }
  }

  private closeResources(): void {
    synchronized(this.queueLock, () => {
      try {
        this.isRunning = false;
        this.isConnected = false;

        const closePromise = new Promise<void>((resolve, reject) => {
          try {
            if (this.writer) {
              this.writer.flush?.();
              this.writer = null;
            }

            if (this.reader) {
              this.reader = null;
            }

            resolve();
          } catch (e) {
            reject(e);
          }
        });

        Promise.race([
          closePromise,
          new Promise((_, reject) => setTimeout(() => reject(new Error('Shutdown timeout')), 5000)),
        ]).catch(e => {
          Logger.error(`Error during resource cleanup: ${e}`);
          if (this.process) {
            try {
              this.process.kill('SIGKILL');
            } catch (killError) {
              Logger.error(`Error killing process: ${killError}`);
            }
          }
        });
      } catch (e) {
        Logger.error(`Error during resource cleanup: ${e}`);
      }
    });
  }

  private startReading(): void {
    if (!this.reader) {
      Logger.error('Reader is not initialized');
      return;
    }
    const readline = require('readline');
    const rl = readline.createInterface({
      input: this.reader,
      crlfDelay: Infinity
    });

    // 监听行数据
    rl.on('line', async (line: string) => {
      if (!this.isRunning || !this.isConnected) {
        return;
      }

      if (line && line.trim() !== '') {
        try {
          await this.handleMessage(line);
        } catch (e) {
          Logger.error(`Error handling message: ${line}, ${e}`);
        }
      } else {
        // 对应Java中的空行处理
        setTimeout(() => { }, 100);
      }
    });

    // 监听关闭事件，相当于Java中的null检测
    rl.on('close', () => {
      Logger.warn('Stream closed');
      this.handleConnectionFailure();
    });

    // 监听错误
    rl.on('error', (err: Error) => {
      Logger.error(`Error in read loop: ${err.message}`);
      this.handleConnectionFailure();
      rl.close();
    });

    // 确保进程结束时关闭readline接口
    if (this.process) {
      this.process.on('exit', () => {
        rl.close();
      });
    }
  }

  public static generateUuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  public getRunning(): boolean {
    return this.isRunning
  }
}

// Helper function to mimic synchronized blocks
function synchronized(lock: any, fn: () => void) {
  fn();
}
