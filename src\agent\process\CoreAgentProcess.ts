import { ChildProcess } from "child_process";
import { AgentProcess } from "./AgentProcess";
import { spawn } from 'child_process';
import { AgentConfig, CORE_AGENT } from "../types";
import { SystemUtils } from '../utils/system';
import * as fs from 'fs';
import * as path from 'path'
import { Logger } from "../../utils/logger";

// 定义 CoreAgentProcess 类，继承自 AgentProcess 类
export class CoreAgentProcess extends AgentProcess {

  public constructor() {
    super();
  }

  public buildProcess(agentPath: string, port: number, config: AgentConfig): ChildProcess {
    const baseBrand = process.env.ISSEC !== 'false' ? 'oscap' : 'codefree';

    // todo: core agent配置
    const nodePath = SystemUtils.getAgentPath(baseBrand, 'node');
    const basePath = SystemUtils.getAgentBasePath(baseBrand)
    SystemUtils.setPermission(nodePath);
    SystemUtils.removeQuarantine(basePath)
    const childProcess = spawn(nodePath, [agentPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        LANG: 'en_US.UTF-8',
        LC_ALL: 'en_US.UTF-8',
        NODE_SKIP_PLATFORM_CHECK: '1',
        "apiKey": config.apiKey,
        "invokerId": config.invokerId,
        "pluginType": config.pluginType,
        "pluginVersion": config.pluginVersion,
        "clientType": config.clientType,
        "clientVersion": config.clientVersion,
        "serverType": config.serverType,
        "serverBaseUrl": config.serverBaseUrl,
        "embeddingSubservice": config.embeddingSubservice,
        "rerankSubservice": config.rerankSubservice,
        "composerSubservice": config.composerSubservice,
        "indexVersion": config.indexVersion,
      },
      detached: false,
      cwd: path.dirname(agentPath)
    });

    this.process = childProcess;
    Logger.debug(`[coreAgentProcess] start agent pid: ${childProcess.pid}`);

    return childProcess;
  }


  public getAgentName(): string {
    return CORE_AGENT;
  }

  public isAgentRunning(): boolean {
    if (!this.process) {
      return false;
    }

    try {
      // 发送信号0来测试进程是否存在
      process.kill(this.process.pid!, 0);
      return true;
    } catch (error) {
      return false;
    }
  }
}
