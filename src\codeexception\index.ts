import * as vscode from 'vscode';
import ExceptionCodeAcitonProvider from './exceptionCodeAcitonProvider';
import { IStatusBarHandler } from '../statusbar/types';
import { ChatMessageType, ChatMessageTypeDesc, SrdCommand } from '../common/constants';

/**
 * 注册ActivityBar的ChatView
 * @param context Extension context
 */
export function registerCodeExceptionFix(
  context: vscode.ExtensionContext,
  statusHandler: IStatusBarHandler
) {
  const provider = new ExceptionCodeAcitonProvider();

  /**
   * 注册WebViewProvider
   */
  context.subscriptions.push(
    vscode.languages.registerCodeActionsProvider({ pattern: '**/*' }, provider, {
        providedCodeActionKinds: [vscode.CodeActionKind.QuickFix]
    })
  );
}
