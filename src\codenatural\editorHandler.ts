import {
  Position,
  TextEditor,
  Disposable,
  TextEditorEdit,
  window,
  Range,
  TextEditorDecorationType,
  TextDocument,
  workspace,
  WorkspaceEdit,
  EndOfLine,
  DecorationRangeBehavior,
} from 'vscode';
import { CodeNaturalEventType, CodeSnippet, ICodeNaturalEventHandler } from './types';
import { Logger } from '../utils/logger';
import { END_OF_LINE } from '../common/config';

/**
 * 处理Editor生成代码片断
 */
export default class EditorHandler implements Disposable {
  private eventHandler: ICodeNaturalEventHandler;

  private reqId: string;

  private editor: TextEditor;

  private document: TextDocument;

  private startPosition: Position;

  private cursorPosition: Position;

  private timerId: NodeJS.Timer | undefined;

  private codeSnippets: CodeSnippet[];

  private codeSnippetDecorationType: TextEditorDecorationType;

  public constructor(
    eventHandler: ICodeNaturalEventHandler,
    reqId: string,
    editor: TextEditor,
    startPosition: Position
  ) {
    this.eventHandler = eventHandler;
    this.reqId = reqId;
    this.editor = editor;
    this.document = editor.document;
    this.startPosition = startPosition;
    this.cursorPosition = startPosition;
    this.codeSnippets = [];
    this.timerId = undefined;
    this.codeSnippetDecorationType = this.getCodeSnippetDecorationType();
  }

  public dispose() {
    throw new Error('Method not implemented.');
  }

  public setEditor(editor: TextEditor) {
    this.editor = editor;
    this.document = editor.document;
  }

  /**
   * 添加代码片断
   * @param isEnd
   * @param answer
   */
  public addCodeSnippet(isEnd: number, answer = '') {
    if (!this.reqId) {
      return;
    }

    this.codeSnippets.push({
      answer,
      isEnd,
    });
  }

  /**
   * 展示代码片断到编辑器
   * @returns
   */
  public async startDisplayCodeSnippet() {
    if (!this.reqId) {
      return;
    }

    // 空白文件生成代码时，修改换行符为'\n';
    if (this.document.getText().trim().length === 0) {
      await this.setEndOfLine();
    }

    let isDisplayEndSnippet = false;

    while (this.codeSnippets.length > 0) {
      const codeSnippet = this.codeSnippets.shift();

      if (codeSnippet) {
        const { answer, isEnd } = codeSnippet;

        if (this.cursorPosition) {
          this.cursorPosition = await this.insertCodeSnippet(answer);
        }

        if (isEnd) {
          isDisplayEndSnippet = true;
        }
      }
    }

    if (!isDisplayEndSnippet) {
      this.timerId = setTimeout(async () => await this.startDisplayCodeSnippet(), 100);
    } else {
      clearTimeout(this.timerId);

      this.cursorPosition = await this.addExtraNewLine();
      this.eventHandler.handleEvent(CodeNaturalEventType.TASK_ON_ANSWER_END, {
        reqId: this.reqId,
        endPosition: this.cursorPosition,
        answer: END_OF_LINE,
      });
    }
  }

  /**
   * 插入代码片断
   * @param editor
   * @param startPosition
   * @param codeSnippet
   * @returns
   */
  public async insertCodeSnippet(answer = '', withDecorations = true): Promise<Position> {
    const lastPosition = this.cursorPosition;
    let endPosition = this.cursorPosition;

    const offset = this.document.offsetAt(this.cursorPosition);

    if (!answer) {
      return endPosition;
    }

    try {
      const weditor = new WorkspaceEdit();
      weditor.insert(this.document.uri, this.cursorPosition, answer);
      await workspace.applyEdit(weditor);

      endPosition = this.document.positionAt(offset + answer.length);

      if (withDecorations) {
        this.editor.setDecorations(this.codeSnippetDecorationType, [
          { range: new Range(this.startPosition, endPosition) },
        ]);
      } else {
        this.editor.setDecorations(this.codeSnippetDecorationType, [
          { range: new Range(this.startPosition, lastPosition) },
        ]);
      }
    } catch (e) {
      // 切换tab时，this.editor会失效，背景色会停止渲染。此处需要catch住
      // 切换回来时，背景色会恢复到最新代码块
    }

    return endPosition;
  }

  /**
   * 接受代码片断
   * @param startPosition
   * @param endPosition
   */
  public async acceptCodeSnippet(answer = '', endPosition?: Position) {
    if (endPosition) {
      this.cursorPosition = endPosition;
    }

    this.editor.setDecorations(this.codeSnippetDecorationType, []);
    await this.editor.edit((editorBuilder: TextEditorEdit) => {
      editorBuilder.replace(new Range(this.startPosition, this.cursorPosition), answer);
    });
  }

  /**
   * 拒绝代码片断
   * @param startPosition
   * @param endPosition
   */
  public async rejectCodeSnippet(endPosition?: Position) {
    if (endPosition) {
      this.cursorPosition = endPosition;
    }

    this.editor.setDecorations(this.codeSnippetDecorationType, []);
    await this.editor.edit((editorBuilder: TextEditorEdit) => {
      editorBuilder.replace(new Range(this.startPosition, this.cursorPosition), '');
    });
  }

  /**
   * 恢复代码片断背景色
   */
  public recoveryCodeSnippet() {
    this.editor.setDecorations(this.codeSnippetDecorationType, [
      {
        range: new Range(this.startPosition, this.cursorPosition),
      },
    ]);
  }

  /**
   * Code背景色
   * @returns
   */
  private getCodeSnippetDecorationType() {
    return window.createTextEditorDecorationType({
      backgroundColor: 'rgba(21, 66, 24, 1)',
      isWholeLine: true,
      rangeBehavior: DecorationRangeBehavior.OpenClosed,
    });
  }

  /**
   * 修改文件的换行符为'\n'
   */
  private async setEndOfLine() {
    await this.editor.edit(editBuilder => {
      editBuilder.setEndOfLine(EndOfLine.LF);
    });
  }

  /**
   * 答案回显后再另起一行，以防最后几个片断都为空时，编辑器没有变更，导致codeLens不变化
   * 暂时如此处理，有更好的办法再说
   * @returns
   */
  private async addExtraNewLine() {
    return await this.insertCodeSnippet(END_OF_LINE, false);
  }
}
