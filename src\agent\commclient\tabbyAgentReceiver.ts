import { IAgentMessageReceiver } from './MessageReceiver';
import { Logger } from '../../utils/logger';

/**
 * Tabby Agent 消息接收器
 * 处理来自 Tabby Agent 的消息
 */
export class TabbyAgentMessageReceiver implements IAgentMessageReceiver {
  // 可以注入依赖服务
  constructor(private readonly serviceId?: string) { }

  /**
   * 处理 Agent 消息
   * @param text 消息内容 JSON 字符串
   */
  public async onAgentMessageHandler(text: string): Promise<any> {

    Logger.warn(`[TabbyAgent] 消息: ${text}`);

    text = text.trim();
    if (!text || !text.trim()) {
      return;
    }

    try {
      // 处理旧格式消息 [id, data], 目前都是这种格式
      if (text.startsWith('[') && text.endsWith(']')) {
        this.handleLegacyFormatMessage(text);
        return;
      }

      // 处理新格式消息 {messageId, messageType, data} ，暂时write和read都是[xxx]行，而不是{xx}, 所以这个暂时没启用
      if (text.startsWith('{') && text.endsWith('}')) {
        this.handleNewFormatMessage(text);
        return;
      }

      // 不支持的格式
      // Logger.warn(`[TabbyAgent] 不支持的消息格式: ${text}`);
    } catch (error) {
      Logger.error(`[TabbyAgent] 处理消息时出错: ${error}`);
    }
  }

  /**
   * 处理旧格式消息 [id, data]
   */
  private handleLegacyFormatMessage(text: string): void {
    const parsed = JSON.parse(text);
    const id = parsed[0];
    const data = parsed[1];

    // 处理事件消息
    if (id === 0) {
      this.handleEventMessage(data);
    } else {
      // 处理响应消息
      Logger.debug(`[TabbyAgent] 收到响应消息 ID: ${id}, 数据: ${JSON.stringify(data)}`);
    }
  }

  /**
   * 处理新格式消息 {messageId, messageType, data}
   */
  private handleNewFormatMessage(text: string): void {
    const message = JSON.parse(text);
    const messageId = message.messageId?.toString();
    const messageType = message.messageType?.toString();
    const data = message.data;

    // 记录消息信息
    Logger.debug(`[TabbyAgent] 消息类型: ${messageType}, 数据: ${JSON.stringify(data)}`);

    // 根据消息类型处理不同的消息
    switch (messageType) {
      case 'completion':
        this.handleCompletionMessage(data);
        break;
      case 'statusChanged':
        this.handleStatusChangedMessage(data);
        break;
      case 'configUpdated':
        this.handleConfigUpdatedMessage(data);
        break;
      case 'issuesUpdated':
        this.handleIssuesUpdatedMessage(data);
        break;
      default:
        // 处理其他类型的消息
        this.handleGenericMessage(messageType, data);
        break;
    }
  }

  /**
   * 处理事件消息（旧格式）
   */
  private handleEventMessage(data: any): void {
    if (typeof data === 'object' && data.event) {
      if (data.event === "statusChanged") {
        Logger.debug(`[TabbyAgent] 状态变更: ${JSON.stringify(data)}`);
      }
      else if (data.event === "configUpdated") {
        Logger.debug(`[TabbyAgent] 配置更新: ${JSON.stringify(data)}`);
      }
      else if (data.event === "authRequired") {
        Logger.debug(`[TabbyAgent] 需要认证`);
      }
      else if (data.event === "issuesUpdated") {
        Logger.debug(`[TabbyAgent] 问题更新: ${JSON.stringify(data)}`);
      }
    } else if (data === "notInitialized") {
      Logger.debug(`[TabbyAgent] 未初始化`);
    } else if (data === "ready") {
      Logger.debug(`[TabbyAgent] 就绪`);
    }
  }

  /**
   * 处理补全消息
   */
  private handleCompletionMessage(data: any): void {
    // 实现补全消息的处理逻辑
    Logger.debug(`[TabbyAgent] 处理补全消息: ${JSON.stringify(data)}`);
  }

  /**
   * 处理状态变更消息
   */
  private handleStatusChangedMessage(data: any): void {
    // 实现状态变更消息的处理逻辑
    Logger.debug(`[TabbyAgent] 状态变更: ${JSON.stringify(data)}`);
  }

  /**
   * 处理配置更新消息
   */
  private handleConfigUpdatedMessage(data: any): void {
    // 实现配置更新消息的处理逻辑
    Logger.debug(`[TabbyAgent] 配置更新: ${JSON.stringify(data)}`);
  }

  /**
   * 处理问题更新消息
   */
  private handleIssuesUpdatedMessage(data: any): void {
    // 实现问题更新消息的处理逻辑
    Logger.debug(`[TabbyAgent] 问题更新: ${JSON.stringify(data)}`);
  }

  /**
   * 处理通用消息
   */
  private handleGenericMessage(messageType: string, data: any): void {
    // 处理其他类型的消息
    Logger.debug(`[TabbyAgent] 收到消息类型: ${messageType}, 数据: ${JSON.stringify(data)}`);
  }
} 