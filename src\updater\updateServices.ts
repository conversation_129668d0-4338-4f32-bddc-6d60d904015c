import axios from 'axios';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';
import { getAddress } from '../settings/settingsGetter';
import { userInfoManager } from '../login/loginUtils';

export interface VersionCheckResponse {
  code: number;
  hasUpdate: boolean;
  latestVersion: string;
  downloadUrl?: string;
  changelog?: string;
  message?: string;
}

export async function checkNewVersion(currentVersion: string): Promise<VersionCheckResponse> {
  //校验是否登录
  if (!userInfoManager.isAuthorized()) {
    return {
      code: 1,
      hasUpdate: false,
      latestVersion: currentVersion,
      message: '检查更新失败，您尚未登录或者没有授权',
    };
  }

  const baseAddress = getAddress();
  // const baseAddress = "http://127.0.0.1:7872"; // todo 本地测试用，用完注释掉

  const productType = currentVersion.slice(currentVersion.indexOf('-') + 1);
  const version = currentVersion.slice(0, currentVersion.indexOf('-'));
  const url = `${baseAddress}/api/check_plugin_version?version=${version}&ide=vscode&productType=${productType}`;

  try {
    // 创建一个 HTTPS agent，设置 rejectUnauthorized 为 false
    const agent = new https.Agent({
      rejectUnauthorized: false, // 忽略 SSL 证书验证
    });

    const response = await axios.get<VersionCheckResponse>(url, {
      httpsAgent: agent,
      timeout: 10000,
    });
    // const response = await axios.get<VersionCheckResponse>(url, {httpsAgent: agent}); // todo  注释掉超时时间测试
    return response.data;
  } catch (error) {
    console.log(error);
    return {
      code: 1,
      hasUpdate: false,
      latestVersion: currentVersion,
      message: '检查更新失败，请检查服务器地址配置',
    };
  }
}

export async function downloadNewVersion(
  saveDir: string,
  currentVersion: string
): Promise<boolean> {
  const baseAddress = getAddress();
  // const baseAddress = "http://127.0.0.1:7872"; // 本地测试
  const productType = currentVersion.slice(currentVersion.indexOf('-') + 1);
  const url = `${baseAddress}/api/download/vscode/${productType}`;

  const agent = new https.Agent({
    rejectUnauthorized: false,
  });

  try {
    const response = await axios.get(url, {
      httpsAgent: agent,
      timeout: 600000, // 超时时间设置为10分钟（600000毫秒）
      responseType: 'stream', // 设置响应类型为流
    });

    // 尝试从响应头中获取文件名
    let fileName = '';
    const disposition = response.headers['content-disposition'];
    if (disposition && disposition.indexOf('attachment') !== -1) {
      const matches = disposition.match(/filename[^;=\n]*=((['"]).*?\2|([^;\n]*))/);
      if (matches != null && matches[1]) {
        fileName = matches[1].replace(/['"]/g, ''); // 去除引号
      }
    }

    // 如果没有从响应头中获取到文件名，则使用 URL 中的文件名
    if (!fileName) {
      const urlObject = new URL(url);
      fileName = path.basename(urlObject.pathname);
    }

    const savePath = path.join(saveDir, fileName);
    const writer = fs.createWriteStream(savePath);

    return new Promise(resolve => {
      response.data.pipe(writer);
      writer.on('finish', () => resolve(true));
      writer.on('error', () => resolve(false));
    });
  } catch (error) {
    // 处理 Axios 错误
    if (axios.isAxiosError(error)) {
      console.error('[secidea] Error message:', error.message);
    } else {
      // 处理其他类型的错误
      console.error('[secidea] Unexpected error:', error);
    }
    return false;
  }
}
