import { CHANNEL_TYPE } from '../common/config';
import { ChannelStatus, ChannelType, MessageName, RtnCode } from '../common/constants';
import CodeAIRequestReceiver from '../service/codeAIRequestReceiver';
import { CodeAIResponseMessage } from '../service/types/codeAI';
import { ICommChannelEvent } from './types/codeAIComm';
import { SendMessageRtn } from './types/webSocket';
import WebSocketClient from './webSocketClient';

/**
 * 支持多种通道与服务器建立连接
 */
export default class CodeAICommHandler implements ICommChannelEvent {
  private static instance: CodeAICommHandler | null = null;

  private wsChannel: WebSocketClient | null = null;

  private hasRegisterChannel = false;

  private registerMessage = '';

  public constructor(wsPath?: string) {
    if (CHANNEL_TYPE === ChannelType.WS) {
      this.wsChannel = new WebSocketClient(this, wsPath);
    }
  }

  public static getInstance(wsPath?: string): CodeAICommHandler {
    if (this.instance === null) {
      this.instance = new CodeAICommHandler(wsPath);
    }

    return this.instance;
  }

  /**
   * 获取通道状态
   * @returns
   */
  public getChannelStatus() {
    switch (CHANNEL_TYPE) {
      case ChannelType.WS:
        return this.wsChannel ? this.wsChannel.getChannelStatus() : ChannelStatus.DISCONNECTED;
      default:
        return ChannelStatus.DISCONNECTED;
    }
  }

  /**
   * 注册通道
   * @param message
   * @returns
   */
  public async registerToCodeAI(message: string): Promise<SendMessageRtn | null> {
    switch (CHANNEL_TYPE) {
      case ChannelType.WS:
        if (this.wsChannel && this.getChannelStatus() === ChannelStatus.DISCONNECTED) {
          const rs = this.wsChannel.connectToServer();
          this.registerMessage = message;

          return new SendMessageRtn(rs);
        }

        return this.sendMessageToCodeAI(message);
      default:
        return null;
    }
  }

  /**
   * 发送消息到通道
   * @param message
   * @returns
   */
  public sendMessageToCodeAI(message: string): SendMessageRtn | null {
    switch (CHANNEL_TYPE) {
      case ChannelType.WS:
        if (!this.wsChannel || this.getChannelStatus() !== ChannelStatus.CONNECTED) {
          return new SendMessageRtn(RtnCode.NO_CHANNEL);
        }

        return this.wsChannel.sendMessage(message);
      default:
        return null;
    }
  }

  /**
   * 断开通道连接
   */
  public disconnect() {
    switch (CHANNEL_TYPE) {
      case ChannelType.WS:
        if (this.wsChannel) {
          this.wsChannel.disconnect();
        }
        break;
      default:
        break;
    }
  }

  /**
   * 处理通道接收的消息
   * @param incomMsg msg
   */
  public onCommChannelIncomMessage(incomMsg: unknown): void {
    const message = incomMsg as CodeAIResponseMessage;
    switch (message.messageName) {
      case MessageName.REGISTER_CHANNEL_RESP:
        if (message.context.optResult === RtnCode.SUCCESS) {
          this.hasRegisterChannel = true;
        }
        break;
      default:
        break;
    }

    CodeAIRequestReceiver.onReceiveMessageFromCodeAI(message);
  }

  /**
   * 处理通道状态变化
   * @param event
   * @param isTerminate
   */
  public onCommChannelEvent(event: number, isTerminate = false): void {
    switch (event) {
      case ChannelStatus.CONNECTED:
        if (!this.hasRegisterChannel && this.registerMessage) {
          this.sendMessageToCodeAI(this.registerMessage);
          CodeAIRequestReceiver.onWSReconnect(isTerminate, RtnCode.SUCCESS);
        }
        break;
      case ChannelStatus.DISCONNECTED:
        this.hasRegisterChannel = false;
        CodeAIRequestReceiver.onTaskErrorOrClose(isTerminate, RtnCode.NO_CHANNEL);
        break;
      default:
        break;
    }
  }
}
