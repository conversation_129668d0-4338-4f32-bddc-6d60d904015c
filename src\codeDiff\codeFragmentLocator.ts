import Fuse from 'fuse.js';

interface CodeLocation {
  startLine: number;
  endLine: number;
  indentation: string;
}

export default class CodeFragmentLocator {
  private fuse: Fuse<string>;
  private sourceLines: string[];

  constructor(sourceCode: string) {
    this.sourceLines = sourceCode.split('\n');
    const options = {
      includeScore: true,
      includeMatches: true,
      threshold: 0.3
    };

    this.fuse = new Fuse(this.sourceLines, options);
  }

  /**
   * 定位代码片段
   */
  locate(fragment: string): CodeLocation | null {
    const fragmentLines = fragment.trim().split('\n');
    const firstLine = fragmentLines[0].trim();

    // 查找第一行的位置
    const results = this.fuse.search(firstLine);
    if (!results.length) {
      return null;
    }

    const startLine = results[0].refIndex;

    // 验证后续行
    const endLine = this.validateFragment(
      startLine,
      fragmentLines
    );

    if (!endLine) {
      return null;
    }

    return {
      startLine,
      endLine,
      indentation: this.getIndentation(this.sourceLines[startLine])
    };
  }

  /**
   * 验证代码片段匹配
   */
  private validateFragment(
    startLine: number,
    fragmentLines: string[]
  ): number | null {
    const normalizedFragment = fragmentLines
      .map(line => line.trim())
      .join('\n');

    const potentialMatch = this.sourceLines
      .slice(startLine, startLine + fragmentLines.length)
      .map(line => line.trim())
      .join('\n');

    if (this.isSimilar(potentialMatch, normalizedFragment)) {
      return startLine + fragmentLines.length - 1;
    } else {
      return null;
    }
  }

  /**
   * 检查相似度
   */
  private isSimilar(str1: string, str2: string): boolean {
    const normalized1 = this.normalizeCode(str1);
    const normalized2 = this.normalizeCode(str2);
    return normalized1 === normalized2;
  }

  /**
   * 标准化代码
   */
  private normalizeCode(code: string): string {
    return code
      .replace(/\/\*[\s\S]*?\*\//g, '')  // 移除多行注释
      .replace(/\/\/[^\n]*/g, '')        // 移除单行注释（保留换行符）
      .replace(/\s+/g, '');              // 移除所有空白字符（包括换行）
  }

  /**
   * 获取缩进
   */
  private getIndentation(line: string): string {
    const match = line.match(/^[\s\t]*/);
    return match ? match[0] : '';
  }
}