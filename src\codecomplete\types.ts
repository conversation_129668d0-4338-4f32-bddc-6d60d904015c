import * as vscode from 'vscode';

export enum SuggestionTrigger {
  DocumentChanged = 'DocumentChanged',
  LookAhead = 'LookAhead',
}

export interface ResultEntry {
  new_prefix: string;
  old_suffix: string;
  new_suffix: string;
  answer: string;
}

export interface CompletionArguments {
  currentCompletion: string;
  currentAnswer: string;
  completions: ResultEntry[];
  position: vscode.Position;
  limited: boolean;
  oldPrefix?: string;
  suggestionTrigger?: SuggestionTrigger;
  isAuto?: boolean;
}

export interface AutocompleteResult {
  old_prefix: string;
  results: ResultEntry[];
}

export interface AutocompleteParams {
  filename: string;
  language: string;
  before: string;
  after: string;
  isAuto: boolean;
  line: number;
  character: number;
  importSnippets?: ImportSnippet[];
}

export interface ImportSnippet {
  filePath: string;
  snippet: string;
}

export interface CodeCompleteAnswer {
  reqId: string;
  isEnd: number;
  answer: string;
  rtnCode: number;
}

export interface HistoryAnswer {
  answer: string;
  isAuto: boolean;
}

export type UpdatedFile = {
  filePath: string,
  isDirectory: boolean,
  updateType: 'change' | 'create' | 'delete'
}