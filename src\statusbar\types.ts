/**
 * 状态栏状态枚举
 */
export enum StatusBarState {
  // 未登录，点击触发Login命令
  NotLogin = 0,
  // 正在登录，点击触发正在登录中提示语，可取消
  Logining = 1,
  // 登录成功且启用了自动补全功能
  CodeCompleteEnabled = 3,
  // 登录成功且禁用了自动补全功能
  CodeCompleteDisabled = 4,
  // 触发了代码自动补全请求， 等待请求完成
  WaitingAutoComplete = 5,
  // 触发了代码手动补全请求， 等待请求完成
  WaitingManualComplete = 6,
  // WS服务连接异常
  WSServerError = 7,
  // 同步服务中
  SyncingAgent,
  // 同步服务失败
  AgentSyncFailed,
  // 服务启动失败
  AgentStartupFailed,
  // 索引进度
  CodeIndexing
}

export interface IStatusBarHandler {
  onCodeCompleteStatusChange(status: number, data?: unknown): void;
  onCodeChatStatusChange(code: number): void;
  onAgentStatusChange(status: number, data?: unknown): void;
}
