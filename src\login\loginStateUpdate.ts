import * as fs from 'fs';
import { loginStoreFile, splitText, decrypt, userInfoManager, UserInfo } from './loginUtils';
import { LoginServiceInst } from '../service/loginService';
import { EventType } from '../service/types/login';
import * as vscode from 'vscode';
import { SrdCommand } from '../common/constants';

export function listenAuthFile() {
  const watcher = fs.watch(loginStoreFile, (eventType: string) => {
    if (eventType === 'change') {
      try {
        const encryptedText = fs.readFileSync(loginStoreFile, 'utf-8');
        const encryptedTexts = encryptedText.split(splitText);
        const userInfo = decrypt(encryptedTexts[0]);
        const address = encryptedTexts[1] || '';
        if (
          address === userInfoManager.address &&
          UserInfo.equals(userInfo, userInfoManager.getUserInfo())
        ) {
          return;
        } else if (
          address !== userInfoManager.address &&
          UserInfo.equals(userInfo, userInfoManager.getUserInfo())
        ) {
          // address更新，用户未更新（说明是未登录）
          userInfoManager.updateUserInfoAndAddress(userInfo, address, true);
        } else if (userInfo.userId !== '') {
          // 未登录 -> 登录
          userInfoManager.updateUserInfoAndAddress(userInfo, address, true);
          LoginServiceInst.handleEvent(EventType.USER_INFO_RECEIVED, userInfo);
        } else {
          // 登录 -> 未登录
          userInfoManager.updateUserInfoAndAddress(userInfo, address, true);
          vscode.commands.executeCommand(SrdCommand.SECIDEA_LOGOUT);
        }
      } catch (e) {
        console.error('watching auth file error', e);
      }
    }
  });

  watcher.on('error', (error: Error) => {
    console.error('File watching error:', error);
  });

  return watcher;
}
