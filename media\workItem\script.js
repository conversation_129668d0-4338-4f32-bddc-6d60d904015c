(function() {
  // ==================== 环境检测和初始化 ====================
  
  // Check if running in JetBrains IDE
  const isJet = !(window.acquireVsCodeApi);
  
  // Initialize vscode/IDE API
  let vscode = null;
  if (!isJet) {
    vscode = acquireVsCodeApi();
  } else {
    // JetBrains IDE 环境，加载特定样式文件
    const additionalStyles = ['/vscode-var.css', '/vscode-var-dark.css'];
    
    additionalStyles.forEach(href => {
      const existingLink = document.querySelector(`link[href="${href}"]`);
      if (!existingLink) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.crossOrigin = 'anonymous';
        link.href = href;
        document.head.appendChild(link);
      }
    });
  }

  // ==================== DOM 元素引用 ====================
  
  const loadingIndicator = document.getElementById('loadingIndicator');
  const noItemsMessage = document.getElementById('noItemsMessage');
  const workItemsList = document.getElementById('workItemsList');
  const searchInput = document.getElementById('searchInput');
  const workItemsContainer = document.getElementById('workItems');
  const loadMoreIndicator = document.getElementById('loadMoreIndicator');
  const confirmButton = document.querySelector('.confirm-button');

  // ==================== 全局变量 ====================
  
  const ITEMS_PER_PAGE = 10;
  let allWorkItems = [];
  let itemsToDisplay = [];
  let currentPage = 1;
  let isLoadingMore = false;
  let searchTimeout;
  let resizeTimeout; // For debouncing resize events

  // ==================== 工具函数 ====================
  
  // Toast 显示功能
  function showToast(message, type = 'error') {
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => toast.classList.add('toast-show'), 10);
    
    setTimeout(() => {
      toast.classList.remove('toast-show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  // 简化的 postMessage
  function postMessage(msg) {
    if (!isJet) {
      vscode.postMessage(msg);
    } else {
      window.workItemQuery({
        request: JSON.stringify(msg),
        persistent: false,
        onSuccess: function(response) {
          window.postMessage("");
        },
        onFailure: function(error_code, error_message) {
          console.log(error_code + ":" + error_message);
        }
      });
    }
  }

  // Promise 版本的请求函数
  function postRequest(message) {
    return new Promise((resolve, reject) => {
      const handleResponse = ({ data: payload }) => {
        if (payload.command === message._command && 
            payload.data.reqType === message.data.reqType) {
          resolve(payload);
          window.removeEventListener('message', handleResponse);
        }
      };
      
      window.addEventListener('message', handleResponse);
      
      setTimeout(() => {
        window.removeEventListener('message', handleResponse);
        reject(new Error('Request timeout'));
      }, 60000);
      
      postMessage(message);
    });
  }

  // 应用主题
  function applyTheme(payload) {
    const theme = (payload.data.theme || '').toLowerCase();
    document.body.setAttribute('arco-theme', theme.includes('light') ? 'white' : 'dark');
  }

  // ==================== 数据显示函数 ====================
  
  function appendItemsToDOM(newItems) {
    let itemsHTML = '';
    for (const item of newItems) {
      itemsHTML += `
        <div class="item">
          <div class="item-checkbox">
            <input type="checkbox" id="${item.id}" value="${item.workItemKey}">
          </div>
          <div class="item-id">${item.workItemKey}</div>
          <div class="item-title" title="${item.title}">${item.title}</div>
          <div class="item-creator">${item.creatorDisplayName}</div>
          <div class="item-link">
            <a href="#" onclick="viewWorkItemDetail('${item.url || '#'}'); return false;" class="link-icon" title="查看详情">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14.000116348266602 14" fill="none">
                <path d="M12.2944216796875,1.61581896484375C13.7820216796875,3.10706896484375,13.7820216796875,5.52094896484375,12.2944216796875,7.01219896484375L12.0123216796875,7.29451896484375C11.8039216796875,7.49452896484375,11.4742216796875,7.49120896484375,11.2701216796875,7.28722896484375C11.0659216796875,7.08323896484375,11.0628216796875,6.75333896484375,11.2632216796875,6.54537896484375L11.5452216796875,6.27392896484375C12.6008216796875,5.22166896484375,12.6034216796875,3.51291896484375,11.5510216796875,2.45747896484375C10.4986716796875,1.4020289648437498,8.7899216796875,1.39950896484375,7.7343516796875,2.45190896484375L5.9646316796875,4.22175896484375C4.9329116796875,5.28687896484375,4.9329116796875,6.97852896484375,5.9646316796875,8.043788964843749C6.1491316796875,8.25518896484375,6.1491316796875,8.57064896484375,5.9646316796875,8.78218896484375C5.7488416796875,8.96628896484375,5.4312716796875,8.96628896484375,5.2154916796875,8.78218896484375C3.7278216796875,7.29093896484375,3.7278216796875,4.87718896484375,5.2154916796875,3.38580896484375L6.9852116796875,1.61595896484375C8.442131679687499,0.12788796484374998,10.8374216796875,0.12788796484374998,12.2944216796875,1.61581896484375ZM8.8524816796875,5.24247896484375C10.3401516796875,6.73796896484375,10.3401516796875,9.15423896484375,8.8524816796875,10.64973896484375L7.0827616796875,12.40873896484375C5.5876716796875,13.87333896484375,3.1923416796875,13.86233896484375,1.7110416796875,12.38383896484375C0.22973167968750002,10.90543896484375,0.2138316796875,8.51006896484375,1.6755216796875,7.01232896484375L1.9575716796875,6.73001896484375C2.1659316796875,6.53000896484375,2.4957016796875,6.53318896484375,2.6998116796875,6.73716896484375C2.9039316796875,6.94115896484375,2.9071116796875,7.27118896484375,2.7067116796875,7.47914896484375L2.4246516796875,7.76146896484375C1.3717316796875,8.81664896484375,1.3743816796875001,10.52563896484375,2.4299516796875,11.57803896484375C3.4855316796875,12.63053896484375,5.1942816796875,12.62823896484375,6.2466816796875,11.57263896484375L8.005801679687501,9.80276896484375C9.0369816796875,8.74175896484375,9.0369816796875,7.05262896484375,8.005801679687501,5.99161896484375C7.8212916796875,5.77596896484375,7.8212916796875,5.45825896484375,8.005801679687501,5.24247896484375C8.2576316796875,5.05810896484375,8.6001216796875,5.05810896484375,8.8524816796875,5.24247896484375Z" fill="currentColor"/>
              </svg>
            </a>
          </div>
        </div>
      `;
    }
    workItemsList.insertAdjacentHTML('beforeend', itemsHTML);
  }

  function checkAndLoadMoreIfNeeded() {
    // This function will be called after rendering a page and on window resize.
    requestAnimationFrame(() => {
      if (isLoadingMore) return; // Don't interfere if a load is already in progress

      const stillMoreItemsToLoad = ((currentPage - 1) * ITEMS_PER_PAGE) < itemsToDisplay.length;
      const noScrollbar = workItemsContainer.scrollHeight <= workItemsContainer.clientHeight;

      // console.log('checkAndLoadMoreIfNeeded: stillMoreItemsToLoad:', stillMoreItemsToLoad,
      //             'noScrollbar:', noScrollbar,
      //             '(scrollHeight:', workItemsContainer.scrollHeight, 'clientHeight:', workItemsContainer.clientHeight + ')',
      //             'currentPage:', currentPage);

      if (stillMoreItemsToLoad && noScrollbar) {
        // console.log('checkAndLoadMoreIfNeeded: Conditions met, calling renderNextPage.');
        renderNextPage();
      }
    });
  }

  function renderNextPage() {
    if (isLoadingMore) return;

    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    
    if (startIndex >= itemsToDisplay.length) {
        loadMoreIndicator.style.display = 'none';
        return;
    }

    isLoadingMore = true;
    if (currentPage > 1) {
        loadMoreIndicator.style.display = 'block';
    }

    const pageItems = itemsToDisplay.slice(startIndex, startIndex + ITEMS_PER_PAGE);
    if (pageItems.length > 0) {
        appendItemsToDOM(pageItems);
        currentPage++;
    }
    // Note: noItemsMessage is handled by handleSearch for the initial state.
    // If pageItems is empty here, it means we've reached the end of a non-empty itemsToDisplay list.

    isLoadingMore = false;
    loadMoreIndicator.style.display = 'none';

    // Check if we need to load more items because there's no scrollbar yet
    checkAndLoadMoreIfNeeded();
  }
  
  function handleSearch() {
    const searchTerm = searchInput.value.trim().toLowerCase();
    
    if (searchTerm === '') {
      itemsToDisplay = [...allWorkItems];
    } else {
      itemsToDisplay = allWorkItems.filter(item => {
        return item.workItemKey.toLowerCase().includes(searchTerm) ||
               item.title.toLowerCase().includes(searchTerm) ||
               item.creatorDisplayName.toLowerCase().includes(searchTerm);
      });
    }
    
    currentPage = 1;
    workItemsList.innerHTML = ''; 

    if (itemsToDisplay.length === 0) {
        noItemsMessage.style.display = 'block';
        loadMoreIndicator.style.display = 'none'; 
    } else {
        noItemsMessage.style.display = 'none';
        renderNextPage(); 
    }
  }

  // ==================== 主要功能函数 ====================
  
  async function fetchAndDisplayWorkItems() {
    loadingIndicator.style.display = 'block';
    noItemsMessage.style.display = 'none'; // Hide initially
    workItemsList.innerHTML = '';
    loadMoreIndicator.style.display = 'none';
    isLoadingMore = true;

    try {
      const response = await postRequest({
        command: 'workitem-request',
        _command: 'workitem-response',
        data: {
          reqType: 'searchworkitems',
          searchParam: ''
        }
      });
      

      if (response.data.error) {
        showToast(response.data.error, 'error');
        allWorkItems = [];
      } else {
        allWorkItems = response.data.workItemList || [];
        console.log('Processed allWorkItems:', allWorkItems); // DEBUG
      }
      
    } catch (error) {
      console.error('Failed to fetch/process work items:', error); // DEBUG
      showToast('刷新工作项失败，请重试', 'error');
      allWorkItems = [];
    } finally {
      isLoadingMore = false;
      loadingIndicator.style.display = 'none';
      // itemsToDisplay and rendering are handled by handleSearch
      currentPage = 1; // Reset page state before calling handleSearch
      handleSearch(); // This will set itemsToDisplay and then render or show noItemsMessage
    }
  }

  // 刷新工作项
  window.refreshWorkItems = async function() {
    searchInput.value = '';
    fetchAndDisplayWorkItems();
  };

  // 查看工作项详情
  window.viewWorkItemDetail = function(url) {
    if (url === '#') return;
    
    postMessage({
      command: 'workitem-request',
      data: {
        reqType: 'viewworkitemdetail',
        workItemURL: url
      }
    });
  };

  // 应用选中的工作项
  window.applySelectedItems = function() {
    const selectedCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
    const selectedItems = [];
    
    selectedCheckboxes.forEach(checkbox => {
      const selectedItemId = checkbox.id;
      const selectedItem = allWorkItems.find(item => item.id === selectedItemId);
      if (selectedItem) {
        selectedItems.push(selectedItem);
      }
    });

    // Disable button immediately on click
    if (confirmButton) {
      confirmButton.disabled = true;
      confirmButton.textContent = '正在生成...';
    }
    
    postMessage({
      command: 'workitem-request',
      data: {
        reqType: 'selectworkitem',
        workItemList: selectedItems  // 改为数组形式
      }
    });
  };

  // ==================== 事件监听器 ====================
  
  // 消息监听
  window.addEventListener('message', event => {
    const message = event.data;
    
    if (message.command === 'workitem-response') {
      if (message.data.reqType === 'answerrecved') {
        const { isEnd } = message.data; // 'error' field is no longer expected here

        if (isEnd === 1) { // Operation has ended (success or error)
          if (confirmButton) {
            confirmButton.disabled = false;
            confirmButton.textContent = '生成提交信息';
          }
          // Error display via toast is removed for 'answerrecved' type,
          // as errors are handled by the extension (Git input box / VS Code messages).
        } else if (isEnd === 0) { // Streaming chunk
          // console.log('Streaming answer chunk:', answer);
        }
      } else if (message.data.reqType === 'selectworkitem' || message.data.reqType === 'viewworkitemdetail') {
        // Handle other specific messages if necessary.
        // These might still send an 'error' field for their specific error conditions.
        if (message.data.error) {
          showToast(message.data.error, 'error');
        }
      }
    } else if (message.command === 'push-theme-changed') {
      applyTheme(message);
    }
  });

  // 搜索输入监听
  searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(handleSearch, 300);
  });

  // Scroll listener for lazy loading
  workItemsContainer.addEventListener('scroll', () => {
    if (isLoadingMore || itemsToDisplay.length === 0) { // Don't try to load more if no items or already loading
      return;
    }
    if (workItemsContainer.scrollTop + workItemsContainer.clientHeight >= workItemsContainer.scrollHeight - 100) {
      renderNextPage();
    }
  });

  // Window resize listener
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      // console.log('Window resized, checking if more items can be loaded.');
      checkAndLoadMoreIfNeeded();
    }, 100); // Reduced debounce from 250ms to 100ms
  });

  // 页面加载事件
  window.addEventListener('DOMContentLoaded', () => {
    postMessage({
      command: 'webview-loaded',
      data: {}
    });
  });

  window.onload = function() {
    fetchAndDisplayWorkItems();
  };

})();