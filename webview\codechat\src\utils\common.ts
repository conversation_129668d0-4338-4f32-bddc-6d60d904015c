import { ChatMessageType, SubServiceType } from '@/constants/common';





export function getSubService(type: ChatMessageType): SubServiceType {
  const typeToServiceMap: { [key in ChatMessageType]?: SubServiceType } = {
    [ChatMessageType.MANUAL_GENERATE]: SubServiceType.CODECHAT,
    [ChatMessageType.EXPLAIN]: SubServiceType.CODEEXPLAIN,
    [ChatMessageType.COMMENT]: SubServiceType.CODECOMMENT,
    [ChatMessageType.UNITTEST]: SubServiceType.CODEUNITTEST,
    [ChatMessageType.OPTIMIZATION]: SubServiceType.CODEOPTIMIZATION,
    [ChatMessageType.EXCEPTION_FIX]: SubServiceType.CODEEXCEPTIONFIX,
    [ChatMessageType.KB_ASSISTANT]: SubServiceType.KBASSISTANT

  };

  return typeToServiceMap[type] || SubServiceType.ASSISTANT;
}