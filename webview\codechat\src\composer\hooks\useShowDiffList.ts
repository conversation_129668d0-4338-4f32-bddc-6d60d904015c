import { computed } from "vue";
import { useChatStore } from '@/store/chat';
import { useComposer } from '@/store/composer';

const useShowDiffList = () => {
  const chatStore = useChatStore();
  const composer = useComposer();

  const showDiffList = computed(() => {
    console.log('chatStorehhffrrrr', chatStore.codeFreeLoginStatus, composer.diffList.length, !composer.isAnswering);
    return (chatStore.codeFreeLoginStatus && composer.diffList.length && !composer.isAnswering)
  })

  return {
    showDiffList,
    composer,
    chatStore
  }
}

export default useShowDiffList;
