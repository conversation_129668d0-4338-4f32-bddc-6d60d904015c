import { getCurrentUser } from "../common/globalContext";
import { CLIENT_TYPE } from "../common/constants";
import * as vscode from 'vscode';
import { getExtensionVersion } from "../common/globalContext";
import { AgentConfig } from "../agent/types";
import { getHttpServerHost } from "../utils/envUtil";
export async function generateCoreAgentConfig() {
  const uInfo = await getCurrentUser();
  const apiKey = uInfo.apiKey as string;
  const invokerId = uInfo.userId as string;
  // codefree config
  const config: AgentConfig = {
    apiKey,
    invokerId,
    pluginType: process.env.ISSEC !== 'false' ? 'oscap' : 'codefree',
    pluginVersion: getExtensionVersion(),
    clientType: CLIENT_TYPE,
    clientVersion: vscode.version,
    serverType: process.env.ISSEC !== 'false' ? 'oscap' : 'codefree',
    serverBaseUrl: `${getHttpServerHost()}/api/aebackend`,
    embeddingSubservice: 'emb-composer',
    rerankSubservice: 'rerank-composer',
    composerSubservice: 'codefree-plugin-composer-chat',
    cplSubservice: 'codefree-plugin-cpl'
  }
  // secidea config
  // const config: AgentConfig = {
  //   apiKey: '',
  //   invokerId: '',
  //   pluginType: 'oscap',
  //   pluginVersion: '',
  //   clientType: '',
  //   clientVersion: '',
  //   serverType: "oscap",
  //   serverBaseUrl: 'https://oscap-poc1.out.secidea.com:40081/oscap',
  //   'embedding-subservice': '',
  //   'rerank-subservice': '',
  //   'composer-subservice': '',
  //   'cpl-subservice': '',
  // }
  return config;
}

export async function generateTabbyAgentConfig() {
  const uInfo = await getCurrentUser();
  const apiKey = uInfo.apiKey as string;
  const invokerId = uInfo.userId as string;
  // codefree config
  const config: AgentConfig = {
    apiKey,
    invokerId,
    pluginType: process.env.ISSEC !== 'false' ? 'oscap' : 'codefree',
    pluginVersion: getExtensionVersion(),
    clientType: CLIENT_TYPE,
    clientVersion: vscode.version,
    serverType: process.env.ISSEC !== 'false' ? 'oscap' : 'codefree',
    serverBaseUrl: process.env.ISSEC !== 'false' ? `${getHttpServerHost()}`: `${getHttpServerHost()}/api/acbackend`,
    embeddingSubservice: 'emb-composer',
    rerankSubservice: 'rerank-composer',
    composerSubservice: 'codefree-plugin-composer-chat',
    cplSubservice: 'codefree-plugin-cpl'
  }
  // secidea config
  // const config: AgentConfig = {
  //   apiKey: '',
  //   invokerId: '',
  //   pluginType: 'oscap',
  //   pluginVersion: '',
  //   clientType: '',
  //   clientVersion: '',
  //   serverType: "oscap",
  //   serverBaseUrl: 'https://oscap-poc1.out.secidea.com:40081/oscap',
  //   'embedding-subservice': '',
  //   'rerank-subservice': '',
  //   'composer-subservice': '',
  //   'cpl-subservice': '',
  // }
  return config;
}