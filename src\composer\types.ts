import { selectedWorkItem } from '../codechat/types';
export interface DiffMsgData {
  path?: string;
  beforeContent?: string;
  afterContent?: string;
}

export interface MessageData {
  content?: string;
  done?: boolean;
  error?: string;
}

export interface ChatMsg {
  project: ProjectItem;
  messages: ChatMessage[];
  chatType: string;
  title: string;
  fileRanges: string[];
  modelName?: string;
  oaiServerUrl?: string;
  gitUrls: string[];
  workItems?: selectedWorkItem[];
}

export interface DiffFileMsg {
  project?: ProjectItem;
}

export interface ProjectItem {
  projectPath: string;
  projectId: string;
  windowId: string;
}

export interface ChatMessage {
  role?: string;
  content: string;
  knowledgeBaseIds?: string[];
  userId?: string;
  project?: string;
}

export interface Message {
  messageType: string;
  data: MessageData;
  messageId: string;
}

export interface DiffMessage {
  messageType: string;
  data: DiffMsgData[];
  messageId: string;
}

export interface Project {
  projectPath: string; //代码工程在本机的绝对路径
  projectId: string; //代码工程名称
  windowId: string; //用于关联用户上下文，当用户在一个窗口对话多次时，保持windowId相同，当用户决定新开窗口时，重新创建一个windowId
}

export interface IComposerService {
  /**
   * 处理 Composer 聊天响应
   */
  handleComposerChatResponse(data: any, project: any): void;

  /**
   * 处理 Composer 文件差异响应
   */
  handleComposerDiffFileResponse(data: any, project: any): void;
}

export interface ComposerRequestParams {
  reqType: 'composerchat';
  dialogId: string;
  input: string; // 用户输入的内容
  contextInputItems: ContextInputItem[];
  chatType: string; // 对话类型，取值：code - 代码生成
  title: string; // 任务名称，取值：OpenAI
  modelName: string; // 本次对话选择的模型名称，仅当clientType=oscap时需要传递，clientType=codefree时，无需传递此参数
  displayContent: string;
  createTime: string;
  selectedWorkItems?: selectedWorkItem[];
}

export interface ComposerMessage {
  role: string; // 本对象角色, 限定为'user'或'assistant'
  content: string; // 提问或（上面几轮）回答的内容
}

// export interface ContextItemsRequestParams{
//   name: string // 索引项目文件类型，取值：folder-目录 codebase-代码库
//   query: string // 查询的目录/代码库绝对地址
//   fullInput: string // 用户输入的内容
//   selectedCode: RangeInFile[] // 用户选中的文件，默认填空字符串即可
// }

export interface RangeInFile {
  filepath: string; // 文件路径，需要确认是绝对路径还是相对路径
  range: Range;
}

export interface Range {
  start: Position;
  end: Position;
}

export interface Position {
  line: number;
  character: number;
}

export interface ContextItemsRequestParams {
  input: string; // 用户输入的内容
  contextInputItems: ContextInputItem[];
}

export interface ContextInputItem {
  itemTag: string; //上下文对象tag，取值为：workspace - 当前工程；folder - 代码目录；codefile - 代码文件；kb - 知识库；file - 文件
  itemValue: string; //上下文对象值，可为空。取值为：当itemTag=folder/codefile/file时，为相关文件或者目录的绝对路径；当itemTag=kb时，为知识库名字
}

export interface ContextOutputItem {
  name: string;
  description: string;
  content: string;
  id: Provider;
  origin: string; //召回内容的原始输入上下文对象，如果为codebase取值为@codebase，如果为folder则为对应的itemValue
}

export interface Provider {
  providerTitle: string;
  itemId: string;
}

export const ideMessageTypes = [
  'getGlobalIgnoreList',
  'isTelemetryEnabled',
  'getUniqueId',
  'getWorkspaceConfigs',
  'listWorkspaceContents',
  'getWorkspaceDirs',
  'fileExists',
  'readFile',
  'getBranch',
  'getIdeInfo',
  'getIdeSettings',
  'getRepoName',
  'getLastModified',
];
