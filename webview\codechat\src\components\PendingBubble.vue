<template>
  <div class="code-bubble ai-bubble">
    <div class="message-base-info">
      <div class="logo">
        <svg-icon :name="brandStore.isSec ? 'ai-logo-sec' : 'ai-logo'" size="24px"></svg-icon>
      </div>
      <div class="message-base-info-text">
        <span class="message-user-name">{{ brandStore.brandName }}</span>
      </div>
    </div>
    <div class="message">
      <div class="message-text code-msg-loading-text">
         {{ msg }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/store/chat';
import { useBrandStore } from '@/store/brand';

const props = defineProps({
  msg: {
    type: String,
  },
});
const chatStore = useChatStore();
const brandStore = useBrandStore();


</script>

<style lang="scss">
</style>
