export enum EventType {
  LOGIN = 1,
  BROWSER_OPENED = 2,
  CODE_RECEIVED = 3,
  ACCESS_TOKEN_RECEIVED = 4,
  USER_INFO_RECEIVED = 5,
  CHANNEL_REGISTERED = 6,
  LOGIN_SUCCESS = 7,
  LOGIN_CANCELED = 8,
  LOGIN_EXPIRED = 9,
  LOGIN_FAILED = 10,
  LOGOUT = 11,
  WSSERVER_ERROR = 12,
  QUESTION_NOT_LOGIN = 13,
  WSSERVER_RECONNECT = 14,
  THEME_CHANGED = 15,
}

export interface IServiceObserver {
  onServiceChanged(eventType: EventType, data?: unknown): void;
  setObservable(observable: IServiceObservable): void;
}

export interface IServiceObservable {
  registerObserver(observer: IServiceObserver): void;
  unregisterObserver(observer: IServiceObserver): void;
  fireServiceChanged(eventType: EventType, data?: unknown): void;
  handleEvent(eventType: EventType, data?: unknown): void;
}

export interface LoginUserInfo {
  name?: string;
  userAccount?: string;
}

export interface AuthResponse {
  access_token?: string;
  token_type?: string;
  expires_in?: number;
  scope?: string;
  uid?: string;
  info?: string;
  state?: string;
  redirect_uri?: string;
  ori_session_id?: string;
  refresh_token?: string;
  refresh_token_expires_in?: string;
}

export interface UInfo {
  userId?: string;
  sessionId?: string;
  apiKey?: string;
  userAccount?: string;
}
