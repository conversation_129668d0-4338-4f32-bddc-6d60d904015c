import HttpClient from '../commclient/httpClient';
import {
  RtnCode,
  ClientCommunicateType,
  LoginStatus,
  ChatTips,
  CLIENT_TYPE,
} from '../common/constants';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { GetChatHistoryResp, GetDialogResp } from './types/chatHistory';
import {
  LIST_DIALOGS_PATH,
  GET_DIALOGS_PATH,
  EDIT_DIALOG_TITLE_PATH,
  REMOVE_DIALOG_PATH,
  STOP_ANSWER_PATH,
  FEEDBACK_ANSWER_PATH,
} from '../common/config';
import { HttpError } from '../commclient/types/http';
import { Logger } from '../utils/logger';
import {
  getCurrentUser,
  getExtensionVersion,
  getGitRepoUrl,
  getProjectName,
  getGitRemoteUrls,
} from '../common/globalContext';
import { ChatHistoryRequest, AnswerStopParams, FeedbackAnswerRequest } from '../codechat/types';
import { BodyInit } from 'node-fetch';
import { LoginServiceInst } from '../service/loginService';
import * as vscode from 'vscode';
import { HttpResult } from './types/http';

/**
 * Prompts服务
 */
export default class ChatHistoryService implements IAnswerHandler {
  private type: ClientCommunicateType = ClientCommunicateType.HTTP;

  private httpClient: HttpClient | undefined;

  /**
   * 通过哪种方式上传
   * @param type
   */
  public constructor(type?: ClientCommunicateType) {
    if (type) {
      this.type = type;
    }

    if (this.type === ClientCommunicateType.HTTP) {
      this.httpClient = new HttpClient(undefined, 'chathistory');
    }
  }

  public async getChatHistory(queries: ChatHistoryRequest): Promise<HttpResult> {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[ChatHistoryService] getChatHistory, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    if (!this.httpClient) {
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const uInfo = await getCurrentUser();
    const params: Record<string, string> = {
      userId: uInfo.userId || '',
      subServices:
        queries.subServices ||
        'codechat,codeexplain,codeoptimize,codecomment,codeunittest,fixexception,assistant,kbassistant',
      pageNum: (queries.pageNum || 1).toString(),
      pageDataCount: (queries.pageDataCount || 30).toString(),
      title: queries.title || '',
    };
    const result = await this.httpClient.requestByGet<GetChatHistoryResp>(
      LIST_DIALOGS_PATH,
      params,
      {
        headers: {
          invokerId: uInfo.userId as string,
          apiKey: uInfo.apiKey as string,
        },
      }
    );
    if (!result || result instanceof HttpError) {
      Logger.error(`[ChatHistoryService] getChatHistory failed, ${JSON.stringify(result)}`);

      return {
        code: result?.code || result?.status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[ChatHistoryService] getChatHistory result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result,
    };
  }

  public async getDialog(queries: ChatHistoryRequest): Promise<HttpResult> {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[ChatHistoryService] getDialog, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    if (!this.httpClient) {
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const uInfo = await getCurrentUser();
    const params: Record<string, string> = {
      userId: uInfo.userId || '',
      subService: queries.subService || 'assistant',
      dialogId: queries.dialogId || '',
    };
    const result = await this.httpClient.requestByGet<GetDialogResp>(GET_DIALOGS_PATH, params, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[ChatHistoryService] getDialog failed, ${JSON.stringify(result)}`);

      return {
        code: result?.code || result?.status || RtnCode.HTTP_REQUEST_ERROR,
        error: result?.message,
      };
    }

    if (result?.code) {
      return {
        code: result.code,
        data: result
      }
    }    

    Logger.debug(`[ChatHistoryService] getDialog result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result,
    };
  }

  public async editDialogTitle(queries: ChatHistoryRequest): Promise<HttpResult> {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[ChatHistoryService] editDialogTitle, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    if (!this.httpClient) {
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const body: BodyInit = {
      title: queries.title || '',
      dialogId: queries.dialogId || '',
    };
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<unknown>(EDIT_DIALOG_TITLE_PATH, body, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        'Content-Type': 'application/json',
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[ChatHistoryService] editDialogTitle failed, ${JSON.stringify(result)}`);

      return {
        code:
          (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
        error: (result as { optResult: number; message: string })?.message,
      };
    }
    Logger.debug(`[ChatHistoryService] editDialogTitle result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result,
    };
  }

  public async removeDialog(queries: ChatHistoryRequest): Promise<HttpResult> {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[ChatHistoryService] removeDialog, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    if (!this.httpClient) {
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const params: Record<string, string> = {
      dialogId: queries.dialogId || '',
    };
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByDelete<unknown>(REMOVE_DIALOG_PATH, params, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[ChatHistoryService] removeDialog failed, ${JSON.stringify(result)}`);

      return {
        code:
          (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[ChatHistoryService] removeDialog result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result,
    };
  }

  public async stopAnswer(queries: AnswerStopParams): Promise<HttpResult> {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[ChatHistoryService] editDialogTitle, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    if (!this.httpClient) {
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const body: BodyInit = {
      reqId: queries.reqId || '',
      dialogId: queries.dialogId || '',
      questionType: queries.questionType || 'newAsk',
      parentReqId: queries.parentReqId || '',
      subService: queries.subService || 'assistant',
      system: queries.system || '',
      // question: queries.question || '',
      answer: queries.answer || '',
      kbId: queries.kbId,
      modelName: queries.modelName,
      qaQuestion: queries.qaQuestion,
      quote: queries.quote,
      files: queries.files,
      modelRouteCondition:
        queries.templateId !== ''
          ? {
            version: 'v1',
            payload: {
              templateId: queries.templateId,
            },
          }
          : undefined,
    };
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<unknown>(STOP_ANSWER_PATH, body, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        'Content-Type': 'application/json',
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[ChatHistoryService] stopAnswer failed, ${JSON.stringify(result)}`);

      return {
        code:
          (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[ChatHistoryService] stopAnswer result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result,
    };
  }

  public async feedbackAnswer(req: FeedbackAnswerRequest) {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[ChatHistoryService] feedbackAnswer, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    if (!this.httpClient) {
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const body = {
      reqId: req.reqId,
      dialogId: req.dialogId,
      feedback: req.feedback,
      type: req.type || 0,
      client: {
        type: CLIENT_TYPE,
        version: vscode.version,
        pluginVersion: getExtensionVersion(),
        gitUrl: getGitRepoUrl(),
        gitUrls: await getGitRemoteUrls(),
        projectName: getProjectName(),
      },
    };
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<unknown>(FEEDBACK_ANSWER_PATH, body, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        'Content-Type': 'application/json',
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[ChatHistoryService] feedbackAnswer failed, ${JSON.stringify(result)}`);

      return {
        code:
          (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[ChatHistoryService] feedbackAnswer result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: {
        ...result,
        reqId: req.reqId,
        feedback: req.feedback,
      },
    };
  }

  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    throw new Error('Method not implemented.');
  }

  public getReqId(): string {
    throw new Error('Method not implemented.');
  }
}
export const ChatHistoryServiceInst = new ChatHistoryService();
