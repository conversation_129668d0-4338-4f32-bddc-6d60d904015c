import HttpClient from '../commclient/httpClient';
import { Rtn<PERSON>ode, ClientCommunicateType, LoginStatus, ChatTips } from '../common/constants';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { UPLOAD_SCAN_FILE_PATH, QUERY_SCAN_ISSUES_PATH, STOP_SCAN_PATH, AI_EXPLAIN_PATH } from '../common/config';
import { HttpError } from '../commclient/types/http';
import { Logger } from '../utils/logger';
import { getCurrentUser, getMacAddress, getGitRepoUrl, getProjectName } from '../common/globalContext';
import { UploadScanFileRequest, QueryIssuesRequest, aiExplainParams} from '../codechat/types';
import { LoginServiceInst } from '../service/loginService';
import { HttpResult } from './types/http';
import { UploadScanFileResp, StopScanResp, UploadScanFileResult, Issue } from './types/security';
import { File, Blob as NodeBlob } from 'node-fetch';
import * as fs from 'fs';
import * as path from 'path';
import * as FormData from 'form-data';
import * as vscode from 'vscode';
import { SseClient } from '../commclient/sseClient';

/**
 * Knowledge base服务
 */
export default class SecurityService implements IAnswerHandler {
  private type: ClientCommunicateType = ClientCommunicateType.HTTP;

  private httpClient: HttpClient | undefined;

  private sseClient: SseClient | undefined;

  /**
   * 通过哪种方式上传
   * @param type
   */
  public constructor(type?: ClientCommunicateType) {
    if (type) {
      this.type = type;
    }

    if (this.type === ClientCommunicateType.HTTP) {
      this.httpClient = new HttpClient(undefined, 'security');
      this.sseClient = new SseClient();
    }
  }


  public async uploadScanFile(queries: UploadScanFileRequest, filePath: string): Promise<HttpResult> { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[SecurityService] uploadScanFile, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }
    const formData = new FormData();
    const macAddr = await getMacAddress();
    const repoUrl = getGitRepoUrl();
    formData.append('scannerEngine', queries.scannerEngine);
    formData.append('projectName', getProjectName());
    formData.append('language', queries.language);
    formData.append('ideType', 'VsCode');
    formData.append('fileSize', queries.fileSize);
    !!macAddr && formData.append('macAddr', await getMacAddress());
    !!repoUrl && formData.append('repoUrl', getGitRepoUrl());


    const file = fs.createReadStream(filePath);
    formData.append('file', file, { filename: path.basename(filePath) });

    
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<UploadScanFileResp>(UPLOAD_SCAN_FILE_PATH, formData, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        ...formData.getHeaders(),
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[SecurityService] uploadScanFile failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || RtnCode.HTTP_REQUEST_ERROR,
        msg: (result as HttpError).message
      };
    }
    Logger.debug(`[SecurityService] uploadScanFile result: ${JSON.stringify(result)}`);
    return result;
  }

  public async queryScanIssues(queries: QueryIssuesRequest): Promise<UploadScanFileResult> { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[securityService] queryScanIssues, not login');
      return { code: RtnCode.NOT_LOGIN }
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }
    const params: Record<string, string> = {
      taskId: queries.taskId,
      page: (queries.page || 1).toString(),
      pageSize: (queries.pageSize || 100).toString(),
    };
    
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByGet<unknown>(QUERY_SCAN_ISSUES_PATH, params, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[securityService] queryScanIssues failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || RtnCode.HTTP_REQUEST_ERROR,
        msg: (result as HttpError).message
      };
    }
    Logger.debug(`[securityService] queryScanIssues result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      issueList: result as Issue[]
    };
  }

  public async stopScan(taskId: string): Promise<HttpResult> { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[SecurityService] stopScan, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }

    
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<StopScanResp>(STOP_SCAN_PATH, { taskId }, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        'Content-Type': 'application/json',
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[SecurityService] stopScan failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || RtnCode.HTTP_REQUEST_ERROR,
        msg: (result as HttpError).message
      };
    }
    Logger.debug(`[SecurityService] stopScan result: ${JSON.stringify(result)}`);
    return result;
  }

  public async aiExplain(params: aiExplainParams, onSseMessage: (data: any) => Promise<void>){
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[securityService] queryScanIssues, not login');
      return { code: RtnCode.NOT_LOGIN }
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }
    const line = params.issueList[0].line - 1
    const rootPath = vscode.workspace.workspaceFolders![0].uri.fsPath;
    const filePath = params.filePath
    const uriString = path.join(rootPath, filePath);
    const document = await vscode.workspace.openTextDocument(uriString)
    const lineCount = document.lineCount;
    const fileStartLine = Math.max(0, line - 20);
    const fileEndLine = Math.min(lineCount - 1, line + 20);
    let fileContent = '';
    for (let i = fileStartLine; i <= fileEndLine; i++) {
      fileContent += document.lineAt(i).text + '\n';
    }
    const uInfo = await getCurrentUser();
    await this.sseClient?.fetchSse({
      url: AI_EXPLAIN_PATH,
      headers:
      {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        userId: uInfo.userId as string
      },
      body: {
        type: params.scannerEngine === 'sonar' ? 1 : 2,
        fileStartLine: fileStartLine,
        fileEndLine: fileEndLine,
        fileContent: fileContent,
        issueList: [{
          issueId: params.issueList[0].issueId,
          rule: params.issueList[0].rule, 
          message: params.issueList[0].message,
          line: line,
          language: params.issueList[0].language
        }]
      }
    }, async (data: any) => {
      await onSseMessage(data)
    });
  }
  
  public stopAiRequest() {
    if (this.sseClient) {
      this.sseClient.stop(AI_EXPLAIN_PATH);
    }
  }

  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    throw new Error('Method not implemented.');
  }

  public getReqId(): string {
    throw new Error('Method not implemented.');
  }

}
export const SecurityServiceInst = new SecurityService();