import { CONVERSATION_FILE_PATH, CONVERSATION_LIMIT, CONVERSATION_TOP } from '../common/config';
import { str2obj } from '../utils/common';
import { Logger } from '../utils/logger';
import { Conversation, ChatMessageSimple } from './conversation';
import { ConversationEventType, IConversationHandler, ChatHistoryRequest } from './types';
import { ChatHistoryServiceInst } from '../service/chatHistoryService';
import * as vscode from 'vscode';
import { GetDialogResp, DialogType, DialogInfo, GetChatHistoryResp } from '../service/types/chatHistory';
import { HttpResult } from '../service/types/http';
import { publicDecrypt } from 'crypto';
/**
 * 会话管理
 */
export default class ConversionManager {
  private conversationList: Map<string, Conversation> = new Map<string, Conversation>();

  private handler: IConversationHandler;

  private fileUri: vscode.Uri;

  private topLen = CONVERSATION_TOP;

  private limit = CONVERSATION_LIMIT;

  public constructor(handler: IConversation<PERSON>andler, globalStorageUri: vscode.Uri) {
    this.handler = handler;
    this.fileUri = vscode.Uri.joinPath(globalStorageUri, CONVERSATION_FILE_PATH);
  }

  /**
   * 从本地文件加载已存的conversations
   */

  private async generateConversations(req: ChatHistoryRequest) {
    const result = await ChatHistoryServiceInst.getChatHistory(req);
    if (!result.code) {
      // this.conversationList.clear();
      const conversationList = (result.data as GetChatHistoryResp).dialogs || null;
      if (conversationList === null) {
        return result;
      }
      for (const conversation of conversationList) {
        this.conversationList.set(
          conversation.dialogId!,
          new Conversation(conversation)
        ); conversation.title, conversation.subService, conversation.dialogId, conversation.createTime, conversation.updateTime
      }
    }
    return result;
  }

  public async refreshConversations(req: ChatHistoryRequest) {
    const result = await this.generateConversations(req);
    this.handler.onConversationChange({
      eventType: ConversationEventType.DATA_REFRESHED,
      data: this.getRefreshConversations(),
      code: result.code
    });
  }


  /**
   * 添加会话
   * @param conversation
   */
  public async addConversation(conversation: Conversation) {
    // await this.generateConversations({});
    const conversations = this.getTopConversations();
    conversations.unshift(conversation);
    this.clearAndtraverseConversations(conversations);
    this.removeOverLimitConversation();

    this.handler.onConversationChange({
      eventType: ConversationEventType.DATA_ADDED,
      data: this.getTopConversations()
    });
  }

  private clearAndtraverseConversations(conversationList: Conversation[]) {
    this.conversationList.clear();
    for (const conversation of conversationList) {
      this.conversationList.set(
        conversation.id,
        conversation
      );
    }
  }

  /**
   * 更新会话
   * @param conversation
   */
  public updateAndSendConversation(conversation: Conversation) {
    this.conversationList.set(conversation.id, conversation);
    this.handler.onConversationChange({
      eventType: ConversationEventType.DATA_UPDATED,
      data: this.getTopConversations(),
    });
  }

  public saveConversationTitle(conversation: Conversation, result: HttpResult) {
    this.conversationList.set(conversation.id, conversation);
    this.handler.onConversationChange({
      eventType: ConversationEventType.DATA_UPDATED,
      data: this.getTopConversations(),
      ...result
    });
  }

  /**
   * 获取会话
   * @param id
   * @returns
   */
  public getConversation(id: string) {
    return this.conversationList?.get(id);
  }

  public setConversation(conversation: Conversation) {
    if (this.conversationList.has(conversation.id)) {
      this.conversationList.set(conversation.id, conversation);
    }
  }

  public delConversation(id: string) {
    if (this.conversationList.has(id)) {
      this.conversationList.delete(id);
    }
  }

  /**
   * 删除会话
   * @param id
   */
  public async removeConversation(id: string) {
    const result = await ChatHistoryServiceInst.removeDialog({ dialogId: id });
    if (!result.code) {
      this.delConversation(id);
    }
    this.handler.onConversationChange({
      eventType: ConversationEventType.DATA_REMOVED,
      data: this.getTopConversations(),
      code: result.code
    });
  }

  /**
   * 保存所有会话至本地
   */
  public saveConversations() {
    const conversations = this.getAllConversations();
    if (conversations.length > 0) {
      const buffer = Buffer.from(JSON.stringify(conversations));
      vscode.workspace.fs.writeFile(this.fileUri, buffer);
    }
  }

  public async updateConversationTitle(dialogId: string, title: string) {
    return await ChatHistoryServiceInst.editDialogTitle({ dialogId, title });
  }


  /**
   * 从文件中读取会话
   */
  private async readConversationFromFile() {
    const conversationMap = new Map<string, Conversation>();

    try {
      const fileData = await vscode.workspace.fs.readFile(this.fileUri);

      if (fileData) {
        const fileJson = str2obj(fileData.toString());

        if (Array.isArray(fileJson)) {
          fileJson.forEach((item: Conversation) => {
            conversationMap.set(item.id, item);
          });
        }
      }

      return conversationMap;
    } catch (e) {
      const error = e as Error;
      Logger.error(`ConversionManager] readCoversationFromFile error: ${error.message}`);
      return conversationMap;
    }
  }

  /**
   * 获取所有会话内容
   */
  private getAllConversations(): Conversation[] {
    const conversations: Conversation[] = [];

    for (const [id, conversation] of this.conversationList) {
      conversations.push(conversation);
    }

    return conversations;
  }

  private getRefreshConversations(): Conversation[] {
    const conversations: Conversation[] = [];

    for (const [id, conversation] of this.conversationList) {
      if (conversation.isNewConversation) {
        continue;
      }
      conversations.push(conversation);
    }

    return conversations;
  }

  /**
   * 获取最新的几个会话给webview, 按会话创建时间倒序
   */
private getTopConversations() {
    // const allConversations = this.getAllConversations().reverse();
    const allConversations = this.getAllConversations();
    return allConversations.slice(0, this.topLen);
  }

  /**
   * 删除超过限制的旧会话，需要从头删，因为保存是正序的
   */
  private removeOverLimitConversation() {
    const size = this.conversationList.size;

    if (size <= this.limit) {
      return;
    }

    const removeKeys = [];
    let index = 0;

    for (const [id, conversation] of this.conversationList) {
      if (index < size - this.limit) {
        removeKeys.push(id);
      }

      index++;
    }

    removeKeys.forEach(key => {
      this.conversationList.delete(key);
    });
  }

  public async updateConversationList(conversation: Conversation) {
    if (!Object.keys(conversation.questions).length) {
      await this.generateConversations({});
      this.handler.onConversationChange({
        eventType: ConversationEventType.DATA_UPDATED,
        data: this.getTopConversations()
      });
    }
  }

  public async updateConversation(conversation: Conversation) {
    const params = {
      dialogId: conversation.id,
      subService: conversation.subService,
    };
    const result = await ChatHistoryServiceInst.getDialog(params);
    if (!result.code) {
      conversation.questions = (result.data as GetDialogResp)?.dialog?.questions as ChatMessageSimple;
      this.setConversation(conversation);
    }
    return result;
  }

  public setConverstionSubservice(conversation: Conversation, subService?: string) {
    if (!Object.keys(conversation.questions || []).length) {
      conversation.subService = subService || 'assistant';
      this.setConversation(conversation);
    }
  }

  public async getConversationList() {
    return this.conversationList;
  }


}





