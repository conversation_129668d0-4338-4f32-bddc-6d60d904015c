import { Logger } from '../utils/logger';
import { SecurityServiceInst } from '../service/securityService';
import * as vscode from 'vscode';
import { FileNode } from './type';
import * as path from 'path';
import * as fs from 'fs';
import * as archiver from 'archiver';
import { ICodeScanHandler, UploadScanFileRequest, QueryIssuesRequest, ViewDetailParams, aiExplainParams } from '../codechat/types';
import { WebViewRspCommand, RtnCode, SrdCommand } from '../common/constants';
import Utils from './utils';
import { utils } from 'mocha';

/**
 * 会话管理
 */
export default class CodeScanManager implements vscode.Disposable {
  private stopSign: boolean = false;
  private handler: ICodeScanHandler;
  private disposable: vscode.Disposable;

  public constructor(handler: ICodeScanHandler) {
    this.handler = handler;
    this.disposable = vscode.Disposable.from(...this.registerCommands());
  }

  public dispose() {
    this.disposable.dispose();
  }

  private registerCommands(): vscode.Disposable[] {
    const disposable: vscode.Disposable[] = [];
    disposable.push(
      vscode.commands.registerCommand(SrdCommand.CODE_SECURITY_SCAN, () => {
        this.sendSecurityScanStartMessage();
      }),
    );

    return disposable;
  }

  private sendSecurityScanStartMessage() { 
    this.handler.handleCodeScanEvent({
      command: WebViewRspCommand.CODE_SECURITY_SCAN_START,
    });
  }

  public runScan(params: UploadScanFileRequest):void { 
    this.stopSign = false;
    this.createZip(params);
  }

  public async getScanFiles(params: {reqType: string}): Promise<void> { 
    const workspaceFolders = vscode.workspace.workspaceFolders;
    let fileNodes: FileNode[] = [];
    if (workspaceFolders) { 
      // for (let i = 0; i < workspaceFolders.length; i++) { 
      //   const rootPath = workspaceFolders[i].uri.fsPath;
      //   const root: FileNode = {
      //     name: path.basename(rootPath),
      //     children: []
      //   }
      //   const fileNode = await Utils.traverseFolder(rootPath);
      //   root.children = fileNode;
      //   fileNodes.push(root);
      // }

      const rootPath = workspaceFolders[0].uri.fsPath;
      const root: FileNode = {
        name: path.basename(rootPath),
        children: []
      }
      const fileNode = await Utils.traverseFolder(rootPath);
      if (fileNode) {
        root.children = fileNode;
      }
      fileNodes.push(root);
    }
    this.handler.handleCodeScanEvent({
      command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
      type: params.reqType,
      data: {
        code: RtnCode.SUCCESS,
        fileNodes
      }
    });
  }

  private createZip(params: UploadScanFileRequest): void { 
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) { 
      return;
    }
    const outputZipPath = workspaceFolders[0].uri.fsPath + '.zip';
    const output = fs.createWriteStream(outputZipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    if (this.stopSign) { 
      return;
    }

    archive.pipe(output);

    archive.on('error', (err) => {
      clearInterval(stopInterval);
      vscode.window.showErrorMessage(`源码检测：压缩过程中发生错误`);
      this.handler.handleCodeScanEvent({
        command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
        type: params.reqType,
        data: {
          code: RtnCode.UPLOAD_FAIL,
          msg: '源码检测：压缩过程中发生错误'
        }
      });
      output.end(); 
    });

    output.on('close', async () => {
      clearInterval(stopInterval);
      try {
        Logger.debug(`[codeScanManager] createZip: archive success`);
        if (this.stopSign) { 
          return;
        }
        params.language = this.getLanguages(params.scanFiles!);
        this.uploadScanFile(params, outputZipPath);
      } catch (err) { 
        Logger.debug(`[codeScanManager] createZip: archive failed`);
      }
    });

    output.on('error', (err) => {
      clearInterval(stopInterval);
      this.handler.handleCodeScanEvent({
        command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
        type: params.reqType,
        data: {
          code: RtnCode.UPLOAD_FAIL,
          msg: '源码检测：压缩过程中发生错误'
        }
      });
    });

    this.addFilesToArchive(params.scanFiles!, archive, workspaceFolders[0].uri.fsPath);

    archive.finalize();

    const stopInterval = setInterval(() => {
      if (this.stopSign) {
          clearInterval(stopInterval);
          archive.abort(); 
          output.end(); 
      }
    }, 100);

  }

  private addFilesToArchive(scanFiles: string[], archive: archiver.Archiver, rootDirectory: string) { 
    scanFiles.forEach((scanFilePath) => {
      if (!this.stopSign) {          
        const normalizedPath = path.normalize(scanFilePath);
        const relativePath = path.relative(rootDirectory, normalizedPath);
        archive.file(normalizedPath, { name: relativePath });
      }
    });
  }

  private async uploadScanFile(params: UploadScanFileRequest, filePath: string): Promise<void> { 
    if (this.stopSign) { 
      return;
    }
    const result = await SecurityServiceInst.uploadScanFile(params, filePath);
    fs.unlink(filePath, () => { });
    this.handler.handleCodeScanEvent({
      command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
      type: params.reqType,
      data: result
    });

  }

  private getLanguages(scanFiles: string[]): string { 
    const languages = new Set<string>();
    scanFiles.forEach((scanFile) => { 
      const extension = Utils.extractFileExtension(scanFile);
      const language = Utils.findLanguageByExtension(extension);
      if (language) { 
        languages.add(language)
      }
    })
    return Array.from(languages).join(',');
  }

  public async queryScanIssues(params: QueryIssuesRequest): Promise<void> { 
    const result = await SecurityServiceInst.queryScanIssues(params);
    this.handler.handleCodeScanEvent({
      command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
      type: params.reqType,
      data: result
    });
  }

  public viewDetail(params: ViewDetailParams): void { 
    const { fileName, line } = params;
    const rootPath = vscode.workspace.workspaceFolders![0].uri.fsPath;
    const uriString = path.join(rootPath, fileName);
    const uri = vscode.Uri.file(uriString);
    vscode.workspace.openTextDocument(uri).then(doc => {
      const start = new vscode.Position(line - 1, 0); 
      const end = new vscode.Position(line - 1, doc.lineAt(line - 1).text.length);

      vscode.window.showTextDocument(doc, {
        preview: true,
        selection: new vscode.Range(start, end)
      });
    });
  }

  public async aiExplain(params: aiExplainParams){
    const language = this.getLanguages([params.filePath]);
    params.issueList[0].language = language;
    await SecurityServiceInst.aiExplain(params, async (data: any) => {
      this.handler.handleCodeScanEvent({
        command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
        type: params.reqType,
        data: data
      })
    });

  }

  public async stopAiRequest() {
    SecurityServiceInst.stopAiRequest();
  }

  public async stopScan(params: { reqType: string, taskId: string }): Promise<void> { 
    const { reqType, taskId } = params;
    const result = await SecurityServiceInst.stopScan(taskId);
    this.handler.handleCodeScanEvent({
      command: WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE,
      type: reqType,
      data: result
    });
  } 

}



