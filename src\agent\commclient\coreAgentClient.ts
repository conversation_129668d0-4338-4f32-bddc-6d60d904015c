import { IAgentMessageReceiver } from './MessageReceiver';
import { Logger } from '../../utils/logger';
import * as net from 'net';
import * as fs from 'fs';
import { AgentCommClient } from './agentcommclient';


/**
 * Core Agent 专用客户端
 * 继承自基本的 AgentCommClient
 */
export class CoreAgentClient extends AgentCommClient {
  // 覆盖基类的 generatorTypes，提供 Core Agent 特定的类型
  protected generatorTypes: string[] = [
    'llm/streamComplete',
    'llm/streamChat',
    'command/run',
    'streamDiffLines',
  ];

  constructor(process: any, messageReceiver: IAgentMessageReceiver) {
    super(process, messageReceiver);
  }

} 