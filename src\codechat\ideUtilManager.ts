import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { RecentlyUsedFile } from './types';
import { FileNode } from './types';
import { IIdeUtilHand<PERSON>, DirItem } from './types';
import { WebViewRspCommand } from '../common/constants';
import { IdeUtilEventType } from './types';
import Utils from '../codesecurity/utils';
import { RtnCode } from '../common/constants';
import { EXCLUSION_RULES } from '../common/exclusionRules';
import { Logger } from '../utils/logger';
import * as minimatch from 'minimatch';


export class IdeUtilManager implements vscode.Disposable {

  private static DEBOUNCE_TIME = 300;
  private static FILE_TREE_UPDATE_DEBOUNCE_TIME = 1000;

  private recentFiles: RecentlyUsedFile[] = [];

  private storageKey = 'autoTrackRecentFiles';

  private context: vscode.ExtensionContext;

  private disposable: vscode.Disposable;

  private handler: IIdeUtilHandler;

  // Cached data for file tree and project folders
  private cachedFileTree: FileNode | null = null;
  private cachedProjectFolders: DirItem[] = [];
  private isFileTreeInitialized = false;
  private isProjectFoldersInitialized = false;
  private fileTreeInitializationPromise: Promise<void> | null = null;
  private projectFoldersInitializationPromise: Promise<void> | null = null;

  private debouncedHandleEditorChange = this.debounce((editor: vscode.TextEditor | undefined) => {
    if (editor && this.isTrackableDocument(editor.document)) {
      this.updateRecentFiles(editor.document.uri.fsPath);
    }
  }, IdeUtilManager.DEBOUNCE_TIME);

  private debouncedUpdateFileTree = this.debounce(async () => {
    await this.initializeFileTree();
    // this.notifyWebViewOfUpdates();
  }, IdeUtilManager.FILE_TREE_UPDATE_DEBOUNCE_TIME);

  private debouncedUpdateProjectFolders = this.debounce(async () => {
    await this.initializeProjectFolders();
    // this.notifyWebViewOfUpdates();
  }, IdeUtilManager.FILE_TREE_UPDATE_DEBOUNCE_TIME);

  public constructor(context: vscode.ExtensionContext, handler: IIdeUtilHandler) {
    this.context = context;
    this.handler = handler;
    this.disposable = vscode.Disposable.from(...this.setupEventListeners());
    context.subscriptions.push(this.disposable);
    this.loadInitialData();
    this.initializeFileTree();
    this.initializeProjectFolders();
  }

  public dispose(): void {
    // 清理事件监听器
    this.disposable.dispose();
  }

  private async initializeFileTree(): Promise<void> {
    // If already initializing, return the existing promise
    if (this.fileTreeInitializationPromise) {
      return this.fileTreeInitializationPromise;
    }

    this.fileTreeInitializationPromise = (async () => {
      try {
        this.cachedFileTree = await this.getFullFilesInternal();
        this.isFileTreeInitialized = true;
      } catch (error) {
        this.cachedFileTree = null;
        this.isFileTreeInitialized = false;
      } finally {
        this.fileTreeInitializationPromise = null;
      }
    })();

    return this.fileTreeInitializationPromise;
  }

  private async initializeProjectFolders(): Promise<void> {
    // If already initializing, return the existing promise
    if (this.projectFoldersInitializationPromise) {
      return this.projectFoldersInitializationPromise;
    }

    this.projectFoldersInitializationPromise = (async () => {
      try {
        this.cachedProjectFolders = await this.getAllProjectFoldersInternal();
        this.isProjectFoldersInitialized = true;
      } catch (error) {
        this.cachedProjectFolders = [];
        this.isProjectFoldersInitialized = false;
      } finally {
        this.projectFoldersInitializationPromise = null;
      }
    })();

    return this.projectFoldersInitializationPromise;
  }

  // private notifyWebViewOfUpdates(): void {
  //   const currentFilePath = this.getActiveFilePath();
  //   const currentIndex = currentFilePath 
  //     ? [...this.recentFiles].findIndex(item => item.path === currentFilePath) 
  //     : -1;

  //   this.handler.handleIdeUtilEvent({
  //     command: WebViewRspCommand.PUSH_FILE_TREE_UPDATED,
  //     data: {
  //       reqType: IdeUtilEventType.FILE_TREE_UPDATED,
  //       code: RtnCode.SUCCESS,
  //       recentlyUsedFiles: [...this.recentFiles],
  //       fileTree: this.cachedFileTree,
  //       currentIndex: currentIndex,
  //       folderList: this.cachedProjectFolders
  //     }
  //   });
  // }

  public async returnChatContexts(): Promise<void> {
    // If file tree and project folders are not yet initialized, wait for them
    if (!this.isFileTreeInitialized) {
      await this.initializeFileTree();
    }
    
    if (!this.isProjectFoldersInitialized) {
      await this.initializeProjectFolders();
    }

    let currentIndex;
    const currentFilePath = this.getActiveFilePath();
    if (!currentFilePath) {
      currentIndex = -1;
    } else {
      currentIndex = [...this.recentFiles].findIndex(item => item.path === currentFilePath);
    }
    
    this.handler.handleIdeUtilEvent({
      command: WebViewRspCommand.GET_IDE_UTILS_RESPONSE,
      data: {
        reqType: IdeUtilEventType.GET_CHAT_CONTEXTS,
        code: RtnCode.SUCCESS,
        recentlyUsedFiles: [...this.recentFiles],
        fileTree: this.cachedFileTree,
        currentIndex: currentIndex,
        folderList: this.cachedProjectFolders
      }
    });
  }

  public getActiveFilePath(): string | undefined {
    const editor = vscode.window.activeTextEditor;
    return editor?.document.uri.fsPath;
  }

  public openTextDocument(request: any) {
    const filePath = request.filePath;
    if (filePath) {
      // 检查文件是否存在
      if (this.validateFile(filePath)) {
        vscode.workspace.openTextDocument(filePath).then(doc => {
          vscode.window.showTextDocument(doc);
        })
        this.handler.handleIdeUtilEvent({
          command: WebViewRspCommand.OPEN_TEXT_DOCUMENT,
          data: {
            code: RtnCode.SUCCESS,
          }
        });
      } else {
        vscode.window.showErrorMessage(`文件不存在或无法访问: ${filePath}`);
        // 发送失败事件
        this.handler.handleIdeUtilEvent({
          command: WebViewRspCommand.OPEN_TEXT_DOCUMENT,
          data: {
            code: RtnCode.INVALID_FILE,
          }
        });
      }
    }
  }

  private async getFullFilesInternal(): Promise<FileNode | null> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders) {
      const rootPath = workspaceFolders[0].uri.fsPath;
      const root: FileNode = {
        name: path.basename(rootPath),
        path: rootPath,
        relativePath: '', // Root folder has empty relative path
        children: []
      }
      const fileNode = await Utils.traverseFolder(rootPath);
      if (!fileNode || fileNode.length === 0) {
        return null;
      }

      // Add relativePath to all nodes
      this.addRelativePathToFileNodes(fileNode, rootPath);

      root.children = fileNode;
      return root;
    }
    return null;
  }


  private loadInitialData() {
    try {
      const savedData = this.context.workspaceState.get<RecentlyUsedFile[]>(this.storageKey);
      if (savedData) {
        // 筛选有效文件且不在排除列表中的文件
        this.recentFiles = savedData
          .filter(f => this.validateFile(f.path) && !this.shouldExcludeFile(f.path))
          .map(f => {
            // 如果不存在相对路径，则添加相对路径
            if (!f.relativePath) {
              const workspaceFolder = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(f.path));
              f.relativePath = workspaceFolder ? path.relative(workspaceFolder.uri.fsPath, f.path) : f.path;
            }
            return f;
          });
      }
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor && this.isTrackableDocument(activeEditor.document)) {
        this.updateRecentFiles(activeEditor.document.uri.fsPath);
      }
    } catch (error) {
      Logger.error(`加载历史记录失败:${error}`);
    }
  }

  private setupEventListeners(): vscode.Disposable[] {
    const disposables: vscode.Disposable[] = [];
    
    // File tracking for recent files
    const deleteWatcher = vscode.workspace.onDidDeleteFiles(this.handleFileDelete.bind(this));
    const renameWatcher = vscode.workspace.onDidRenameFiles(this.handleFileRename.bind(this));
    const editWatcher = vscode.workspace.onDidChangeTextDocument(this.handleFileEdit.bind(this));
    
    // Editor change tracking
    const changeActiveEditorDisposable = vscode.window.onDidChangeActiveTextEditor(
      this.debouncedHandleEditorChange.bind(this)
    );
    
    // File system watchers for entire workspace
    const fileCreateWatcher = vscode.workspace.onDidCreateFiles(() => {
      this.debouncedUpdateProjectFolders();
      this.debouncedUpdateFileTree();
    });
    
    const fileDeleteWatcher = vscode.workspace.onDidDeleteFiles(() => {
      this.debouncedUpdateProjectFolders();
      this.debouncedUpdateFileTree();
    });
    
    const fileRenameWatcher = vscode.workspace.onDidRenameFiles(() => {
      this.debouncedUpdateProjectFolders();
      this.debouncedUpdateFileTree();
    });

    // Watch for changes in workspace folders
    const workspaceFoldersChangeWatcher = vscode.workspace.onDidChangeWorkspaceFolders(() => {
      this.debouncedUpdateFileTree();
      this.debouncedUpdateProjectFolders();
    });
    
    disposables.push(
      deleteWatcher, 
      renameWatcher, 
      editWatcher, 
      changeActiveEditorDisposable,
      fileCreateWatcher,
      fileDeleteWatcher,
      fileRenameWatcher,
      workspaceFoldersChangeWatcher
    );
    
    return disposables;
  }

  private isTrackableDocument(document: vscode.TextDocument): boolean {
    return (
      document.uri.scheme === 'file' &&
      this.isInWorkspace(document.uri) &&
      fs.existsSync(document.uri.fsPath)
    );
  }

  private isInWorkspace(uri: vscode.Uri): boolean {
    return !!vscode.workspace.getWorkspaceFolder(uri);
  }

  private validateFile(filePath: string): boolean {
    return (
      fs.existsSync(filePath) &&
      path.isAbsolute(filePath) &&
      this.isInWorkspace(vscode.Uri.file(filePath))
    );
  }

  private updateRecentFiles(filePath: string) {
    try {
      // 检查文件是否应该被排除
      if (this.shouldExcludeFile(filePath)) {
        return;
      }
      
      // 获取文件信息
      const fileStats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const workspaceFolder = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(filePath));
      const relativePath = workspaceFolder ? path.relative(workspaceFolder.uri.fsPath, filePath) : filePath;

      const newFile = {
        path: filePath,
        relativePath: relativePath,
        name: fileName,
        size: fileStats.size,
      };
      
      // 移除旧条目
      this.recentFiles = this.recentFiles.filter(f => f.path !== filePath);
      // 插入到数组最前面
      this.recentFiles.unshift(newFile);
      
      // 持久化存储
      this.context.workspaceState.update(this.storageKey, this.recentFiles);
    } catch (error) {
      Logger.error(`更新文件记录失败: ${filePath} ${error}`);
    }
  }

  private debounce<T extends (...args: any[]) => void>(fn: T, delay: number): T {
    let timer: NodeJS.Timeout;
    return ((...args: any[]) => {
      clearTimeout(timer);
      timer = setTimeout(() => fn(...args), delay);
    }) as T;
  }

  private handleFileDelete(event: vscode.FileDeleteEvent) {
    this.recentFiles = this.recentFiles.filter(f =>
      !event.files.some(deletedFile =>
        f.path.startsWith(deletedFile.fsPath)
      )
    );
    this.context.workspaceState.update(this.storageKey, this.recentFiles);
  }

  private handleFileRename(event: vscode.FileRenameEvent) {
    event.files.forEach(({ oldUri, newUri }) => {
      this.recentFiles.forEach(f => {
        if (f.path.startsWith(oldUri.fsPath)) {
          // Update the absolute path
          f.path = f.path.replace(oldUri.fsPath, newUri.fsPath);
          f.name = path.basename(f.path);

          // Update the relative path
          const workspaceFolder = vscode.workspace.getWorkspaceFolder(newUri);
          if (workspaceFolder) {
            f.relativePath = path.relative(workspaceFolder.uri.fsPath, f.path);
          }

          // Update size
          const newFileStats = fs.statSync(f.path);
          f.size = newFileStats.size;
        }
      });
    });
    this.context.workspaceState.update(this.storageKey, this.recentFiles);
  }

  private handleFileEdit(event: vscode.TextDocumentChangeEvent) {
    const filePath = event.document.uri.fsPath;
    // 检查文件是否在recentFiles中
    const fileIndex = this.recentFiles.findIndex(f => f.path === filePath);

    if (fileIndex !== -1) {
      // 获取文件的当前大小
      const fileStats = fs.statSync(filePath);
      const currentFileSize = fileStats.size;
      // 如果文件大小发生变化，更新recentFiles中的条目
      if (this.recentFiles[fileIndex].size !== currentFileSize) {
        this.recentFiles[fileIndex].size = currentFileSize;
        Logger.debug(`文件大小已更新:${this.recentFiles[fileIndex].size}`);
        this.context.workspaceState.update(this.storageKey, this.recentFiles);
      }
    }
  }

  // 调试时使用，重置recentFiles
  private clearRecentFiles(): void {
    this.recentFiles = [];
    this.context.workspaceState.update(this.storageKey, this.recentFiles); // 更新存储
  }

  private async getAllProjectFoldersInternal(): Promise<DirItem[]> {
    const folders: DirItem[] = [];

    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) return folders;

    for (const workspaceFolder of workspaceFolders) {
      const rootPath = workspaceFolder.uri.fsPath;
      await this.traverseDirectory(rootPath, folders, rootPath);
    }

    return folders;
  }


  private async traverseDirectory(currentPath: string, folders: DirItem[], rootPath: string) {
    const entries = await fs.promises.readdir(currentPath, { withFileTypes: true });

    for (const entry of entries) {
      if (entry.isDirectory()) {
        if (EXCLUSION_RULES.DIRECTORIES.has(entry.name)) {
          continue; // 排除该目录
        }
        const fullPath = path.join(currentPath, entry.name);
        const relativePath = path.relative(rootPath, fullPath);

        if (relativePath) {
          folders.push({
            name: entry.name,
            path: fullPath,
            relativePath: relativePath
          });
        }

        await this.traverseDirectory(fullPath, folders, rootPath);
      }
    }
  }

  // Helper method to add relativePath to all file nodes
  private addRelativePathToFileNodes(nodes: FileNode[], rootPath: string): void {
    for (const node of nodes) {
      if (node.path) {
        node.relativePath = path.relative(rootPath, node.path);
      }

      if (node.children && node.children.length > 0) {
        this.addRelativePathToFileNodes(node.children, rootPath);
      }
    }
  }

  /**
   * 根据排除规则检查文件是否应该被排除
   * @param filePath 文件的绝对路径
   * @returns 如果文件应该被排除则返回true，否则返回false
   */
  private shouldExcludeFile(filePath: string): boolean {
    const workspaceFolder = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(filePath));
    if (!workspaceFolder) return true; // 排除工作区外的文件
    
    // 计算相对路径用于模式匹配（统一使用'/'做分隔符）
    const rootPath = workspaceFolder.uri.fsPath;
    const relativePath = path.relative(rootPath, filePath);
    const normalizedPath = relativePath.split(path.sep).join('/');
    
    // 检查文件路径是否包含被排除的目录
    const pathParts = relativePath.split(path.sep);
    if (pathParts.some(part => EXCLUSION_RULES.DIRECTORIES.has(part))) {
      return true; // 如果文件路径包含排除的目录，则排除
    }
    
    // 检查文件是否匹配要排除的文件模式
    if (EXCLUSION_RULES.FILE_PATTERNS.some(pattern => 
      minimatch(normalizedPath, pattern, { dot: true })
    )) {
      return true; // 如果文件匹配排除模式，则排除
    }
    
    return false; // 文件不应被排除
  }
}
