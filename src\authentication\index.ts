import * as vscode from 'vscode';
import SrdAuthenticationProvider, { getAuthenticationSession } from './srdAuthenticationProvider';
import { RtnCode, RtnMessage, SrdCommand } from '../common/constants';
import { LoginServiceInst } from '../service/loginService';
import { parseUriQuery } from '../utils/common';
import { EventType } from '../service/types/login';
import { FORBIDDEN_PATH, NO_SUBSCRIBE_PATH, SECIDEA_EXTENSION } from '../common/config';
import { getAuthToken, getCurrentUser, getCodeAIConfig } from '../common/globalContext';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';
import { getHttpServerHost } from '../utils/envUtil';

export let srdAuthProviderInstance: SrdAuthenticationProvider | null = null;

/**
 * 注册用户认证相关
 * @param context ExtensionContext
 */
export async function registerSrdAuthentication(context: vscode.ExtensionContext) {
  const srdAuthenticationProvider = new SrdAuthenticationProvider(context.secrets);
  srdAuthProviderInstance = srdAuthenticationProvider;
  LoginServiceInst.initDisposable();
  srdAuthenticationProvider.setObservable(LoginServiceInst);

  /**
   * 注册用户认证
   */
  context.subscriptions.push(
    vscode.authentication.registerAuthenticationProvider(
      SrdAuthenticationProvider.id,
      SrdAuthenticationProvider.label,
      srdAuthenticationProvider
    )
  );

  /**
   * 注册Oauth2浏览器打开vscode://监听事件，以获取code
   */
  context.subscriptions.push(
    vscode.window.registerUriHandler({
      handleUri(uri: vscode.Uri): vscode.ProviderResult<void> {
        if (uri.path === '/did-authenticate') {
          const query = parseUriQuery(uri.query);
          LoginServiceInst.handleEvent(EventType.BROWSER_OPENED);
          LoginServiceInst.handleEvent(EventType.CODE_RECEIVED, query.code);
        }
      },
    })
  );

  /**
   *
   * 注册登录命令
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(
      SrdCommand.LOGIN,
      async (other: unknown, showMessage?: boolean) => {
        const hasAuthToken = await checkIfAuthToken();
        const hasUser = await checkIfCurrentUser();

        if (!hasUser) {
          // 有时登出provider的Session未清除，此时再清一次
          await srdAuthenticationProvider.removeTokenAndSession();
          await tryLoginBySrdcloud(showMessage);
        } else if (hasUser && !hasAuthToken) {
          await srdAuthenticationProvider.createSession();
        } else if (hasUser && hasAuthToken) {
          LoginServiceInst.handleEvent(EventType.LOGIN);
        }
      }
    )
  );

  /**
   * 注册登录失败，重新登录或者详情查看命令。从头开始登录流程
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.SHOW_LOGIN_FAILED, async (code: RtnCode) => {
      const reason = RtnMessage[code] || '';
      const selection = await vscode.window.showInformationMessage(
        `登录认证失败${reason ? `，原因：${reason}` : ''}`,
        code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN ? '详情' : '重新登录',
        '取消'
      );

      if (selection === '详情') {
        const path = code === RtnCode.INVALID_USER ? NO_SUBSCRIBE_PATH : FORBIDDEN_PATH;
        const uri = `${getHttpServerHost()}${path}`;
        vscode.env.openExternal(vscode.Uri.parse(uri));
      } else if (selection === '重新登录') {
        await tryLoginBySrdcloud();
      }
    })
  );

  /**
   * 注册版本更新命令
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(
      SrdCommand.UPGRADE_VERSION,
      async (latestVersion: string, versionDesc, clientLatestVersionDownloadUrl) => {
        const selection = await vscode.window.showInformationMessage(
          `发现新版本${latestVersion}, 升级以享受更好体验, 更新详情如下: ${versionDesc}`,
          '在线更新',
          '本地更新'
        );

        if (selection === '本地更新') {
          const uri = clientLatestVersionDownloadUrl;
          vscode.env.openExternal(uri);
        } else if (selection === '在线更新') {
          shwoExtensionInMarketplace();
        }
      }
    )
  );

  // 开始进行登录认证
  await vscode.commands.executeCommand(SrdCommand.LOGIN, undefined, true);
} /*  */

function shwoExtensionInMarketplace() {
  const extensionId =
    process.env.ISSEC !== 'false' ? SECIDEA_EXTENSION : 'srdcloud.srd-copilot-vscode';
  vscode.commands
    .executeCommand('workbench.extensions.action.showExtensionsWithIds', [extensionId])
    .then(() => {
      // 显示插件详情页
      vscode.commands.executeCommand('extension.open', extensionId);
    });
}

/**
 * 前往研发云登录认证，通过oauth2获取userId,sessionId
 */
export async function tryLoginBySrdcloud(showMessage?: boolean) {
  // 展示消息通知，以触发登录
  if (showMessage) {
    // 先静默处理，让AccountMenu有登录提示
    await getAuthenticationSession();

    // 展示消息通知
    const selection = await vscode.window.showInformationMessage(
      process.env.ISSEC !== 'false'
        ? '请前往海云安平台进行登录认证'
        : '请前往研发云平台进行登录认证',
      '前往',
      '取消'
    );

    if (selection === '前往') {
      getAuthenticationSession(true);
    }
  } else {
    // 不展示消息通知，直接触发登录
    getAuthenticationSession(true);
  }
}

/**
 * 本地AuthenticationToken有效性检查
 * @param context
 * @returns
 */
async function checkIfAuthToken() {
  const authToken = await getAuthToken();

  return !!authToken;
}

/**
 * 本地是否已获取到userId, sessionId
 * @param context
 */
async function checkIfCurrentUser() {
  const user = await getCurrentUser();

  return !!(user.userId && user.sessionId);
}
