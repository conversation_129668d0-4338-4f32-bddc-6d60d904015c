import * as vscode from 'vscode';
import { Logger } from './utils/logger';
import { registerChatView } from './codechat';
import { registerSrdAuthentication } from './authentication';
import {
  clearAuthToken,
  clearCurrentUser,
  setCurrentUser,
  setGlobalContext,
} from './common/globalContext';
import { registerSrdStatusBar } from './statusbar';
import { GlobalStateKey, RuntimeEnv } from './common/constants';
import { registerCodeComplete } from './codecomplete';
import { registerCodeNatural } from './codenatural';
import { setRuntimeEnv } from './adapter/common';
import { registerSidebarToolbar } from './toolbar';
import { registerCodeExceptionFix } from './codeexception';
import { FileLockUtil } from './agent/utils/lockUtil';
import { registerSettings } from './settings';
import { registerCheckUpdate } from './updater';
import { startHttpServer } from './login/browserRedirectListener';
import { userInfoManager } from './login/loginUtils';
import { listenAuthFile } from './login/loginStateUpdate';
import { registerCodeLens } from './codelens';

import { registerWorkItemList } from './workItem';
/**
 * lifecycle activate
 * @param context 上下文
 */
export async function activate(context: vscode.ExtensionContext) {
  context.subscriptions.push(Logger);
  // 保存插件Context
  setGlobalContext(context);
  // 同步全局配置参数
  syncGlobalState(context);
  if (process.env.ISSEC !== 'false') {
    const port = await userInfoManager.getLoginCallbackPort();
    // 启动HTTP服务器
    if (port !== null) {
      startHttpServer(context, port);
    }
    listenAuthFile();
  }

  Logger.info(`[extension] "srd-copilot" is now active`);

  await tryGetUInfoFromIDE();

  // 注册Authentication
  registerSrdAuthentication(context);

  // 注册配置页面
  registerSettings(context);

  // 注册检测更新
  registerCheckUpdate(context);

  // 注册StatusBar
  const statusBar = registerSrdStatusBar(context);

  //    注册CodeComplete
  registerCodeComplete(context);

  // 注册CodeChat in ActivityBar
  registerChatView(context, statusBar);

  // 注册CodeNatural
  // registerCodeNatural(context, statusBar);

  registerSidebarToolbar(context);

  registerCodeExceptionFix(context, statusBar);

  // 注册工作项列表和触发button
  if (process.env.ISSEC !== 'true') {
    registerWorkItemList(context);
  }

  // 注册Codelens
  registerCodeLens(context);
}

// This method is called when your extension is deactivated
export async function deactivate() {
  try {
    await FileLockUtil.releaseAllLocks();
    Logger.info('Extension deactivated cleanly.');
  } catch (err) {
    Logger.error(`Error during extension deactivation:${err}`);
  }
}

/**
 * 同步全局配置参数, 暂无
 */
function syncGlobalState(context: vscode.ExtensionContext) {
  const keys = Object.values(GlobalStateKey);
  context.globalState.setKeysForSync(keys);
}

/**
 * 判断是否为云IDE环境，并获取云IDE的用户信息
 * @param context
 */
async function tryGetUInfoFromIDE() {
  if (process.env.USER_ID && process.env.SESSION_ID) {
    // 设置当前环境为云IDE环境
    setRuntimeEnv(RuntimeEnv.CLOUD);

    const uInfo = {
      userId: process.env.USER_ID,
      sessionId: process.env.SESSION_ID,
      // TODO: 待后续云IDE提供process.env.USER_ACCOUNT,云IDE账户展示不出来，目前不提供也可以
      userAccount: 'code_cloud_user',
    };

    await clearAuthToken();
    await setCurrentUser(uInfo);

    Logger.info(
      `[extension] run in CloudIDE, vscode version: ${vscode.version}, user: ${JSON.stringify(
        uInfo
      )}`
    );
  }
}
