import { Sign } from 'crypto';
import { ChatMessageType } from '../common/constants';
import { ActivityType } from '../service/types/dataReport';
import { ChatMessageSimple } from './conversation';
import * as vscode from 'vscode';
import { CodeAIResponsePayload } from '../service/types/codeAI';
import { DiffFile, DiffMsgData } from '../diff/types';
import { ContextInputItem } from '../composer/types';
import { ChatHistory } from '../composer/history/AgentHistoryUtil';

export interface WebViewRequest {
  command: string;
  data: unknown;
}

export interface WebViewResponse {
  command: string;
  type?: string;
  data?: unknown;
}

export interface ChatRequest {
  question: string;
  createTime: string;
  type?: ChatMessageType;
  text?: string;
  dialogId?: string;
  questionAskType?: string;
  parentReqId?: string;
  subService?: string;
  templateId?: string;
  chatContext?: ChatMessageSimple;
  kbId?: number;
  quote?: KbQuoteParams[];
  relatedFiles?: RelatedFile[];
  contextInputItems?: ContextInputItem[];
  selectedWorkItems?: selectedWorkItem[];
}

export interface selectedWorkItem {
  workItemKey: string;
  description: string;
  title: string;
}

export interface RelatedFile {
  path: string; //文件绝对路径
  text?: string;
  startLine?: number;
  endLine?: number;
  codebase?: boolean; //是否为@codebase关联文件，
  folderPath?: string; //如果本对象是@folder，则为folder值，否则为空值
}

export type PartialChatRequest = Pick<
  ChatRequest,
  'dialogId' | 'questionAskType' | 'parentReqId' | 'templateId' | 'kbId' | 'quote' | 'relatedFiles' | 'selectedWorkItems'
>;

export interface KbQuoteParams {
  metadata: KbQuoteMetaData;
  page_content: string;
  type: string;
}

export interface KbQuoteMetaData {
  fileName: string;
  full_text: string;
  knowledge_base: string;
  source: string;
}

export interface ConversationChangeParams {
  eventType: ConversationEventType;
  data: unknown;
  code?: number | string;
  error?: string;
  isNewConversation?: boolean;
}

export interface diagnosticCodeType {
  value: string | number;
  target: vscode.Uri;
}

export enum ConversationEventType {
  DATA_LOADED = 'dataloaded',
  DATA_UPDATED = 'dataupdated',
  DATA_REFRESHED = 'datarefreshed',
  DATA_REMOVED = 'dataremoved',
  DATA_ADDED = 'dataaded',
}

export enum CodeSelectionEventType {
  CODE_SELECTION_ASKED = 'codeselectionasked',
  CODE_SELECTION_CHANGED = 'codeselectionchanged',
}

export interface CodeSelectionParams {
  type: ChatMessageType;
  code: string;
  text?: string;
  isNewConversation?: boolean;
  cancelLastQuestion?: boolean;
}

export interface ICodeChatEnginHandler {
  onCodeChatResponse(
    reqId?: string,
    isEnd?: number,
    answer?: string,
    error?: string,
    code?: number,
    payload?: CodeAIResponsePayload
  ): void;

  onCurReqIdChange(newReqId?: string): void;
}

export interface IConversationHandler {
  onConversationChange(params: ConversationChangeParams): void;
}

export interface ICodeScanHandler {
  handleCodeScanEvent(response: WebViewResponse): void;
}

export interface IFileChatHandler {
  handleFileChatEvent(response: WebViewResponse): void;
}
export interface IComposerServiceHandler {
  onComposerChatResponse(reqType: string, dialogId: string, done: boolean, content: string): void;
  onReportDiffFilesResponse(reqType: string, dialogId: string, content: DiffFile[]): void;
  onComposerHistoryResponse(data: { reqType: string, historyList?: ChatHistory[] | null, error?: string }): void;
}

export interface IDiffServiceHandler { 
  onDiffStatusChanged(diffFiles: DiffFile[]): void; 
}

export interface DiffFileResult {
  path: string; // 文件相对于项目的路径（可能为修改或新建）
  beforeContent: string; // 文件修改前的内容（不要使用该字段，并非文件原始内容）
  afterContent: string; // 文件修改后的内容
}

export interface ICodeSelectionHandler {
  handleCodeSelectionEvent(eventType: CodeSelectionEventType, data: unknown): void;
  sendMessageToWebView(response: WebViewResponse): void;
}

export interface IIdeUtilHandler {
  handleIdeUtilEvent(response: WebViewResponse): void;
}

export interface SrdChatRequest {
  type: string;
  question?: string;
  timeBefore?: string;
  currentPage?: number;
  pageSize?: number;
  requestBody?: unknown;
}

export interface dataReportParams {
  activityType: ActivityType;
  answer: string;
}

export interface PromptsRequest {
  reqType: string;
  type: string;
  pageNum?: number;
  name?: string;
  lastUsed?: boolean;
  categoryId?: string;
  pageDataCount?: number;
  templateId?: string;
  operationType?: string;
}

export interface KnowledgeBaseRequest {
  reqType: string;
  kbId?: number;
  currentPage?: number;
  keyword?: string;
  pageSize?: number;
}

export interface ChatHistoryRequest {
  pageNum?: number;
  pageDataCount?: number;
  title?: string;
  subService?: string;
  subServices?: string;
  dialogId?: string;
  isFromSearch?: boolean;
}

export interface AnswerStopParams {
  reqId: string;
  dialogId: string;
  questionType: string;
  parentReqId?: string;
  subService: string;
  system?: string;
  question: string;
  answer: string;
  templateId?: string | number;
  kbId?: number;
  modelName?: string;
  qaQuestion?: string;
  quote?: KbQuoteParams[];
  files?: RelatedFile[];
}

export interface FeedbackAnswerRequest {
  reqId: string;
  dialogId: string;
  type: number;
  feedback: string;
}

export interface UploadScanFileRequest {
  language?: string;
  fileSize: number;
  scannerEngine: string;
  scanFiles?: string[];
  reqType?: string;
}

export interface QueryIssuesRequest {
  taskId: string;
  page?: number;
  pageSize?: number;
  reqType?: string;
}

export interface ViewDetailParams {
  issueId?: string;
  rule?: string;
  fileName: string;
  line: number;
  message?: string;
  severity?: number;
  detail?: string;
}

export interface aiExplainParams {
  reqType: string;
  scannerEngine: string;
  filePath: string;
  issueList: issue[];
}

export interface issue {
  issueId: string;
  rule: string;
  message: string;
  line: number;
  language?: string;
}

export interface DiffViewVerticalParams {
  reqType: string;
  path: string;
  content: string;
  originalContent?: string;
}

export interface ComposerHistoryParams {
  reqType: string;
  dialogId: string;
}

export enum SrdChatEventType {
  ASK_QUESTION = 'askquestion',
  RECORD_LIST = 'recordlist',
  RECORD_ANSWER = 'recordanswer',
}

export enum PromptsEventType {
  GET_TEMPLATES = 'gettemplates',
  OPERATE_TEMPLATE = 'operatetemplate',
  GET_CATEGORIES = 'getcategories',
}

export enum KnowledgeBaseEventType {
  SEARCH_DEV_KBS = 'searchdevkbs',
  GET_KB_INFO = 'getkbinfo',
}

export enum CodeSecurityEventType {
  GET_SCAN_FILES = 'getscanfiles',
  RUN_SCAN = 'runscan',
  QUERY_SCAN_ISSUES = 'queryscanissues',
  VIEW_DETAIL = 'viewdetail',
  STOP_SCAN = 'stopscan',
  AI_EXPLAIN = 'aiexplain',
  STOP_AI_REQUEST = 'stopairequest',
}

export enum ComposerEventType {
  COMPOSER_CHAT = 'composerchat',
  STOP_COMPOSER_CHAT = 'stopcomposerchat',
  REPORT_DIFF_FILES = 'reportdifffiles',
  LOAD_HISTORY = 'loadhistory',
  DELETE_HISTORY = 'deletehistory',
}

export enum WorkItemEventType {
  SEARCH_WORKITEMS = 'searchworkitems',
  OPEN_WORKITEM_PANEL = 'openworkitempanel'
}

export enum DiffViewVerticalEventType {
  OPEN_DIFF_VIEW_VERTICAL = 'opendiffviewvertical',
  ACCEPT_FILE_DIFF = 'acceptfilediff',
  UNDO_FILE_DIFF = 'undofilediff',
  REJECT_FILE_DIFF = 'rejectfilediff',
}

export enum IndexingEventType {
  START_INDEXING = 'startindexing',
  SET_INDEXING_PAUSED = 'setindexingpaused',
}

export enum FileChatEventType {
  GET_RELATED_FILES = 'getrelatedfiles',
}

export interface FileNode {
  name: string;
  size?: number;
  path?: string;
  relativePath?: string;
  children?: FileNode[];
}

export interface RecentlyUsedFile {
  path: string;
  relativePath: string;
  name: string;
  size: number;
}

export interface DiffViewParams {
  filePath: string;
  originalContent: string;
  generatedContent: string;
}

export enum IdeUtilEventType {
  GET_CHAT_CONTEXTS = 'getchatcontexts',
}

export interface DirItem {
  name: string;
  path: string;
  relativePath: string;
}

export interface TerminalParams { 
  reqType: string;
  command: string;
}

export enum TerminalEventType { 
  EXECUTE_COMMAND = 'executecommand'
}

export interface WorkItemResponse {
  reqType: string;
  workItemList: Array<{
    id: string;
    workItemKey: string;
    description: string;
    title: string;
    creatorDisplayName: string;
    url: string;
  }>;
  error?: string;
}

export interface WorkItemParams {
  reqType: string;
  searchParam: any;
}