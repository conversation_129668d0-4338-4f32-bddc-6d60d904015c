import {
  InteractiveEditorSession,
  Range,
  InteractiveEditorSlashCommand,
  TextEditor,
  TextDocument,
  WorkspaceEdit,
  MarkdownString,
  TextEdit,
  InteractiveEditorMessageResponse,
} from 'vscode';

export class SrdInteractiveEditorSession implements InteractiveEditorSession {
  public placeholder?: string;

  public input?: string;

  public slashCommands?: InteractiveEditorSlashCommand[];

  public wholeRange?: Range;

  public message?: string;

  public editor: TextEditor;

  public document: TextDocument;

  public constructor(
    editor: TextEditor,
    document: TextDocument,
    placeholder: string,
    message: string
  ) {
    this.editor = editor;
    this.document = document;
    this.placeholder = placeholder;
    this.message = message;
  }
}

export class SrdInteractiveEditorResponse implements InteractiveEditorMessageResponse {
  public contents: MarkdownString;

  public placeholder?: string | undefined;

  public wholeRange?: Range | undefined;

  public constructor(contents: MarkdownString, wholeRange?: Range) {
    this.contents = contents;
    this.wholeRange = wholeRange;
  }
}

export interface AnswerEndParams {
  reqId: string;
  range: Range;
  wholeAnswer: string;
}
