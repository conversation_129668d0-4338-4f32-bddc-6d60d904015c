import { MultiMediaContent } from '../../codechat/conversation';
import { KbQuoteParams, selectedWorkItem } from '../../codechat/types';
import { ImportSnippet } from '../../codecomplete/types';
import { ChangeItem, WorkItem } from '../../workItem/types';

export interface RequestMessage {
  messageName: string;
  reqId?: string;
  appGid?: string;
  clientType?: string;
  fileName?: string;
  prefix?: string;
  suffix?: string;
  language?: string;
  question?: string;
  manualType?: number;
  maxNewTokens?: number;
}

export interface CodeAIRequestContext {
  messageName: string;
  reqId?: string;
  appGId?: string;
  invokerId: string;
  sessionId?: string;
  version: string;
  optResult?: number;
  msg?: string;
  apiKey?: string;
}

export interface CodeAIRequestPayload {
  clientType?: string;
  clientVersion?: string;
  gitUrls?: string[];
  messages?: CodeMessage;

  client?: CodeAIClientInfo;
  activityType?: string;
  lines?: number;
  count?: number;
  clientPlatform?: string;
  isAuto?: boolean;
  latency?: number;
}

export interface CodeAIRouteConditionInfo {
  version?: string;
  payload?: CodeAIConditonPayload;
}

export interface CodeAIConditonPayload {
  templateId?: string | number;
}

export interface CodeAIClientInfo {
  type: string;
  version: string;
  pluginVersion: string;
  gitUrl: string;
  gitUrls: string[];
  projectName: string;
}

export interface CodeMessage {
  language?: string;
  filename?: string;
  prefix?: string;
  suffix?: string;
  max_new_tokens?: number;
  prompts?: CodeAIRequestPromptChat[];
  stop_words?: string[];
  sub_service?: string;
  dialogId?: string;
  questionAskType?: string;
  parentReqId?: string;
  modelRouteCondition?: CodeAIRouteConditionInfo;
  kbId?: number;
  quote?: KbQuoteParams;
  importSnippets?: ImportSnippet[];
  diffList?: ChangeItem[];
  workitemTitle?: string;
  workitemDescription?: string;
  workItemList?: { title: string; description: string }[]
}

export interface CodeAIRequestPromptChat {
  files?: FileInfo[];
  content: string;
  role: string;
  workItems?: selectedWorkItem[];
}

export interface FileInfo {
  path: string;  //文件绝对路径
  text?: string;
  startLine?: number;
  endLine?: number;
}

export class CodeAIRequestMessage {
  public messageName: string;

  public context!: CodeAIRequestContext;

  public payload!: CodeAIRequestPayload;

  public constructor(messageName: string, context?: CodeAIRequestContext) {
    this.messageName = messageName;

    if (context) {
      this.context = context;
    }
  }

  public setContext(context: CodeAIRequestContext) {
    this.context = context;
  }

  public setPayload(payload: CodeAIRequestPayload) {
    this.payload = payload;
  }

  public toString() {
    return JSON.stringify({
      messageName: this.messageName,
      context: this.context,
      payload: this.payload,
    });
  }
}

export interface CodeAIResponseContext {
  messageName: string;
  reqId: string;
  invokerId: string;
  optResult: number;
  msg: string;
}

export interface CodeAIResponsePayload {
  apiKey: string;
  seqId: number;
  retCode: number;
  isEnd: number;
  answer: string;
  clientLatestVersion?: string;
  config?: CodeAIResponseConfig;
  errMsg?: string;
  inValid?: boolean;
  quote?: KbQuoteRecv;
  agentVersion?: agentConfig[];
  clientLatestVersionContent?: string;
  clientLatestVersionDownloadUrl?: string;
  clientVersionContent?: string;
}

export interface agentConfig {
  type: string, // agent类型
  version: string, // agent版本号
  downloadUrl: string, // agent下载地址
  md5: string, // md5校验哈希
}

export interface CodeAIResponseConfig {
  ChatCharacterLimit: string;
  InputCharacterLimit: string;
  SnippetsCharacterLimit: string;
  ClientUrlSubPath: string;
  PrintCacheConfig: unknown;
  indexVersion: string;
  ignoreList: string;
  codeCompleteStrategy: string;
}

export interface chatUIConfig { 
  config: CodeAIResponseConfig,
  versionDesc: string;
}


export interface CodeAIResponseMessage {
  messageName: string;
  context: CodeAIResponseContext;
  payload?: CodeAIResponsePayload;
}

export interface KbQuoteRecv {
  api: KbQuoteParams[];
  document: KbQuoteParams[];
  guide: KbQuoteParams[];
}

export interface ICodeAIReceiverResult {
  onRegisterChannelResult(code: number): void;
  onGetApiKeyResult(code: number, payload?: CodeAIResponsePayload): void;
  onTaskErrorOrClose(isTerminate: boolean, code?: number): void;
  onWSReconnect(isTerminate: boolean, code?: number): void;
}

export interface ICodeAIDataReportReceiveHandler {
  onUserActivityNotify(code: number): void;
}

export interface IAnswerHandler {
  onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void;

  getReqId(): string;
}
