export enum CodeNaturalEventType {
  ADD_TASK = 1,
  TASK_ON_ANSWER_END = 2,
  TASK_ON_ERROR = 3,
  TASK_ON_FINISHED = 4,
  TASK_ON_REGENERATE = 5,
}

export enum CodeNaturalDataType {
  TASK = 'task',
}

export enum CodeNaturalTaskState {
  Init = 1,
  WaitingResp = 2,
  OnAnswering = 3,
  OnAcceptOrReject = 4,
  OnFinished = 5,
}

export interface CodeNaturalRequest {
  question: string;
  filename: string;
  language: string;
  prefix: string;
  suffix: string;
  manualType: number;
}

export interface CodeSnippet {
  answer: string;
  isEnd: number;
}

export enum CodeNaturalTaskType {
  INPUTBOX = 1,
  INTERACTIVE = 2,
}

export interface ICodeNaturalEnginHandler {
  onCodeNaturalResponse(reqId: string, isEnd: number, answer: string, code: number): void;
}

export interface ICodeNaturalEventHandler {
  handleEvent(eventType: CodeNaturalEventType, params: unknown): void;
  getEventData(dataType: CodeNaturalDataType, params: unknown): unknown;
}
