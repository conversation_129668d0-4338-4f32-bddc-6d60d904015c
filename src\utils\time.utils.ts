export const SECOND_IN_MS = 1000;
export const MINUTE_IN_MS = 60 * SECOND_IN_MS;
const HOUR_IN_MS = 60 * MINUTE_IN_MS;

export function isInTheLastHour(date: Date): boolean {
  return Date.now() - date.getTime() < HOUR_IN_MS;
}

/**
 * 格式化时间
 */
export function formatDate(date: Date, format = 'yyyy-MM-dd hh:mm:ss.S') {
  const o = {
    'M+': date.getMonth() + 1, //月份
    'd+': date.getDate(), //日
    'h+': date.getHours(), //小时
    'm+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
    'q+': Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      const val = (o as any)[k];
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? val : ('00' + val).substr(('' + val).length)
      );
    }
  }

  return format;
}
