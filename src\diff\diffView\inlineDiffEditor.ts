import * as vscode from 'vscode';
import { diffLines } from 'diff';
interface DiffInfo {
  originalText: string;
  newText: string;
  originalRange: vscode.Range;
  newRange: vscode.Range;
}
export class DiffManager {
  private additionDecoration: vscode.TextEditorDecorationType;
  private deletionDecoration: vscode.TextEditorDecorationType;
  private currentDecorations: vscode.TextEditorDecorationType[] = [];
  public codeLensProvider: DiffCodeLensProvider;
  private diffMap: Map<string, DiffInfo> = new Map();
  private currentAddedRanges: vscode.Range[] = [];
  private currentDeletedRanges: vscode.Range[] = [];
  private disposables: vscode.Disposable[] = [];

  constructor() {
    // this.initializeDecorations();
    this.additionDecoration = vscode.window.createTextEditorDecorationType({
      backgroundColor: 'rgba(155, 185, 85, 0.2)',
      isWholeLine: true,
      before: {
        contentText: '+',
        color: '#87d96c',
        margin: '0 4px',
      },
    });

    this.deletionDecoration = vscode.window.createTextEditorDecorationType({
      backgroundColor: 'rgba(255, 0, 0, 0.2)',
      isWholeLine: true,
      before: {
        contentText: '-',
        color: '#f14c4c',
        margin: '0 4px',
      },
    });
    this.codeLensProvider = new DiffCodeLensProvider();
  }

  async showInlineDiff(editor: vscode.TextEditor, newContent: string) {
    const document = editor.document;
    const originalText = document.getText();
    const diffs = diffLines(originalText, newContent, { ignoreWhitespace: false });

    this.codeLensProvider.clearRanges();
    this.currentAddedRanges = [];
    this.currentDeletedRanges = [];
    this.diffMap.clear();

    let currentLine = 0;
    let offset = 0;

    const workspaceEdit = new vscode.WorkspaceEdit();

    for (let i = 0; i < diffs.length; i++) {
      const diff = diffs[i];
      const lines = diff.value.split('\n');
      const lineCount = diff.value.endsWith('\n') ? lines.length - 1 : lines.length;

      if (diff.removed) {
        // 处理删除的代码
        const originalRange = new vscode.Range(
          currentLine + offset,
          0,
          currentLine + offset + lineCount - 1,
          lines[lineCount - 1].length
        );

        this.currentDeletedRanges.push(originalRange);

        // 检查下一个diff是否是对应的added
        const nextDiff = diffs[i + 1];
        if (nextDiff && nextDiff.added) {
          // 处理修改场景（删除+添加）
          const newLines = nextDiff.value.split('\n');
          const newLineCount = nextDiff.value.endsWith('\n')
            ? newLines.length - 1
            : newLines.length;

          const newRange = new vscode.Range(
            currentLine + offset + lineCount + 1,
            0,
            currentLine + offset + lineCount + 1 + newLineCount - 1,
            newLines[newLineCount - 1].length
          );

          workspaceEdit.insert(
            document.uri,
            new vscode.Position(currentLine + offset + lineCount + 1, 0),
            nextDiff.value
            // nextDiff.value.endsWith('\n') ? nextDiff.value.slice(0, -1) : nextDiff.value
          );

          this.currentAddedRanges.push(newRange);

          const key = `${originalRange.start.line}-${originalRange.end.line}`;
          this.diffMap.set(key, {
            originalText: diff.value,
            newText: nextDiff.value,
            originalRange,
            newRange,
          });

          this.codeLensProvider.addRange(originalRange, 'modified');

          offset += lineCount + newLineCount + 1;
          i++; // 跳过下一个diff
        } else {
          // 纯删除场景
          const key = `${originalRange.start.line}-${originalRange.end.line}`;
          this.diffMap.set(key, {
            originalText: diff.value,
            newText: '',
            originalRange,
            // 删除场景下新范围等于原始范围
            newRange: originalRange,
          });

          this.codeLensProvider.addRange(originalRange, 'deleted');
          offset += lineCount;
        }
      } else if (diff.added) {
        // 纯添加场景
        const newRange = new vscode.Range(
          currentLine + offset,
          0,
          currentLine + offset + lineCount - 1,
          lines[lineCount - 1].length
        );

        // 在新行插入内容
        workspaceEdit.insert(
          document.uri,
          new vscode.Position(currentLine + offset, 0),
          diff.value
          // diff.value.endsWith('\n') ? diff.value.slice(0, -1) : diff.value
        );

        this.currentAddedRanges.push(newRange);

        const key = `${newRange.start.line}-${newRange.end.line}`;
        this.diffMap.set(key, {
          originalText: '',
          newText: diff.value,
          // 添加场景下原始范围等于新范围
          originalRange: newRange,
          newRange,
        });

        this.codeLensProvider.addRange(newRange, 'added');
        offset += lineCount;
      } else {
        currentLine += lineCount;
      }
    }

    // 应用编辑和装饰器
    await vscode.workspace.applyEdit(workspaceEdit);
    editor.setDecorations(this.additionDecoration, this.currentAddedRanges);
    editor.setDecorations(this.deletionDecoration, this.currentDeletedRanges);

    // 添加文档变化监听器
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument(async e => {
        if (e.document === editor.document) {
          await this.updateRangesAfterEdit(editor, e.contentChanges);
        }
      })
    );
  }

  private async updateRangesAfterEdit(
    editor: vscode.TextEditor,
    changes: readonly vscode.TextDocumentContentChangeEvent[]
  ) {
    // 计算行号偏移
    let lineOffset = 0;
    for (const change of changes) {
      const startLine = change.range.start.line;
      const endLine = change.range.end.line;
      const newLines = change.text.split('\n').length - 1;
      const removedLines = endLine - startLine;
      lineOffset += newLines - removedLines;
    }

    if (lineOffset === 0) return; // 如果行数没有变化，不需要更新

    // 更新所有范围
    // 更新所有范围
    const updatedDiffMap = new Map<string, DiffInfo>();
    const updatedAddedRanges: vscode.Range[] = [];
    const updatedDeletedRanges: vscode.Range[] = [];

    for (const [key, info] of this.diffMap) {
      // 对原始范围和新范围都应用偏移
      let newOriginalStartLine = info.originalRange.start.line;
      let newOriginalEndLine = info.originalRange.end.line;
      let newRangeStartLine = info.newRange.start.line;
      let newRangeEndLine = info.newRange.end.line;

      // 对于在变化位置之后的范围，应用偏移
      for (const change of changes) {
        if (info.originalRange.start.line > change.range.start.line) {
          newOriginalStartLine += lineOffset;
          newOriginalEndLine += lineOffset;
        }
        if (info.newRange.start.line > change.range.start.line) {
          newRangeStartLine += lineOffset;
          newRangeEndLine += lineOffset;
        }
      }

      // 创建新的范围
      const newOriginalRange = new vscode.Range(
        newOriginalStartLine,
        info.originalRange.start.character,
        newOriginalEndLine,
        info.originalRange.end.character
      );

      const newRange = new vscode.Range(
        newRangeStartLine,
        info.newRange.start.character,
        newRangeEndLine,
        info.newRange.end.character
      );

      // 更新信息
      const updatedInfo: DiffInfo = {
        originalText: info.originalText,
        newText: info.newText,
        originalRange: newOriginalRange,
        newRange: newRange,
      };

      // 存储更新后的信息
      const newKey = `${newOriginalRange.start.line}-${newOriginalRange.end.line}`;
      updatedDiffMap.set(newKey, updatedInfo);

      // 更新装饰器范围
      if (info.originalText && !info.newText) {
        updatedDeletedRanges.push(newOriginalRange);
      } else if (!info.originalText && info.newText) {
        updatedAddedRanges.push(newRange);
      } else {
        // 对于修改场景，两个范围都需要装饰
        updatedDeletedRanges.push(newOriginalRange);
        updatedAddedRanges.push(newRange);
      }
    }

    // 应用更新
    this.diffMap = updatedDiffMap;
    this.currentAddedRanges = updatedAddedRanges;
    this.currentDeletedRanges = updatedDeletedRanges;

    // 更新装饰器
    editor.setDecorations(this.additionDecoration, this.currentAddedRanges);
    editor.setDecorations(this.deletionDecoration, this.currentDeletedRanges);

    // 更新 CodeLens
    this.codeLensProvider.updateRanges(Array.from(this.diffMap.values()));
  }

  async acceptChange(range: vscode.Range) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    console.log('Accept change for range:', range);

    const diffInfo = this.findDiffInfo(range);
    if (!diffInfo) return;

    const workspaceEdit = new vscode.WorkspaceEdit();

    if (diffInfo.originalText && !diffInfo.newText) {
      // 处理纯删除场景
      console.log('Handling pure deletion');

      // 1. 获取要删除的范围
      const deleteRange = new vscode.Range(
        diffInfo.originalRange.start,
        // 确保包含整行（包括换行符）
        new vscode.Position(diffInfo.originalRange.end.line + 1, 0)
      );

      // 2. 获取文档总行数
      const document = editor.document;
      const totalLines = document.lineCount;

      // 3. 如果删除范围后还有内容，需要将后续内容向上移动
      if (deleteRange.end.line < totalLines) {
        // 获取后续所有内容
        const remainingText = document.getText(
          new vscode.Range(deleteRange.end, new vscode.Position(totalLines, 0))
        );

        if (remainingText.length > 0) {
          // 删除整个范围（包括被删除的代码和后续内容）
          workspaceEdit.delete(
            document.uri,
            new vscode.Range(deleteRange.start, new vscode.Position(totalLines, 0))
          );

          // 在删除起始位置插入后续内容
          workspaceEdit.insert(
            document.uri,
            deleteRange.start,
            remainingText.startsWith('\n') ? remainingText.substring(1) : remainingText
          );
        } else {
          // 如果后面没有内容，直接删除范围
          workspaceEdit.delete(document.uri, deleteRange);
        }
      } else {
        // 如果是文档最后的内容，直接删除
        workspaceEdit.delete(document.uri, deleteRange);
      }
    } else if (!diffInfo.originalText && diffInfo.newText) {
      // 处理纯添加场景
      // 保持新增的代码
      console.log('Handling pure addition');
    } else {
      // 处理修改场景
      console.log('Handling modification');
      // 删除原始代码和新代码（包括分隔的空行）
      const fullRange = new vscode.Range(
        diffInfo.originalRange.start,
        new vscode.Position(diffInfo.newRange.end.line + 1, 0)
      );

      workspaceEdit.delete(editor.document.uri, fullRange);

      // 插入新代码
      workspaceEdit.insert(
        editor.document.uri,
        diffInfo.originalRange.start,
        diffInfo.newText
        // diffInfo.newText.endsWith('\n') ? diffInfo.newText.slice(0, -1) : diffInfo.newText
      );
    }

    // 执行编辑
    await vscode.workspace.applyEdit(workspaceEdit);

    // 移除装饰器和 CodeLens
    await this.removeChange(diffInfo.originalRange);
  }

  async rejectChange(range: vscode.Range) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    const diffInfo = this.findDiffInfo(range);
    if (!diffInfo) return;

    const workspaceEdit = new vscode.WorkspaceEdit();

    // 删除原始代码和新代码
    workspaceEdit.delete(
      editor.document.uri,
      new vscode.Range(diffInfo.originalRange.start, diffInfo.newRange.end)
    );

    // 保留原始代码
    workspaceEdit.insert(
      editor.document.uri,
      diffInfo.originalRange.start,
      diffInfo.originalText.endsWith('\n')
        ? diffInfo.originalText.slice(0, -1)
        : diffInfo.originalText
    );

    // 执行编辑
    await vscode.workspace.applyEdit(workspaceEdit);

    // 移除装饰器和 CodeLens
    await this.removeChange(range);
  }

  private findDiffInfo(range: vscode.Range) {
    console.log('Finding diff info for range:', range);

    // 遍历 diffMap 查找匹配的范围
    for (const [key, info] of this.diffMap.entries()) {
      // 检查是否匹配原始代码范围
      if (info.originalRange.start.line === range.start.line) {
        console.log('Found matching diff info:', info);
        return info;
      }
    }

    console.log('No matching diff info found');
    return null;
  }

  private rangesAreEqual(range1: vscode.Range, range2: vscode.Range): boolean {
    return range1.start.line === range2.start.line && range1.end.line === range2.end.line;
  }

  private async removeChange(range: vscode.Range) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    console.log('Removing change for range:', range);

    // 查找完整的 diffInfo
    const diffInfo = this.findDiffInfo(range);
    if (!diffInfo) return;

    // 从当前范围数组中移除
    this.currentAddedRanges = this.currentAddedRanges.filter(
      r => !this.rangesAreEqual(r, diffInfo.newRange)
    );
    this.currentDeletedRanges = this.currentDeletedRanges.filter(
      r => !this.rangesAreEqual(r, diffInfo.originalRange)
    );

    // 重新应用装饰器
    editor.setDecorations(this.additionDecoration, this.currentAddedRanges);
    editor.setDecorations(this.deletionDecoration, this.currentDeletedRanges);

    // 从 diffMap 中移除
    const key = `${diffInfo.originalRange.start.line}-${diffInfo.originalRange.end.line}`;
    this.diffMap.delete(key);

    // 从 CodeLens provider 中移除该范围
    this.codeLensProvider.removeRange(diffInfo.originalRange);

    // 如果所有更改都处理完了，清理所有装饰器
    if (this.diffMap.size === 0) {
      await this.clearAllDecorations();
    }
  }

  private async clearAllDecorations() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    // 清空所有范围
    this.currentAddedRanges = [];
    this.currentDeletedRanges = [];

    // 清除所有装饰器
    editor.setDecorations(this.additionDecoration, []);
    editor.setDecorations(this.deletionDecoration, []);

    // 清除所有 CodeLens
    this.codeLensProvider.clearRanges();

    // 清空 diffMap
    this.diffMap.clear();
  }

  dispose() {
    this.clearAllDecorations();
    this.currentDecorations.forEach(d => d.dispose());
  }
}

class DiffCodeLensProvider implements vscode.CodeLensProvider {
  private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
  public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;

  private diffRanges: {
    originalRange: vscode.Range;
    newRange: vscode.Range;
    type: 'modified' | 'added' | 'deleted';
  }[] = [];

  public updateRanges(diffInfos: DiffInfo[]) {
    this.diffRanges = diffInfos.map(info => ({
      originalRange: info.originalRange,
      newRange: info.newRange,
      type:
        info.originalText && info.newText ? 'modified' : info.originalText ? 'deleted' : 'added',
    }));

    this._onDidChangeCodeLenses.fire();
  }

  public addRange(range: vscode.Range, type: 'modified' | 'added' | 'deleted') {
    const codeLensRange = new vscode.Range(range.start.line, 0, range.start.line, 0);

    this.diffRanges.push({
      originalRange: codeLensRange,
      newRange:
        type === 'modified'
          ? new vscode.Range(range.end.line + 1, 0, range.end.line + 1, 0)
          : codeLensRange,
      type,
    });

    this._onDidChangeCodeLenses.fire();
  }

  public removeRange(range: vscode.Range) {
    const beforeLength = this.diffRanges.length;

    this.diffRanges = this.diffRanges.filter(
      dr => dr.originalRange.start.line !== range.start.line
    );

    if (beforeLength !== this.diffRanges.length) {
      this._onDidChangeCodeLenses.fire();
    }
  }

  public clearRanges() {
    this.diffRanges = [];
    this._onDidChangeCodeLenses.fire();
  }

  public provideCodeLenses(
    document: vscode.TextDocument,
    token: vscode.CancellationToken
  ): vscode.CodeLens[] {
    const codeLenses: vscode.CodeLens[] = [];

    for (const { originalRange, type } of this.diffRanges) {
      const acceptTitle =
        type === 'added'
          ? '$(check) Accept addition'
          : type === 'deleted'
          ? '$(check) Accept deletion'
          : '$(check) Accept changes';

      const rejectTitle =
        type === 'added'
          ? '$(x) Reject addition'
          : type === 'deleted'
          ? '$(x) Reject deletion'
          : '$(x) Reject changes';

      codeLenses.push(
        new vscode.CodeLens(originalRange, {
          title: acceptTitle,
          command: 'codeDiff.acceptChange',
          arguments: [originalRange],
        })
      );

      codeLenses.push(
        new vscode.CodeLens(originalRange, {
          title: rejectTitle,
          command: 'codeDiff.rejectChange',
          arguments: [originalRange],
        })
      );
    }

    return codeLenses;
  }
}
