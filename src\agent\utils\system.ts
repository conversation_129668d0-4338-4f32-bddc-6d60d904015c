import * as os from 'os';
import * as path from 'path';
import * as fs from 'fs';
import { promisify } from 'util';
import { exec } from 'child_process';
import { getAgentVersion } from '../../common/globalContext';
import { Logger } from '../../utils/logger';

export class SystemUtils {
  public static getOsType(): string {
    const platform = os.platform();
    if (platform === 'win32') {
      return 'win32';
    }
    if (platform === 'darwin') {
      return 'darwin';
    }
    return 'linux';
  }

  public static getCurrentUsername(): string {
    return os.userInfo().username;
  }

  public static getCPUArchitecture(): string {
    const arch = process.arch;

    switch (arch) {
      case 'arm64':
        return 'arm64';
      case 'x64':
        return 'x64';
      case 'ia32':
        return 'x86';
      default:
        return arch;
    }
  }

  // 使用示例
  // const cpuArch = getCPUArchitecture();

  public static getAgentBasePath(pluginName: string): string {
    const osType = this.getOsType();
    const homeDir = os.homedir();

    switch (osType) {
      case 'win32':
        return path.join(homeDir, '\\AppData\\Local\\', `.${pluginName}`);
      case 'darwin':
        return path.join(homeDir, `.${pluginName}`);
      default:
        return path.join(homeDir, '/.local/share/', `.${pluginName}`);
    }
  }

  public static getAgentDownloadPath(pluginName: string, agentName: string): string {
    const osType = this.getOsType();
    const basePath = this.getAgentBasePath(pluginName);
    switch (osType) {
      case 'win32':
        return path.join(basePath, '\\agent\\', agentName, '\\');
      case 'darwin':
        return path.join(basePath, '/agent/', agentName, '/');
      default:
        return path.join(basePath, '/agent/', agentName, '/');
    }
  }

  public static getAgentPath(pluginName: string, agentName: string): string {
    const osType = this.getOsType();
    let agentPath = this.getAgentFolder(pluginName, agentName);

    if (agentName === 'node') {
      if (osType === 'win32') {
        agentPath = path.join(agentPath, 'node.exe');
      } else {
        agentPath = path.join(agentPath, 'bin/node');
      }
    } else {
      agentPath = path.join(agentPath, 'index.js');
    }

    return agentPath;
  }


  public static getAgentFolder(pluginName: string, agentName: string): string {
    const osType = this.getOsType();
    const cpuArch = this.getCPUArchitecture();
    const basePath = this.getAgentBasePath(pluginName);
    let agentPath = '';
    const agentConfigs = getAgentVersion();
    let agentVersion = '';

    if (agentConfigs) {
      const agentConfig = agentConfigs.find(config => config.type === agentName);
      if (agentConfig) {
        agentVersion = agentConfig.version;
      }
    }
    switch (osType) {
      case 'win32':
        agentPath = path.join(basePath, '\\agent\\', agentName, '\\', `${agentName}-${agentVersion}-${osType}-${cpuArch}`, '\\');
        break;
      case 'darwin':
        agentPath = path.join(basePath, '/agent/', agentName, '/', `${agentName}-${agentVersion}-${osType}-${cpuArch}`, '/');
        break;
      default:
        agentPath = path.join(basePath, '/agent/', agentName, '/', `${agentName}-${agentVersion}-${osType}-${cpuArch}`, '/');
        break;
    }
    return agentPath;
  }

  public static getAgentConfigPath(pluginName: string): string {
    const osType = this.getOsType();
    const basePath = this.getAgentBasePath(pluginName);
    switch (osType) {
      case 'win32':
        return path.join(basePath, '\\agent\\config.json');
      case 'darwin':
        return path.join(basePath, '/agent/config.json');
      default:
        return path.join(basePath, '/agent/config.json');
    }
  }

  public static async removeQuarantine(filePath: string) {
    const execAsync = promisify(exec);
    const osType = this.getOsType()
    if (osType === 'darwin') {
      try {
        await execAsync(`xattr -dr com.apple.quarantine "${filePath}"`);
      } catch (error) {
        Logger.error(`Failed to remove quarantine from ${filePath}:${error}`);
      }
    }
  }

  public static getAgentConfig(pluginName: string): any {
    const configPath = this.getAgentConfigPath(pluginName);
    const config = fs.readFileSync(configPath, 'utf8');
    return JSON.parse(config);
  }

  public static getCpuArch(): string {
    return os.arch();
  }

  public static setPermission(filePath: string) {
    const osType = this.getOsType()
    if (osType === 'darwin' || osType === 'linux') {
      const stats = fs.statSync(filePath);
      const newMode = stats.mode | 0o755; // 使用位操作添加执行权限
      fs.promises.chmod(filePath, newMode);
    }

  }

  /**
   * 获取客户端平台信息, 用于发请求拿后端下发的参数
   * @returns 平台标识字符串
   */
  public static getClientPlatform(): string {
    const platform = process.platform;
    const arch = process.arch;

    if (platform === 'darwin') {
      return arch === 'arm64' ? 'macos-arm64' : 'macos-x64';
    } else if (platform === 'linux') {
      return arch === 'arm64' ? 'linux-arm64' : 'linux-x64';
    } else if (platform === 'win32') {
      return 'windows-x64';
    }

    return `${platform}-${arch}`;
  }

  /**
   * 获取本地配置文件，用于config.json文件中存储的平台信息
   * @returns 本地配置文件平台标识
   */
  public static getLocalConfig(): string {
    const platform = process.platform;
    const arch = process.arch;

    if (platform === 'darwin') {
      return arch === 'arm64' ? 'darwin-arm64' : 'darwin-x64';
    } else if (platform === 'linux') {
      return arch === 'arm64' ? 'linux-arm64' : 'linux-x64';
    } else if (platform === 'win32') {
      return 'win32-x64';
    }
    return `${platform}-${arch}`;
  }

  /**
   * 获取Agent锁文件的路径
   * @param pluginName 插件名称
   * @returns 锁文件的完整路径：basePath/agent/.lock/agent.lock
   */
  public static getAgentLockFilePath(pluginName: string): string {
    const osType = this.getOsType();
    const basePath = this.getAgentBasePath(pluginName);
    switch (osType) {
      case 'win32':
        return path.join(basePath, '\\agent\\.lock\\agent.lock');
      case 'darwin':
        return path.join(basePath, '/agent/.lock/agent.lock');
      default:
        return path.join(basePath, '/agent/.lock/agent.lock');
    }
  }

}
