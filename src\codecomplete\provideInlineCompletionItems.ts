import * as vscode from 'vscode';
import { getShouldComplete } from './inlineSuggestions/documentChangesTracker';
import { clearCurrentLookAheadSuggestion, getLookAheadSuggestion } from './lookAheadSuggestion';
import { debounceWithPromise } from '../utils/common';
import getInlineCompletionItems from './getInlineCompletionItems';
import { CODE_GEN_DEBOUNCE_DELAY } from '../common/config';
import { Logger } from '../utils/logger';
import AutoCompleteStatusStore from '../statusbar/autoCompleteStatusStore';
import TimeCounter from '../utils/timeCounter';
import RunCompletionCache from './runCompletionCache';

const END_OF_LINE_VALID_REGEX = new RegExp('^\\s*[)}\\]"\'`]*\\s*[:{;,]?\\s*$');

// 防抖300ms处理getInline请求
const debounceInlineCompletions = debounceWithPromise(
  getInlineCompletionItems,
  CODE_GEN_DEBOUNCE_DELAY
);

// 防抖300ms处理getLookAhead请求
const debounceLookAheadSuggestion = debounceWithPromise(
  getLookAheadSuggestion,
  CODE_GEN_DEBOUNCE_DELAY
);

export default async function provideInlineCompletionItems(
  document: vscode.TextDocument,
  position: vscode.Position,
  context: vscode.InlineCompletionContext,
  token: vscode.CancellationToken
): Promise<vscode.InlineCompletionList | undefined> {
  try {
    clearCurrentLookAheadSuggestion();

    // 自动or手动补全
    const isAuto = context.triggerKind === vscode.InlineCompletionTriggerKind.Automatic;

    if (
      isAuto &&
      (!isValidMidlinePosition(document, position) ||
        !getShouldComplete() ||
        !(await isCodeCompleteEnabled()))
    ) {
      return undefined;
    }

    // 手动补全时清除结果缓存
    if (!isAuto) {
      RunCompletionCache.clearCache();
    }

    const completionInfo = context.selectedCompletionInfo;
    let isValidCompletion = false;

    if (completionInfo) {
      const textAtRange = document.getText(completionInfo.range);

      if (completionInfo.text.startsWith(textAtRange)) {
        isValidCompletion = true;
      }
    }

    Logger.debug(
      `[provideInlineCompletionItems] request from [${position.line},${
        position.character
      }], isAuto: ${isAuto}, lookAhead:${!!completionInfo}, lookAheadValid:${isValidCompletion}`
    );

    let completions;
    // 记录开始时间
    const timeCounter = new TimeCounter();

    // 自动补全需要防抖处理，手动补全则不需要
    // 触发popup提示词时，将提示词增加到prefix
    if (isAuto && completionInfo && isValidCompletion) {
      completions = await debounceLookAheadSuggestion(
        document,
        completionInfo,
        position,
        token,
        isAuto
      );
    } else if (isAuto && (!isValidCompletion || !completionInfo)) {
      completions = await debounceInlineCompletions(document, position, token, isAuto);
    } else if (!isAuto && completionInfo && isValidCompletion) {
      completions = await getLookAheadSuggestion(document, completionInfo, position, token, isAuto);
    } else if (!isAuto && (!isValidCompletion || !completionInfo)) {
      completions = await getInlineCompletionItems(document, position, token, isAuto);
    }

    // 记录请求时间和结果
    const duration = timeCounter.getDuration();
    const insertText = completions?.items?.[0]?.insertText;
    const rStart = completions?.items?.[0]?.range?.start;
    const rEnd = completions?.items?.[0]?.range?.end;

    Logger.debug(
      `[provideInlineCompletionItems] request from [${position.line},${position.character}], duration: ${duration}ms, firstText:${insertText}, firstRange:[${rStart?.line},${rStart?.character}]-[${rEnd?.line},${rEnd?.character}]`
    );

    return completions;
  } catch (e) {
    Logger.error(`[provideInlineCompletionItems] catch request error: ${e}`);

    return undefined;
  }
}

function isValidMidlinePosition(document: vscode.TextDocument, position: vscode.Position): boolean {
  const lineSuffix = document.getText(
    new vscode.Range(position, document.lineAt(position.line).range.end)
  );

  return END_OF_LINE_VALID_REGEX.test(lineSuffix);
}

/**
 * 是否启用自动补全
 * @returns
 */
async function isCodeCompleteEnabled() {
  return await AutoCompleteStatusStore.checkIfEnabled();
}
