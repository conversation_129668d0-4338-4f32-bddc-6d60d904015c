import * as vscode from 'vscode';
import {FunctionCodeLensProvider } from './functionCodeLensProvider';
import { languageRegexes } from './functionParser';

export async function registerCodeLens(context: vscode.ExtensionContext) { 
  const codeLensProvider = new FunctionCodeLensProvider();

  // Register CodeLens provider for all supported languages
  context.subscriptions.push(
    vscode.languages.registerCodeLensProvider(
      Object.keys(languageRegexes).map(lang => ({ language: lang })),
      codeLensProvider
    )
  );

}
