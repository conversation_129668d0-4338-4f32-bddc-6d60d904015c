import * as vscode from 'vscode';
import SrdInlineCompletionItem from './inlineSuggestions/srdInlineCompletionItem';
import runCompletion from './runCompletion';
import getAutoImportCommand from './getAutoImportCommand';
import { SuggestionTrigger } from './types';
import { Logger } from '../utils/logger';

export default async function getInlineCompletionItems(
  document: vscode.TextDocument,
  position: vscode.Position,
  cancellationToken: vscode.CancellationToken,
  isAuto: boolean
): Promise<vscode.InlineCompletionList | undefined> {
  Logger.debug(`[getInlineCompletionItems] request from [${position.line},${position.character}]`);

  const response = await runCompletion({
    document,
    position,
    isAuto,
    retry: {
      cancellationToken,
    },
  });

  const completions = response?.results.map(
    result =>
      new SrdInlineCompletionItem(
        result.answer,
        result,
        // new vscode.Range(position, position.translate(0, result.answer.length)),
        new vscode.Range(position, position.translate(0, 0)),
        getAutoImportCommand(result, response, position, SuggestionTrigger.DocumentChanged, '', isAuto)
      )
  );

  return completions ? new vscode.InlineCompletionList(completions) : undefined;
}
