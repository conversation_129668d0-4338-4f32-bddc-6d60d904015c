import { Logger } from '../utils/logger';
import { fetchEventSource } from '@ai-zen/node-fetch-event-source';
import { SseClientParams } from '../codesecurity/type';
import { getHttpServerHost } from '../utils/envUtil';

export class SseClient {
  private controllers: Map<string, AbortController> = new Map(); 

  /**
   * 发起 SSE 请求
   * @param {SseClientParams} params - 请求参数
   * @param {(data: any) => Promise<void>} onMessage - 消息回调
   */
  public async fetchSse(params: SseClientParams, onMessage: (data: any) => Promise<void>){
    const { url, headers, body } = params;
    if (this.controllers.has(url)) {
      this.stop(url);
    }
    const ctrl = new AbortController();
    this.controllers.set(url, ctrl);
    const path = `${getHttpServerHost()}${url}`;
    await fetchEventSource(path, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      signal: ctrl.signal,
      body: JSON.stringify(body),
      async onopen(response) {
        Logger.debug(`sse connection ${url} opened, ${response}`)
      },
      onmessage(event) {
        try {
          if (event.data) {
            const data = JSON.parse(event.data);
            onMessage(data);  // 每次接收到消息，立即传递给上层
          } else {
            Logger.debug(`Received a non-data event:, ${event}`)
          }
        }catch(error) {
          Logger.error(`Error processing event:, ${error}`);
        }
      },
      onclose:()=>{
        Logger.debug(`sse connection ${url} closed`);
        this.controllers.delete(url)
      },
      onerror: (err) => {
        Logger.error(`SSE error:, ${err}`);
        this.stop(url)
      },
    });
  }
  
  /**
   * 停止指定URL的 SSE 请求
   * @param {string} url - 要停止的 URL
   */
  public stop(url: string): void {
    const ctrl = this.controllers.get(url);
    if (ctrl) {
      ctrl.abort(); // 终止连接
      this.controllers.delete(url); // 从映射中移除
      Logger.debug(`SSE connection for URL ${url} stopped.`);
    } else {
      Logger.debug(`No active SSE connection for URL ${url}.`);
    }
  }
}