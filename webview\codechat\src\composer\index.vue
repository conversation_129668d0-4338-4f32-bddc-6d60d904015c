<template>
  <div class="composer-root">
    <div class="composer-content">
      <template v-if="chatStore.codeFreeLoginStatus">
        <MessageListView v-if="composer.messageList.length" />
        <GuidePage v-else />
      </template>
      <GuidePageWithoutLogin v-else/>
    </div>
    <InputBox />
  </div>
</template>

<script setup>
import GuidePage from './GuidePage.vue';
import GuidePageWithoutLogin from './GuidePageWithoutLogin.vue'
import InputBox from './InputBox.vue';
import MessageListView from './MessageListView.vue'
import { onMounted, watch } from 'vue'
import { useComposer } from '@/store/composer.js'
import { useChatStore } from '@/store/chat.ts'

import * as messageStorage from './message-storage.js'
const composer = useComposer();
const chatStore = useChatStore()


watch(()=>chatStore.tab,async (value)=>{
  if(chatStore.tab === 'composer') {
    messageStorage.setProjectPath(chatStore.config.projectPath)
  }
})



</script>
<style lang="scss">
.composer-root {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .composer-content {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

}
</style>