type resolveFn<T> = (value: T) => void;
type rejectFn = (reason?: unknown) => void;

export default class Defered<T> {
  private defered: Promise<T>;

  private resolve: resolveFn<T> | null = null;

  private reject: rejectFn | null = null;

  private settled = false;

  private notInited = new Error('Defered not constructed');

  public constructor() {
    this.defered = new Promise<T>((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
  }

  public get isSettled() {
    return this.settled;
  }

  public get wait() {
    return this.defered;
  }

  public setFinished(t: T) {
    if (!this.resolve) {
      throw this.notInited;
    }
    this.resolve(t);
    this.settled = true;
  }

  public setFailed(reason?: unknown) {
    if (!this.reject) {
      throw this.notInited;
    }
    this.reject(reason);
    this.settled = true;
  }
}
