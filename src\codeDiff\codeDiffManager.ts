import * as vscode from 'vscode';
import { Disposable, commands } from 'vscode';
import { SrdCommand } from '../common/constants';
import { DiffViewParams } from '../codechat/types';
import CodeUpdater from './codeUpdater';
import { Logger } from '../utils/logger';
import { DiffService } from '../diff/DiffService';

interface DiffBlock {
  start: number;
  end: number;
  content: string;
}

export default class CodeDiffManager {
  private disposable: Disposable;
  private originalUri?: vscode.Uri;
  private modifiedUri?: vscode.Uri;
  private modifiedDocument?: vscode.TextDocument;
  private static instance: CodeDiffManager | null;

  public constructor() {
    this.disposable = Disposable.from(...this.registerCommands(), this.registerWillSaveTextDocument());
  }

  public dispose() {
    this.disposable.dispose();
  }

  private registerCommands(): Disposable[] {
    const disposable: Disposable[] = [];
    disposable.push(
      // diff 操作命令 - 使用 VS Code 内置命令
      // commands.registerCommand(SrdCommand.CODE_DIFF_ACCEPT_ALL_CHANGES, () => this.applyAllChanges()),
      // commands.registerCommand(SrdCommand.CODE_DIFF_REVERT_ALL_CHANGES, () => this.revertAllChanges())
    );

    return disposable;
  }

  public static getInstance(): CodeDiffManager {
    if (CodeDiffManager.instance == null) {
      CodeDiffManager.instance = new CodeDiffManager();
    }
    return CodeDiffManager.instance;
  }

  private registerWillSaveTextDocument() {
    return vscode.workspace.onWillSaveTextDocument(e => {
      Logger.debug(`即将保存文档:${e.document.uri.toString()}`);
    })
  }

  // public async showCodeDiffview1(params: DiffViewParams) {
  //   try {
  //     // 打开文件并获取 TextDocument 对象
  //     const sourcePath = params.filePath;
  //     const document = await vscode.workspace.openTextDocument(vscode.Uri.file(sourcePath));
  //     // 获取文件内容
  //     const sourceCode = document.getText();
  //     const fragment = params.originalContent;
  //     const generatedCode = params.generatedContent;

  //     this.originalUri = vscode.Uri.file(sourcePath);

  //     this.modifiedUri = vscode.Uri.parse('untitled:Modified_' + document.fileName.split('/').pop());

  //     // 创建修改后的临时文档
  //     // this.modifiedDocument = await vscode.workspace.openTextDocument({
  //     //   language: document.languageId,
  //     // });
  //     this.modifiedDocument = await vscode.workspace.openTextDocument(this.modifiedUri);

  //     // 写入生成的内容
  //     const edit = new vscode.WorkspaceEdit();
  //     const fullRange = new vscode.Range(
  //       this.modifiedDocument.positionAt(0),
  //       this.modifiedDocument.positionAt(this.modifiedDocument.getText().length)
  //     );


  //     const updater = new CodeUpdater(sourceCode);
  //     const { updatedCode } = updater.updateFragment(fragment, generatedCode);
  //     // updatedCode = this.addBoundaryIfNeeded(sourceCode, updatedCode);

  //     edit.replace(this.modifiedUri, fullRange, updatedCode);
  //     // edit.insert(this.modifiedUri, new vscode.Position(0, 0), updatedCode);
  //     await vscode.workspace.applyEdit(edit);

  //     // 打开 diff 视图，使用带换行符的原始内容
  //     await vscode.commands.executeCommand(
  //       'vscode.diff',
  //       this.originalUri,
  //       // { uri: this.originalUri, external: originalContent }, // 使用带换行符的内容
  //       this.modifiedUri,
  //       'CodeFree查看变更',
  //       {
  //         preview: true,
  //         revealFirst: true,
  //       }
  //     );

  //   } catch (error) {
  //     Logger.error(`[codeDiffManager] openTextDocument error: ${error}`);
  //     return null;
  //   }
  // }

  public async showCodeDiffview(params: DiffViewParams) {
    try {
      // 打开文件并获取 TextDocument 对象
      const sourcePath = params.filePath;
      const document = await vscode.workspace.openTextDocument(vscode.Uri.file(sourcePath));
      // 获取文件内容
      const sourceCode = document.getText();
      const fragment = params.originalContent;
      const generatedCode = params.generatedContent;



      const updater = new CodeUpdater(sourceCode);
      const { updatedCode } = updater.updateFragment(fragment, generatedCode);
      // updatedCode = this.addBoundaryIfNeeded(sourceCode, updatedCode);
      DiffService.getInstance().openDiffEditor(sourcePath, updatedCode)
      

    } catch (error) {
      Logger.error(`[codeDiffManager] openTextDocument error: ${error}`);
      return null;
    }
  }

  private addBoundaryIfNeeded(originalContent: string, modifiedContent: string): string {
    // 如果修改内容长度大于等于原内容长度，说明可能在文件末尾有改动
    if (modifiedContent.length >= originalContent.length) {
      return modifiedContent + '\n';
    }
    return modifiedContent;
  }

  // 接受所有更改
  private async applyAllChanges() {
    if (!this.originalUri || !this.modifiedUri || !this.modifiedDocument) {
      return;
    }

    try {
      // 1. 应用更改到原始文件
      const edit = new vscode.WorkspaceEdit();
      const originalDoc = await vscode.workspace.openTextDocument(this.originalUri);
      const fullRange = new vscode.Range(
        new vscode.Position(0, 0),
        originalDoc.lineAt(originalDoc.lineCount - 1).range.end
      );

      edit.replace(this.originalUri, fullRange, this.modifiedDocument.getText());
      await vscode.workspace.applyEdit(edit);

      await vscode.commands.executeCommand('workbench.action.revertAndCloseActiveEditor');

      // 3. 清理引用
      this.originalUri = undefined;
      this.modifiedUri = undefined;
      this.modifiedDocument = undefined;
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to apply changes: ${error}`);
    }
  }

  // 还原所有更改
  private async revertAllChanges() {
    await vscode.commands.executeCommand('workbench.action.revertAndCloseActiveEditor');
  }
}
