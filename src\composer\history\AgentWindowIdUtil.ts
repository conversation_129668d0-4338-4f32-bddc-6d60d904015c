import * as fs from 'fs';
import * as path from 'path';
import { ProjectItem } from '../types';
import { AgentHistoryUtil } from './AgentHistoryUtil';
import * as os from 'os';
import { Logger } from '../../utils/logger';
import { setPathPermissions } from '../../utils/fileSystemUtils';

interface WindowData {
  windowIds: string[];
}

export class AgentWindowIdUtil {
  private static historyDir = path.join(os.homedir(), '.codefree', 'plugin', 'history');
  private static windowIdFile = path.join(this.historyDir, 'window_ids_v1.json');
  private static windowDataCache: Map<string, WindowData> | null = null;

  static {
    // --- Directory Handling ---
    if (!fs.existsSync(this.historyDir)) {
      try {
        fs.mkdirSync(this.historyDir, { recursive: true });
      } catch (mkdirError: any) {
        if (mkdirError.code === 'EACCES' || mkdirError.code === 'EPERM') {
          Logger.error(`Permission denied creating directory ${this.historyDir} for window IDs: ${mkdirError.message}`);
        } else {
          Logger.error(`Failed to create directory ${this.historyDir} for window IDs: ${mkdirError.message}`);
        }
        // If directory creation fails, subsequent operations will likely also fail.
        // Consider if further initialization should be skipped. For now, it continues to setPathPermissions.
      }
      setPathPermissions(this.historyDir, 'directory');
    } else {
      setPathPermissions(this.historyDir, 'directory');
    }

    // --- File Handling ---
    if (!fs.existsSync(this.windowIdFile)) {
      try {
        fs.writeFileSync(this.windowIdFile, '{}');
      } catch (writeFileError: any) {
        if (writeFileError.code === 'EACCES' || writeFileError.code === 'EPERM') {
          Logger.error(`Permission denied writing initial window ID file ${this.windowIdFile}: ${writeFileError.message}`);
        } else {
          Logger.error(`Failed to write initial window ID file ${this.windowIdFile}: ${writeFileError.message}`);
        }
      }
      setPathPermissions(this.windowIdFile, 'file');
    } else {
      setPathPermissions(this.windowIdFile, 'file');
    }
  }

  static getWindowId(project: ProjectItem): string {
    const projectPath = project.projectPath;
    if (!projectPath) {
      throw new Error('projectPath 为空');
    }

    // 从缓存获取数据
    if (!this.windowDataCache) {
      this.windowDataCache = this.loadWindowIds();
    }

    const windowData = this.windowDataCache.get(projectPath);
    if (!windowData || windowData.windowIds.length === 0) {
      throw new Error('没有找到对应的windowId');
    }
    return windowData.windowIds[windowData.windowIds.length - 1];
  }

  static createNewWindowId(project: ProjectItem): string {
    const projectPath = project.projectPath;
    if (!projectPath) {
      return Date.now().toString();
    }

    if (!this.windowDataCache) {
      this.windowDataCache = this.loadWindowIds();
    }

    const windowData = this.windowDataCache.get(projectPath);
    if (windowData?.windowIds.length) {
      const latestWindowId = windowData.windowIds[windowData.windowIds.length - 1];
      if (!AgentHistoryUtil.getInstance().getHistoryByConversationId(project, latestWindowId)) {
        return latestWindowId;
      }
    }

    const newWindowId = Date.now().toString();
    const windowIds = windowData?.windowIds.slice() || [];
    windowIds.push(newWindowId);

    this.windowDataCache.set(projectPath, { windowIds });
    this.saveWindowIds(this.windowDataCache);
    return newWindowId;
  }

  private static loadWindowIds(): Map<string, WindowData> {
    try {
      const content = fs.readFileSync(this.windowIdFile, 'utf-8');
      if (content) {
        const data = JSON.parse(content);
        return new Map(Object.entries(data));
      }
    } catch (error) {
      Logger.error(`加载窗口ID失败:${error}`);
    }
    return new Map();
  }

  private static saveWindowIds(windowIds: Map<string, WindowData>): void {
    try {
      const data = Object.fromEntries(windowIds);
      fs.writeFileSync(this.windowIdFile, JSON.stringify(data));
    } catch (error: any) {
      if (error.code === 'EACCES' || error.code === 'EPERM') {
        Logger.error(`Permission denied saving window IDs to ${this.windowIdFile}: ${error.message}`);
      } else {
        Logger.error(`保存窗口ID失败:${error.message}`);
      }
    }
  }

  static clearWindowId(project: ProjectItem): void {
    const projectPath = project.projectPath;
    if (projectPath) {
      if (!this.windowDataCache) {
        this.windowDataCache = this.loadWindowIds();
      }
      this.windowDataCache.delete(projectPath);
      this.saveWindowIds(this.windowDataCache);
    }
  }

  static clearAllWindowIds(): void {
    try {
      fs.writeFileSync(this.windowIdFile, '{}');
      this.windowDataCache = new Map();
    } catch (error: any) {
      if (error.code === 'EACCES' || error.code === 'EPERM') {
        Logger.error(`Permission denied clearing all window IDs at ${this.windowIdFile}: ${error.message}`);
      } else {
        Logger.error(`清除所有窗口ID失败:${error.message}`);
      }
    }
  }
}