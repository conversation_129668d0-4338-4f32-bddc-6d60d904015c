<template>
  <div class="chat-item chat-item-pending">
    <template v-for="item in list">
      <UserBubble v-if="item.role === 'user'" :msg="item.msg" :files="item.files" />
      <PendingBubble v-if="item.role === 'assistant'" :msg="item.msg"/>
    </template>
  </div>
</template>
<script setup>
import UserBubble from '@/components/UserBubble.vue';
import PendingBubble from './PendingBubble.vue';
import { useChatStore } from '@/store/chat';
import { computed, reactive, watch } from 'vue';


const chatStore = useChatStore()

const list = computed(() => {
  return chatStore.pendingList
})



</script>
<style lang="scss" scoped></style>