import HttpClient from '../commclient/httpClient';
import { RtnCode, ClientCommunicateType, CLIENT_TYPE, LoginStatus, ChatTips } from '../common/constants';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { PromptsResult, OperateTemplateResp } from './types/prompts';
import { TEMPLATES_PATH, GET_CATEGORIES_PATH } from '../common/config';
import { HttpError } from '../commclient/types/http';
import { Logger } from '../utils/logger';
import { getCurrentUser, getExtensionVersion, getGitRepoUrl, getProjectName } from '../common/globalContext';
import { PromptsRequest } from '../codechat/types';
import * as vscode from 'vscode';
import { LoginServiceInst } from '../service/loginService';

/**
 * Prompts服务
 */
export default class PromptsService implements IAnswerHandler {
  private type: ClientCommunicateType = ClientCommunicateType.HTTP;

  private httpClient: HttpClient | undefined;

  /**
   * 通过哪种方式上传
   * @param type
   */
  public constructor(type?: ClientCommunicateType) {
    if (type) {
      this.type = type;
    }

    if (this.type === ClientCommunicateType.HTTP) {
      this.httpClient = new HttpClient(undefined, 'prompts');
    }
  }


  public async getTemplates(queries: PromptsRequest): Promise<PromptsResult> { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[PromptsService] getTemplates, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }
    const params: Record<string, string> = {
      name: queries.name || '',
      lastUsed: (queries.lastUsed || false).toString(),
      categoryId: queries.categoryId || '',
      pageNum: (queries.pageNum || 1).toString(),
      pageDataCount: (queries.pageDataCount || 10).toString(),
      type: queries.type
    };

    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByGet<unknown>(TEMPLATES_PATH, params, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[PromptsService] getTemplates failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[PromptsService] getTemplates result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result
    };
  }

  public async operateTemplate(req: PromptsRequest) { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[PromptsService] operateTemplate, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const body = {
      client: {
        type: CLIENT_TYPE,
        version: vscode.version,
        pluginVersion: getExtensionVersion(),
        gitUrl: getGitRepoUrl(),
        projectName: getProjectName()
      },
    }
    const uInfo = await getCurrentUser();
    const templateId = req.templateId || '';
    const operationType = req.operationType || '';
    const path = `/${templateId}/operation/${operationType}`;
    const result = await this.httpClient.requestByPost<OperateTemplateResp>(TEMPLATES_PATH + path, body, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        'Content-Type': 'application/json',
      }
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[PromptsService] operateTemplate failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[PromptsService] operateTemplate result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: {
        ...result,
        templateId,
        operationType
      }
    };
  }

  public async getCategories() { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[PromptsService] getCategories, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }

    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByGet<unknown>(GET_CATEGORIES_PATH, {}, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[PromptsService] getCategories failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[PromptsService] getCategories result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result
    };
  }

  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    throw new Error('Method not implemented.');
  }

  public getReqId(): string {
    throw new Error('Method not implemented.');
  }

}
export const PromptsServiceInst = new PromptsService();