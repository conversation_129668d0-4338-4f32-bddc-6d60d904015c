import axios from 'axios';
import * as fs from 'fs';
import { AgentVersion, LocalAgentConfig } from '../types';
import { SystemUtils } from '../utils/system';
import { getAgentVersion } from '../../common/globalContext';
import { Logger } from '../../utils/logger';
import * as path from 'path';
export class VersionChecker {

  public constructor() { }

  /**
   * 从远端下发的配置获取最新Agent版本信息
   * @returns 版本信息列表
   */
  public getConfiguredAgentVersions(): AgentVersion[] {
    try {
      const agentConfigs = getAgentVersion();
      if (!agentConfigs || agentConfigs.length === 0) {
        return [];
      }

      // 将配置转换为AgentVersion结构
      return agentConfigs.map(config => ({
        agentName: config.type,
        version: config.version,
        // 保存下载URL和MD5用于后续下载
        downloadUrl: config.downloadUrl,
        md5: config.md5
      }));
    } catch (error) {
      Logger.error(`[VersionChecker] 获取Agent配置版本失败: ${error}`);
      return [];
    }
  }


  /**
   * 读取本地版本配置，如果不存在则创建
   * @param configPath 配置文件路径
   * @returns 本地配置对象
   */
  public async readLocalVersions(configPath: string): Promise<LocalAgentConfig> {
    try {
      // 检查配置文件是否存在
      if (!fs.existsSync(configPath)) {

        // 创建初始配置对象
        const initialConfig: LocalAgentConfig = { agent: {} };

        // 确保目录存在
        const configDir = path.dirname(configPath);
        if (!fs.existsSync(configDir)) {
          fs.mkdirSync(configDir, { recursive: true, mode: 0o755 });
        }

        // 写入初始配置文件
        await fs.promises.writeFile(configPath, JSON.stringify(initialConfig, null, 2), 'utf-8');

        return initialConfig;
      }

      // 读取现有配置文件
      const content = await fs.promises.readFile(configPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      Logger.error(`[VersionChecker] 读取或创建本地版本配置失败: ${error}`);
      // 即使出错，也返回一个有效的空配置对象
      return { agent: {} };
    }
  }

  /** 
   * 更新本地配置文件
   */
  public async updateLocalVersions(configPath: string, updates: AgentVersion[]): Promise<void> {
    try {
      // 获取当前配置
      const config = await this.readLocalVersions(configPath);
      const clientPlatform = SystemUtils.getLocalConfig();

      // 更新配置中的版本信息
      for (const update of updates) {
        if (!config.agent[update.agentName]) {
          config.agent[update.agentName] = {};
        }
        
        // 确保平台配置存在
        if (!config.agent[update.agentName][clientPlatform]) {
          // 初始化为空数组
          config.agent[update.agentName][clientPlatform] = [];
        }
        
        // 获取版本数组
        const versionArray = config.agent[update.agentName][clientPlatform];
        
        // 构建新版本对象
        const newVersionEntry = {
          path: SystemUtils.getAgentFolder('codefree', update.agentName).replace(/[/\\]$/, ''),
          version: update.version
        };
        
        // 直接添加新版本到数组，不检查是否已存在
        versionArray.push(newVersionEntry);
      }

      // 写入更新后的配置
      const configDir = require('path').dirname(configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true, mode: 0o755 });
      }

      await fs.promises.writeFile(configPath, JSON.stringify(config, null, 2), 'utf-8');
    } catch (error) {
      Logger.error(`[VersionChecker] 更新本地版本配置失败: ${error}`);
      throw error;
    }
  }
}
