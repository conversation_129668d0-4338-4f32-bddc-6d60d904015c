import { AuthenticationSession, AuthenticationSessionAccountInformation } from 'vscode';
import { APP_KEY } from '../common/constants';

export interface UserInfo {
  userId: string;
  sessionId: string;
  userAccount: string;
}

export default class SrdSession implements AuthenticationSession {
  public readonly id: string = APP_KEY;

  public readonly accessToken: string = '';

  public readonly account: AuthenticationSessionAccountInformation = {
    id: this.id,
    label: 'NotLogin',
  };

  public readonly scopes: readonly string[] = [];

  public constructor(token?: string | UserInfo) {
    if (typeof token === 'string') {
      this.accessToken = token;
    } else if (token) {
      this.accessToken = this.buildAccessToken(token.userId, token.sessionId, token.userAccount);
    }

    if (this.userInfo) {
      this.account = {
        id: this.id,
        label: this.userInfo.userAccount,
      };
    }
  }

  public get userInfo(): UserInfo | null {
    return this.parseAccessToken(this.accessToken);
  }

  private buildAccessToken(userId: string, sessionId: string, userAccount: string): string {
    return `${userId}_${sessionId}_${userAccount}`;
  }

  private parseAccessToken(accessToken?: string): UserInfo | null {
    if (!accessToken) {
      return null;
    }

    const [userId, sessionId, userAccount] = accessToken.split('_');

    return {
      userId,
      sessionId,
      userAccount,
    };
  }
}
