import { v4 as uuidv4 } from 'uuid';
const MessageListkey = '__messageList';
const DialogIdKey = '__dialogId'
let latestDialogId = ''
let latestProjectPath = ''

export function setProjectPath(projectPaht) {
  latestProjectPath = projectPaht
  let id = localStorage.getItem(DialogIdKey + latestProjectPath)
  if (!id) {
    id = uuidv4()
    localStorage.setItem(DialogIdKey + latestProjectPath, id)
  }
  latestDialogId = id
}

export function getDialogId() {
  return latestDialogId
}

// 当点击新建或者
export function setProjectDialogId() {
  let id = uuidv4()
  localStorage.setItem(DialogIdKey + latestProjectPath, id)
  latestDialogId = id
  return id
}

