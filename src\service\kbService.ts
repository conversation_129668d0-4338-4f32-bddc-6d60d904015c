import HttpClient from '../commclient/httpClient';
import { RtnCode, ClientCommunicateType, LoginStatus, ChatTips } from '../common/constants';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { GET_KB_INFO_PATH, SEARCH_DEV_KBS_PATH } from '../common/config';
import { HttpError } from '../commclient/types/http';
import { Logger } from '../utils/logger';
import { getCurrentUser, getExtensionVersion, getGitRepoUrl, getProjectName } from '../common/globalContext';
import { KnowledgeBaseRequest } from '../codechat/types';
import { LoginServiceInst } from '../service/loginService';
import { HttpResult } from './types/http';
import { getAddress, getSecretKey } from '../settings/settingsGetter';

/**
 * Knowledge base服务
 */
export default class KbService implements IAnswerHandler {
  private type: ClientCommunicateType = ClientCommunicateType.HTTP;

  private httpClient: HttpClient | undefined;

  /**
   * 通过哪种方式上传
   * @param type
   */
  public constructor(type?: ClientCommunicateType) {
    if (type) {
      this.type = type;
    }

    if (this.type === ClientCommunicateType.HTTP) {
      this.httpClient = new HttpClient(undefined, 'kb');
    }
  }


  public async getKbInfo(queries: KnowledgeBaseRequest): Promise<HttpResult> { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[KbService] getKbInfo, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }
    const params: Record<string, string> = {
      kbId: (queries.kbId)?.toString() || '',
    };
    
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByGet<unknown>(GET_KB_INFO_PATH, params, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[KbService] getKbInfo failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[KbService] getKbInfo result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result
    };
  }

  public async searchDevKbs(queries: KnowledgeBaseRequest): Promise<HttpResult> { 
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[KbService] searchDevKbs, not login');
      return { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN,}
    }
    if (!this.httpClient) { 
      return { code: RtnCode.HTTP_CLIENT_ERROR };
    }
    const params: Record<string, string> = {
      currentPage: (queries.currentPage || 1)?.toString(),
      keyword: queries.keyword || '',
      pageSize: queries.pageSize ? queries.pageSize.toString() : '',
      kbServer: getAddress(),
      userSecret: getSecretKey() || '',
    };

    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByGet<unknown>(SEARCH_DEV_KBS_PATH, params, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[KbService] searchDevKbs failed, ${JSON.stringify(result)}`);

      return {
        code: (result as HttpError).code || (result as HttpError).status || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[KbService] searchDevKbs result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result
    };
  }


  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    throw new Error('Method not implemented.');
  }

  public getReqId(): string {
    throw new Error('Method not implemented.');
  }

}
export const KbServiceInst = new KbService();