<template>
  <div class="logo-container">
    <div class="top">
      <svg-icon :name="brandStore.isSec ? 'ai-logo-sec' : 'srd_ai'" class="logo-icon" size="50px" />
      <div class="content">{{ brandStore.brandName }}</div>
      <div class="bottom">
        <template v-if="isMac">
          <svg-icon name="mac_option" class='bottom-icon'></svg-icon>
          <svg-icon name="mac_up" class='bottom-icon'></svg-icon>
          <svg-icon name="mac_k" class='bottom-icon'></svg-icon>
        </template>
        <template v-else>
          <div class="keyboard-item">
            Alt
          </div>
          <div class="keyboard-item">
            Shift
          </div>
          <div class="keyboard-item">
            K
          </div>
        </template>
        <div class="text">快速开始问答</div>
      </div>
      <div v-if="chatStore.tab === 'composer'" class="bottom" style="flex-direction: column;gap: 10px;">
        <div>
          通过自然语言对当前代码库进行全面修改
        </div>
        <div>
          高效完成多文件创建和编辑
        </div>
      </div>
    </div>
    <div class="bubble">
      <IntroPanel @shortcut="shortcut" @submitMsg="submitMsg"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import IntroPanel from './IntroPanel.vue';
import { useBrandStore } from '@/store/brand';

import { useChatStore } from '@/store/chat'
const chatStore = useChatStore()
const emit = defineEmits(['shortcut', 'submitMsg']);
const brandStore = useBrandStore();

const isMac = navigator.userAgent.toLowerCase().includes('mac')

function shortcut(message: number) {
  emit('shortcut', message)
}
function submitMsg(message: string) {
  emit('submitMsg', message)
}
</script>
<style>
.logo-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.top {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin-top: 150px;
}

.bottom {
  display: flex;
  margin-top: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}

.text {
  font-size: 14px;
  color: var(--vscode-input-foreground);
  white-space: nowrap;
  margin-left: 5px;
}

.content {
  padding-top: 15px;
  font-weight: bold;
  font-size: 30px;
  color: var(--vscode-input-foreground);

}

.bottom-icon {
  margin: 0 3px;
  width: 30px;
  height: 30px;
  color: var(--vscode-inputOption-activeForeground);
}

.bubble {
  width: 100%
}

.keyboard-item {
  color: var(--vscode-inputOption-activeForeground);
  height: 30px;
  border-radius: 3px;
  padding: 0 5px;
  display: flex;
  align-items: center;
  min-width: 30px;
  justify-content: center;
  box-sizing: border-box;
  margin: 0 3px;
  background: var(--vscode-input-background); 
  /* background: ; */
}
</style>