<template>
  <div class="input-box__icon-list-box">
    <span class="title">工具栏:</span>
    <svg-icon size="14px" color="#d36c09" name="icon_git" @click="gitJump" class="toolbar-icon-style"></svg-icon>
  </div>
</template>
<script setup lang='ts'>
import { useComposer } from '@/store/composer.js'

const composer = useComposer();

// 跳转到git工作项
const gitJump = () => {
  composer.gitJump();
};
</script>
<style lang='scss'>
.input-box__icon-list-box {
  margin: 12px 20px 4px;
  padding: 6px 12px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  column-gap: 16px;
  row-gap: 0px;
  padding: 6px 18px 6px 12px;
  background: linear-gradient(90deg, rgba(48, 124, 251, 0.1) 0%, rgba(149, 99, 250, 0.1) 100%);
  border-width: 1px;
  border-style: solid;
  border-image: linear-gradient(90deg, rgba(48, 124, 251, 0.4) 0%, rgba(149, 99, 250, 0.4) 100%) 1;

  .title {
    font-weight: 400;
    font-size: 14px;
    color: #87898b;
  }

  .toolbar-icon-style {
    cursor: pointer;
  }
}
</style>
