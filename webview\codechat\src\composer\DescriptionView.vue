<template>
  <div class="desc-view-root">
    <div class="desc-view-title">
      使用向导
    </div>
    <div class="desc-view-content">
      <div>
        1、在输入框“添加参考”处选择参考文件
      </div>
      <div>
        2、在输入框描述您想要实现的功能或需要解决的问题
      </div>
      <div>
        3、回车发送消息后，Al将对您项目的代码进行相应修改
      </div>
      <div>
        4、生成结果后，点击输入框上方弹窗中按钮查看与接受修改更多使用说明，请查看
        <a style="cursor: pointer;color: #468CFF;" @click="helpDoc">
          在线文档
        </a>
      </div>
    </div>
  </div>
</template>
<script lang="js" setup>
import * as sender from '@/MessageSender'
import { useChatStore } from '@/store/chat'
import { WebViewReqCommand } from '@/constants/common';
import brandConfig from '../../brand.json';

const isSrd = brandConfig.isSec !== true;

const chatStore = useChatStore()
function helpDoc() {
  const url = chatStore.host + '/helpcenter/content?id=1364186113581133824'
  if (sender.isJet) {
    sender.postMessage({
      command: WebViewReqCommand.OPEN_EXTERNAL,
      data: {
        path: url,
        pureUrl: true
      }
    })
  } else {
    if (isSrd) {
      const a = document.createElement('a')
      a.href = url
      a.target = '_blank'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    } else { 
      sender.postMessage({
        command: WebViewReqCommand.OPEN_EXTERNAL,
        data: {
          path: '/helpcenter/content?id=1189244999559761920'
        }
      })
    }

  }
  console.log('帮助url', url)
}
</script>

<style lang="scss">
.desc-view-root {
  padding: 12px 12px 16px 12px;
  border-radius: 4px;
  background-color: var(--vscode-list-inactiveSelectionBackground);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-none-color);

  .desc-view-title {
    font-weight: 600;
  }

  .desc-view-content {
    margin-top: 8px;
    opacity: 0.8;
    line-height: 1.5;
  }
}
</style>