import * as vscode from 'vscode';
import { getNonce, generateUUID } from '../utils/common';
import {
  ChatMessageType,
  IsAnswerEnd,
  RtnMessage,
  SrdCommand,
  SrdViewType,
  WebViewReqCommand,
  WebViewRspCode,
  WebViewRspCommand,
} from '../common/constants';
import CodeChatEngin from './codeChatEngin';
import {
  ChatRequest,
  ConversationEventType,
  ICodeChatEnginHandler,
  IConversationHandler,
  ICodeSelectionHandler,
  WebViewRequest,
  WebViewResponse,
  CodeSelectionEventType,
  CodeSelectionParams,
  SrdChatRequest,
  SrdChatEventType,
  dataReportParams,
  PromptsRequest,
  PromptsEventType,
  ChatHistoryRequest,
  AnswerStopParams,
  FeedbackAnswerRequest,
  ConversationChangeParams,
  KnowledgeBaseRequest,
  KnowledgeBaseEventType,
  CodeSecurityEventType,
  ICodeScanHandler,
  FileChatEventType,
  DiffViewParams,
  DiffViewVerticalParams,
  IdeUtilEventType,
  ComposerEventType,
} from './types';
import ConversionManager from './conversationManager';
import { Conversation, ChatMessageSimple } from './conversation';
import { Logger } from '../utils/logger';
import { IStatusBarHandler } from '../statusbar/types';
import EditorUtil from './editorUtil';

import SrdChatService from '../service/srdChatService';
import { LoginServiceInst } from '../service/loginService';
import { DataReportServiceInst } from '../service/dataReportService';
import { RtnCode, LoginStatus, ChatTips, ChannelStatus } from '../common/constants';
import { PromptsServiceInst } from '../service/promptsService';
import { ChatHistoryServiceInst } from '../service/chatHistoryService';
import { IServiceObserver, IServiceObservable, EventType } from '../service/types/login';
import { CodeAIResponsePayload, chatUIConfig } from '../service/types/codeAI';
import { KbServiceInst } from '../service/kbService';
import CodeAICommHandler from '../commclient/codeAICommHandler';
import CodeScanManager from '../codesecurity/codeScanManager';
import { DiffViewHandler } from '../diff/diffViewHandler';
import CodeDiffManager from '../codeDiff/codeDiffManager';
import { getFileTextByPath } from '../utils/textEditor';
import { RelatedFile } from './types';
import { MAX_TOTAL_CHARS } from '../common/config';
import { WorkItemEventType } from './types';

// import { SrdChatResult } from '../service/types/srdChat';
import { IdeUtilManager } from './ideUtilManager';
import { IComposerServiceHandler } from './types';
import { ComposerService } from '../composer/ComposerService';
import { DiffFile } from '../diff/types';
import { ComposerRequestParams } from '../composer/types';
import { initAgent } from '../agent';
// import { ContextInputService } from '../service/contextInputService';
import { ContextInputService } from '../composer/ContextInputService';
import { v4 as uuidv4 } from 'uuid';
import { getCodeAIConfig, getExtensionVersion } from '../common/globalContext';
import { ComposerHistoryHandler } from '../composer/history/agentHistoryHandler';
import { ChatHistory } from '../composer/history/AgentHistoryUtil';
import { getProjectInfo } from '../utils/textEditor';
import { TerminalManager } from '../terminal/terminalManager';
import { TerminalParams } from './types';
import { getHttpServerHost } from '../utils/envUtil';
import { InlineDiffViewManager } from '../diff/diffView/InlineDiff';
import { WorkItemManager } from '../workItem/workItemManager';
import { WorkItemResponse, WorkItemParams } from './types';
import { getHelpUrl } from '../login/loginUtils';

export default class ChatViewProvider
  implements
    vscode.WebviewViewProvider,
    ICodeChatEnginHandler,
    IConversationHandler,
    ICodeSelectionHandler,
    ICodeScanHandler,
    IComposerServiceHandler,
    IServiceObserver,
  vscode.Disposable {
  public static readonly viewType = SrdViewType.CHAT;

  private view?: vscode.WebviewView;

  private extensionUri: vscode.Uri;

  private codeChatEngin: CodeChatEngin;

  private conversationManager: ConversionManager;

  private curConversation: Conversation | null = null;

  private curReqId: string | undefined;

  private cachedWebViewRspList: WebViewResponse[] = [];

  private srdChatService: SrdChatService;

  private serviceObservable: IServiceObservable | undefined;

  private codeScanManager: CodeScanManager;

  private diffViewHandler: DiffViewHandler;

  private ideUtilManager: IdeUtilManager;

  private composerService: ComposerService;

  private composerHistoryHandler: ComposerHistoryHandler;

  private terminalMananger: TerminalManager;

  private workItemManager: WorkItemManager;

  public constructor(context: vscode.ExtensionContext, statusHandler: IStatusBarHandler) {
    this.extensionUri = context.extensionUri;
    this.conversationManager = new ConversionManager(this, context.globalStorageUri);
    this.codeChatEngin = new CodeChatEngin(this, this.conversationManager, statusHandler);
    this.srdChatService = new SrdChatService();
    this.codeScanManager = new CodeScanManager(this);
    this.diffViewHandler = new DiffViewHandler(this);
    this.ideUtilManager = new IdeUtilManager(context, this);
    this.composerService = new ComposerService(this);
    this.composerHistoryHandler = new ComposerHistoryHandler(this);
    this.terminalMananger = new TerminalManager();
    this.workItemManager = new WorkItemManager(this);
  }

  /**
   * 初始化WebView
   * @param webviewView
   * @param context
   * @param _token
   */
  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    this.view = webviewView;

    webviewView.webview.options = {
      // Allow scripts in the webview
      enableScripts: true,
      localResourceRoots: [this.extensionUri],
    };

    webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);

    webviewView.webview.onDidReceiveMessage((data: WebViewRequest) => {
      this.didReceiveMessage(data);
    });
  }

  /**
   * 设置被观察者，即LoginService
   * @param observable
   */
  public setObservable(observable: IServiceObservable): void {
    this.serviceObservable = observable;
    this.serviceObservable.registerObserver(this);
  }

  public async onServiceChanged(eventType: EventType, data: unknown) {
    let response;
    switch (eventType) {
      case EventType.LOGIN_SUCCESS:
        const { config, versionDesc } = data as chatUIConfig;
        const indexVersion = config.indexVersion;
        initAgent(this.composerService, indexVersion);
        const host = getHttpServerHost();
        const releaseVersion = process.env.RELEASE_VERSION;
        const clientVersion = getExtensionVersion();
        const projectPath = getProjectInfo().projectPath
        response = {
          command: WebViewRspCommand.PUSH_LOGIN_STATUS_RESPONSE,
          data: { code: WebViewRspCode.SUCCESS, config: { host, releaseVersion, ...config, clientVersion, versionDesc, projectPath } },
        };
        this.pushMessage2WebView(response);
        break;
      case EventType.WSSERVER_RECONNECT:
        response = {
          command: WebViewRspCommand.PUSH_NETWORK_STATUS_RESPONSE,
          data: { code: WebViewRspCode.WSSERVER_RECONNECT_SUCCESS },
        };
        this.pushMessage2WebView(response);
        break;
      case EventType.WSSERVER_ERROR:
        const code = data as number;
        response = {
          command: WebViewRspCommand.PUSH_NETWORK_STATUS_RESPONSE,
          data: { code },
        };
        this.pushMessage2WebView(response);
        break;
      case EventType.THEME_CHANGED:
        response = {
          command: WebViewRspCommand.PUSH_THEME_CHANGED,
          data: { theme: data },
        };
        this.pushMessage2WebView(response);
        break;
      case EventType.LOGOUT:
      case EventType.LOGIN_EXPIRED:
      case EventType.LOGIN_FAILED:
        response = {
          command: WebViewRspCommand.PUSH_LOGIN_STATUS_RESPONSE,
          data: { code: WebViewRspCode.NOT_LOGIN },
        };
        this.pushMessage2WebView(response);
        break;
      default:
        break;
    }
  }

  private pushMessage2WebView(response: WebViewResponse) {
    if (this.view?.webview) {
      this.sendMessageToWebView(response);
    } else {
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== response.command
      );
      this.cachedWebViewRspList.push(response);
    }
  }

  public dispose(): void {
    this.serviceObservable?.unregisterObserver(this);
  }

  /**
   * 收到chat回答
   * @param answer 回答
   * @param error 异常信息
   */
  public onCodeChatResponse(
    reqId?: string,
    isEnd?: number,
    answer?: string,
    error?: string,
    code?: WebViewRspCode,
    payload?: CodeAIResponsePayload
  ): void {
    let response;
    const { inValid, quote } = payload || {};
    if (inValid) {
      code = WebViewRspCode.INSERT_ERROR;
      error = RtnMessage[RtnCode.INSERT_ERROR];
    }

    if (!reqId || reqId === this.curReqId) {
      response = {
        command: WebViewRspCommand.ANSWER_RECVED,
        data: {
          reqId,
          isEnd: !!isEnd,
          answer,
          code,
          error,
          quote,
        },
      };
    }

    if (response) {
      this.sendMessageToWebView(response);

      if (isEnd === IsAnswerEnd.YES) {
        this.curReqId = '';
        this.codeChatEngin.updateConversationMessage();
      }
    }
  }

  /**
   * 重新发起提问时变更curReqId
   * @param newReqId
   */
  public onCurReqIdChange(newReqId?: string | undefined): void {
    if (newReqId && this.curReqId !== newReqId) {
      this.curReqId = newReqId;
    }
  }

  /**
   * 会话数据变更
   * @param eventType
   * @param data
   */
  public onConversationChange(resp: ConversationChangeParams): void {
    let response;
    const { eventType, data, code, isNewConversation, error } = resp;
    switch (eventType) {
      case ConversationEventType.DATA_UPDATED:
        // 全量会话列表数据
        response = {
          command: WebViewRspCommand.CONVERSATION_CHANGED,
          data: {
            code: code || RtnCode.SUCCESS,
            data,
            error,
          },
        };
        this.sendMessageToWebView(response);
        break;
      case ConversationEventType.DATA_REFRESHED:
        // 全量会话列表数据
        response = {
          command: WebViewRspCommand.CONVERSATION_REFRESHED,
          data: {
            code: code || RtnCode.SUCCESS,
            conversations: data,
            error,
          },
        };
        this.sendMessageToWebView(response);
        break;
      case ConversationEventType.DATA_REMOVED:
        response = {
          command: WebViewRspCommand.CONVERSATION_REMOVED,
          data: {
            code: code || RtnCode.SUCCESS,
            conversations: data,
            error,
          },
        };
        this.sendMessageToWebView(response);
        break;
      case ConversationEventType.DATA_ADDED:
        response = {
          command: WebViewRspCommand.CONVERSATION_ADDED,
          data: {
            code: code || RtnCode.SUCCESS,
            conversations: data,
            error,
          },
        };
        this.sendMessageToWebView(response);
        break;
      default:
        break;
    }
  }

  public onComposerChatResponse(reqType: string, dialogId: string, done: boolean, content: string) {
    const response = {
      command: WebViewRspCommand.COMPOSER_RESPONSE,
      data: {
        reqType,
        dialogId,
        done,
        content,
      },
    };
    this.sendMessageToWebView(response);
  }

  public onReportDiffFilesResponse(reqType: string, dialogId: string, content: DiffFile[]) {
    const response = {
      command: WebViewRspCommand.COMPOSER_RESPONSE,
      data: {
        reqType,
        dialogId,
        content,
      },
    };
    this.sendMessageToWebView(response);
  }

  public onDiffStatusChanged(diffFiles: DiffFile[]) {
    const response = {
      command: WebViewRspCommand.DIFF_STATUS_CHANGED,
      data: {
        content: diffFiles
      },
    };
    this.sendMessageToWebView(response);
  }

  public onComposerHistoryResponse(data: { reqType: string, historyList?: ChatHistory[] | null, error?: string }) {
    const response = {
      command: WebViewRspCommand.COMPOSER_RESPONSE,
      data,
    };
    this.sendMessageToWebView(response);
  }
  /**
   * 处理代码选中事件
   * @param eventType
   * @param data
   */
  public handleCodeSelectionEvent(eventType: CodeSelectionEventType, data: unknown): void {
    switch (eventType) {
      case CodeSelectionEventType.CODE_SELECTION_ASKED:
        this.askedByCodeSelection(data as CodeSelectionParams);
        break;
      case CodeSelectionEventType.CODE_SELECTION_CHANGED:
        this.pushCodeSelectionChanged(data as { code: string; filePath: string });
        break;
      default:
        break;
    }
  }

  public handleCodeScanEvent(response: WebViewResponse): void {
    // webview实例已生成
    if (this.view?.webview) {
      this.sendMessageToWebView(response);
    } else {
      // 打开chat窗口
      vscode.commands.executeCommand('workbench.view.extension.srd-chat');
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== WebViewRspCommand.CODE_SECURITY_SCAN_START
      );

      this.cachedWebViewRspList.push(response);
    }
  }

  public handleFileChatEvent(response: WebViewResponse) {
    if (this.view?.webview) {
      this.sendMessageToWebView(response);
    } else {
      // 打开chat窗口
      vscode.commands.executeCommand('workbench.view.extension.srd-chat');
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== WebViewRspCommand.QA_FOR_RELATED_FILES_RESPONSE
      );

      this.cachedWebViewRspList.push(response);
    }
  }

  /**
   * 接收webview发送消息
   * @param data
   */
  private didReceiveMessage(req: WebViewRequest) {
    // Logger.debug(`[ChatViewProvider] didReceiveMessage: ${JSON.stringify(req)}`);

    switch (req.command) {
      case WebViewReqCommand.WEBVIEW_LOADED:
        const theme = vscode.workspace.getConfiguration().get('workbench.colorTheme');
        const response = {
          command: WebViewRspCommand.PUSH_THEME_CHANGED,
          data: { theme },
        };
        this.pushMessage2WebView(response);
        this.sendCachedMessageToWebView();
        break;
      case WebViewReqCommand.CONVERSATION_ADD:
        this.addConversation((req.data || {}) as { title?: string });
        break;
      case WebViewReqCommand.CONVERSATION_SWITCH:
        const res = req.data as { dialogId: string };
        this.switchConversation(res.dialogId);
        break;
      case WebViewReqCommand.CONVERSATION_EDIT_TITLE: {
        const res = req.data as { dialogId: string; title: string };
        this.updateConversationTitle(res.dialogId, res.title);
        break;
      }
      case WebViewReqCommand.CONVERSATION_REMOVE:
        this.removeConversation((req.data as { dialogId: string }).dialogId);
        break;
      case WebViewReqCommand.CONVERSATION_REFRESH:
        this.refreshConversation(req.data as ChatHistoryRequest);
        break;
      case WebViewReqCommand.CONVERSATION_FEEDBACK:
        this.feedbackConversation(req.data as FeedbackAnswerRequest);
        break;
      case WebViewReqCommand.CHAT_REQUEST:
        this.sendChatRequest(req.data as ChatRequest);
        break;
      case WebViewReqCommand.LOGIN:
        this.login();
        break;
      case WebViewReqCommand.RETRIVE_CODE_SELECTION:
        this.retriveCodeSelection();
        break;
      case WebViewReqCommand.INSERT_CODE:
        this.insertTextEditor(req.data as { code: string });
        break;
      case WebViewReqCommand.INSERT_UNITTEST:
        this.insertTextEditor(req.data as { code: string }, true);
        break;
      case WebViewReqCommand.CANCEL_CHAT_REQUEST:
        this.cancelChatRequest();
        break;
      case WebViewReqCommand.STOP_CHAT_REQUEST:
        this.stopChatRequest(req.data as AnswerStopParams);
        break;
      case WebViewReqCommand.SRD_CHAT_REQUEST:
        this.sendSrdChatRequest(req.data as SrdChatRequest);
        break;
      case WebViewReqCommand.DATA_REPORT:
        this.reportData(req.data as dataReportParams);
        break;
      case WebViewReqCommand.CHECK_IF_LOGIN:
        this.checkIfLogin();
        break;
      case WebViewReqCommand.OPEN_EXTERNAL:
        this.openExternal(req.data as { path: string });
        break;
      case WebViewReqCommand.PROMPTS_REQUEST:
        this.sendPromptsRequest(req.data as PromptsRequest);
        break;
      case WebViewReqCommand.KNOWLEDGE_BASE_REQUEST:
        this.sendKbRequest(req.data as KnowledgeBaseRequest);
        break;
      case WebViewReqCommand.CODE_SECURITY_SCAN_REQUEST:
        this.sendCodeSecurityScanRequest(req.data);
        break;
      case WebViewReqCommand.COMPOSER_REQUEST:
        this.handleComposerRequest(req.data);
        break;
      case WebViewReqCommand.DIFF_VIEW_VERTICAL_REQUEST:
        this.diffViewHandler.handleDiffViewVerticalRequest(req.data as DiffViewVerticalParams);
        break;
      case WebViewReqCommand.GET_IDE_UTILS_REQUEST:
        this.handleIDEUtilsRequest(req.data);
      case WebViewReqCommand.QA_FOR_RELATED_FILES_REQUEST:
        this.sendRelatedFilesRequest(req.data);
        break;
      case WebViewReqCommand.OPEN_TEXT_DOCUMENT:
        this.openTextDocument(req.data);
        break;
      case WebViewReqCommand.VIEW_DIFF:
        CodeDiffManager.getInstance().showCodeDiffview(req.data as DiffViewParams);
        break;
      case WebViewReqCommand.INVOKE_TERMINAL_CAPABILITY:
        this.terminalMananger.handleTerminalInvocation(req.data as TerminalParams);
        break;
      case WebViewReqCommand.WORK_ITEM_REQUEST:
        this.handleWorkItemRequest(req.data)

        break;
      default:
        break;
    }
  }

  private async handleComposerRequest(request: any) {
    switch (request.reqType) {
      case ComposerEventType.COMPOSER_CHAT:
        this.composerService.startConversation(request as ComposerRequestParams);
        break;
      case ComposerEventType.STOP_COMPOSER_CHAT:
        this.composerService.stopConversation(request.dialogId);
        break;
      case ComposerEventType.LOAD_HISTORY:
      case ComposerEventType.DELETE_HISTORY:
        this.composerHistoryHandler.handleComposerHistoryRequest(request);
        break;
    }
  }

  private handleWorkItemRequest(request: any) {
    switch (request.reqType) {
      case WorkItemEventType.SEARCH_WORKITEMS:
        this.workItemManager.getWorkItems();
        break;
      case WorkItemEventType.OPEN_WORKITEM_PANEL:
        this.workItemManager.openPanel();
        break;
    }
  }


  private handleDiffViewVerticalRequest(request: DiffViewVerticalParams) {
    this.diffViewHandler.handleDiffViewVerticalRequest(request);
  }

  public handleIdeUtilEvent(response: WebViewResponse) {
    if (this.view?.webview) {
      this.sendMessageToWebView(response);
    } else {
      // 打开chat窗口
      vscode.commands.executeCommand('workbench.view.extension.srd-chat');
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== WebViewRspCommand.GET_IDE_UTILS_RESPONSE
      );
      this.cachedWebViewRspList.push(response);
    }
  }

  public handleWorkItemEvent(response: WebViewResponse) {
    const workItemData = response.data as WorkItemResponse;
    const simplifiedResponse: WebViewResponse = {
      ...response,
      data: {
        reqType: workItemData.reqType,
        workItemList: Array.isArray(workItemData.workItemList)
          ? workItemData.workItemList.map(item => ({
              id: item.id,
              workItemKey: item.workItemKey,
              description: item.description,
              title: item.title,
              creatorDisplayName: item.creatorDisplayName,
              url: item.url
            }))
          : (workItemData.workItemList === undefined || workItemData.workItemList === null ? [] : workItemData.workItemList),
        error: workItemData.error
      }
    };

    if (this.view?.webview) {
      this.sendMessageToWebView(simplifiedResponse);
    } else {
      // 打开chat窗口
      vscode.commands.executeCommand('workbench.view.extension.srd-chat');
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== WebViewRspCommand.WORK_ITEM_RESPONSE
      );
      this.cachedWebViewRspList.push(simplifiedResponse);
    }
  }

  private handleIDEUtilsRequest(request: any) {
    switch (request.reqType) {
      case IdeUtilEventType.GET_CHAT_CONTEXTS:
        this.ideUtilManager.returnChatContexts();
        break;
      default:
        break;
    }
  }

  private sendCodeSecurityScanRequest(request: any) {
    switch (request.reqType) {
      case CodeSecurityEventType.GET_SCAN_FILES:
        this.codeScanManager.getScanFiles(request as { reqType: string });
        break;
      case CodeSecurityEventType.RUN_SCAN:
        this.codeScanManager.runScan(request);
        break;
      case CodeSecurityEventType.QUERY_SCAN_ISSUES:
        this.codeScanManager.queryScanIssues(request);
        break;
      case CodeSecurityEventType.STOP_SCAN:
        this.codeScanManager.stopScan(request as { reqType: string; taskId: string });
        break;
      case CodeSecurityEventType.VIEW_DETAIL:
        this.codeScanManager.viewDetail(request as { fileName: string; line: number });
        break;
      case CodeSecurityEventType.AI_EXPLAIN:
        this.codeScanManager.aiExplain(request);
        break;
      case CodeSecurityEventType.STOP_AI_REQUEST:
        this.codeScanManager.stopAiRequest();
      default:
        break;
    }
  }

  private async sendRelatedFilesRequest(request: any) {
    switch (request.reqType) {
      case FileChatEventType.GET_RELATED_FILES:
        this.ideUtilManager.returnChatContexts();
        break;
      default:
        break;
    }
  }

  private openTextDocument(request: any) {
    this.ideUtilManager.openTextDocument(request);
  }

  private async refreshConversation(request: ChatHistoryRequest) {
    this.conversationManager.refreshConversations(request);
  }

  private async feedbackConversation(request: FeedbackAnswerRequest) {
    const result = await ChatHistoryServiceInst.feedbackAnswer(request);
    this.sendMessageToWebView({
      command: WebViewRspCommand.FEEDBACK_CONVERSATION_RESPONSE,
      data: result,
    });
  }

  private async sendKbRequest(request: KnowledgeBaseRequest) {
    let result;
    switch (request.reqType) {
      case KnowledgeBaseEventType.SEARCH_DEV_KBS:
        result = await KbServiceInst.searchDevKbs(request);
        break;
      case KnowledgeBaseEventType.GET_KB_INFO:
        result = await KbServiceInst.getKbInfo(request);
        break;
      default:
        break;
    }

    Logger.debug(`sendKbRequest result:${JSON.stringify(result)}`);
    this.sendMessageToWebView({
      command: WebViewRspCommand.KNOWLEDGE_BASE_RESPONSE,
      data: {
        type: request.reqType,
        data: result,
      },
    });
  }

  private async sendPromptsRequest(request: PromptsRequest) {
    let result;
    switch (request.reqType) {
      case PromptsEventType.GET_TEMPLATES:
        result = await PromptsServiceInst.getTemplates(request);
        break;
      case PromptsEventType.OPERATE_TEMPLATE:
        result = await PromptsServiceInst.operateTemplate(request);
        break;
      case PromptsEventType.GET_CATEGORIES:
        result = await PromptsServiceInst.getCategories();
        break;
      default:
        break;
    }
    Logger.debug(`sendPromptsRequest result:${JSON.stringify(result)}`);
    this.sendMessageToWebView({
      command: WebViewRspCommand.PROMPTS_RESPONSE,
      data: {
        type: request.reqType,
        data: result,
      },
    });
  }

  private openExternal(request: { path: string }) {
    const uri = `${getHttpServerHost()}${request.path}`;
    if (request.path.includes('helpcenter') && process.env.ISSEC !== 'false') {
      vscode.env.openExternal(vscode.Uri.parse(getHelpUrl()));
    } else {
      vscode.env.openExternal(vscode.Uri.parse(uri));
    }
  }

  private checkIfLogin() {
    let result = { code: RtnCode.SUCCESS, error: '' };
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      result = { code: RtnCode.NOT_LOGIN, error: ChatTips.NOT_LOGIN };
    }
    this.sendMessageToWebView({
      command: WebViewRspCommand.CHECK_IF_LOGIN_RESPONSE,
      data: result,
    });
  }

  private reportData(request: dataReportParams) {
    DataReportServiceInst.notifyUserActivity(request.activityType, request.answer);
  }

  /**
   * 发送信息回给webview
   * @param response
   */
  public sendMessageToWebView(response: WebViewResponse) {
    // Logger.debug(`[ChatViewProvider] sendMessageToWebView, ${JSON.stringify(response)}`);
    this.view?.webview.postMessage(response);
  }

  /**
   * 发送未打开webview前的缓存消息
   */
  private sendCachedMessageToWebView() {
    while (this.cachedWebViewRspList.length > 0) {
      const rsp = this.cachedWebViewRspList.shift();

      if (rsp) {
        this.sendMessageToWebView(rsp);
      }
    }
  }

  /**
   * 去登录
   */
  private async login() {
    await vscode.commands.executeCommand(SrdCommand.LOGIN);
  }

  private async generateFiles(relatedFiles: RelatedFile[] | undefined) {
    const files = [];
    let totalChars = 0;
    // dev环境没有配置ChatCharacterLimit参数，默认写入80000
    const MAX_TOTAL_CHARS = +(getCodeAIConfig()?.ChatCharacterLimit || 80000);
    if (!relatedFiles) {
      return;
    }

    for (let i = 0; i < relatedFiles.length; i++) {
      const path = relatedFiles[i].path;
      const startLine = relatedFiles[i].startLine;
      const endLine = relatedFiles[i].endLine;
      let text = relatedFiles[i].text;

      if (!text) {
        text = await getFileTextByPath(path, startLine, endLine);
      }

      if (text !== undefined) {
        if (totalChars + text.length <= MAX_TOTAL_CHARS) {
          files.push({ path, text, startLine, endLine });
          totalChars += text.length;
        } else {
          const remainingChars = MAX_TOTAL_CHARS - totalChars;
          const truncatedText = text.substring(0, remainingChars);
          const newlineCount = (truncatedText.match(/\r\n|\r|\n/g) || []).length;
          const newEndLine = (startLine ?? 0) + newlineCount - 1;
          files.push({
            path,
            text: truncatedText,
            startLine: startLine ?? 0,
            endLine: newEndLine,
          });
          totalChars += truncatedText.length;
          this.sendMessageToWebView({
            command: WebViewRspCommand.FILE_EXCEED_LIMIT
          });
          break;
        }
      }
    }
    return files;
  }

  /**
   * 发送chat请求
   * @param question
   */
  private async sendChatRequest(request: ChatRequest) {
    if (!this.curConversation) {
      return;
    }
    this.conversationManager.setConverstionSubservice(this.curConversation, request?.subService);

    EditorUtil.cancelSelectedCode();
    Logger.info(`sendChatRequest, request: ${JSON.stringify(request)}`);

    const relatedFiles = await this.generateFiles(request.relatedFiles);

    // Add new code to process context input items
    let combinedFiles = [...(relatedFiles || [])];
    if (request.contextInputItems) {
      const contextInputService = new ContextInputService();
      const [contextOutputItems, _] = await contextInputService.getContextCodeItemForInput(
        request.question,
        request.contextInputItems
      );

      // Transform context output items to match generateFiles output structure
      const transformedFiles = contextOutputItems.map(item => {
        // Parse the line range from name field which has format: "relative_path(start_line-end_line)"
        const matches = item.name.match(/.*\((\d+)-(\d+)\)/);
        let startLine = 0;
        let endLine = 0;

        if (matches && matches.length === 3) {
          startLine = parseInt(matches[1], 10);
          endLine = parseInt(matches[2], 10);
        }

        return {
          path: item.name.split('(')[0], // Get the path part before the line range
          text: item.content,
          startLine,
          endLine,
          codebase: item.origin === '@codebase', // true if origin is @codebase
          folderPath: item.origin === '@codebase' ? '' : item.origin, // empty if codebase, otherwise use origin value
        };
      });

      // Merge the transformed files with relatedFiles
      combinedFiles = [...combinedFiles, ...transformedFiles];
    }

    const reqId = this.codeChatEngin.askQuestion(this.curConversation, {
      ...request,
      relatedFiles: combinedFiles,
    });

    if (reqId) {
      this.curReqId = reqId;
    }
  }

  /**
   * 发送SRD chat请求
   * @param question
   */
  private async sendSrdChatRequest(request: SrdChatRequest) {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[sendSrdChatRequest] sendSrdChatRequest, not login');

      this.sendMessageToWebView({
        command: WebViewRspCommand.SRD_CHAT_RESPONSE,
        data: {
          type: request.type,
          data: {
            code: RtnCode.NOT_LOGIN,
            error: ChatTips.NOT_LOGIN,
          },
        },
      });
      return;
    }
    let result;
    switch (request.type) {
      case SrdChatEventType.ASK_QUESTION:
        result = await this.srdChatService.askQuestion(request.question);
        break;
      case SrdChatEventType.RECORD_LIST:
        result = await this.srdChatService.recordList(
          request as { timeBefore: string; currentPage: number; pageSize: number }
        );
        break;
      default:
        break;
    }

    this.sendMessageToWebView({
      command: WebViewRspCommand.SRD_CHAT_RESPONSE,
      data: {
        type: request.type,
        data: result,
      },
    });
  }

  /**
   * 取消当前chat请求
   */
  private cancelChatRequest() {
    if (this.curReqId) {
      this.codeChatEngin.cancelQuestion();
    }
  }

  /**
   * 停止当前chat回答
   */
  private async stopChatRequest(request: AnswerStopParams) {
    if (CodeAICommHandler.getInstance().getChannelStatus() === ChannelStatus.DISCONNECTED) {
      return;
    }
    const reqId = this.curReqId;
    if (reqId) {
      this.codeChatEngin.stopAnswer(reqId, request);
      // await ChatHistoryServiceInst.stopAnswer({ ...request, reqId });
    }
  }
  /**
   * 初始时加载本地已有会话
   */

  /**
   * 切换会话
   * @param id 会话Id
   */
  private async switchConversation(dialogId: string) {
    const conversation = this.conversationManager.getConversation(dialogId);
    if (conversation) {
      this.curConversation = conversation;
      const result = await this.conversationManager.updateConversation(conversation);
      Logger.info(`switchConversation, result: ${JSON.stringify(result)}`);
      this.sendMessageToWebView({
        command: WebViewRspCommand.SWITCH_CONVERSATION_RESPONSE,
        data: result,
      });
    }
  }

  private async sendConversationToWebview(dialogId: string, subService: string) {
    const result = await ChatHistoryServiceInst.getDialog({ dialogId, subService });
    this.sendMessageToWebView({
      command: WebViewRspCommand.SWITCH_CONVERSATION_RESPONSE,
      data: result,
    });
    return result;
  }

  /**
   * 新建会话
   */
  private addConversation(data: { title?: string }) {
    const params = {
      isNewConversation: true,
      ...data,
    };
    const conversation = new Conversation(params);
    this.curConversation = conversation;
    this.conversationManager.addConversation(conversation);
  }

  /**
   * 删除会话
   */
  private removeConversation(dialogId: string) {
    this.conversationManager.removeConversation(dialogId);

    if (dialogId === this.curConversation?.id) {
      this.curConversation = null;
      this.curReqId = '';
    }
  }

  /**
   * 清除当前会话的某条消息
   */
  private async removeConversationMessage(conversationId: string, msgId: string) {
    const conversation = await this.conversationManager.getConversation(conversationId);

    if (conversation) {
      conversation.removeMessage(msgId);
      this.conversationManager.updateAndSendConversation(conversation);
      this.conversationManager.saveConversations();
    }
  }

  /**
   * 修改会话标题
   * @param conversationId
   * @param title
   */
  private async updateConversationTitle(conversationId: string, title: string) {
    const conversation = this.conversationManager.getConversation(conversationId);

    if (conversation) {
      const result = await this.conversationManager.updateConversationTitle(conversationId, title);
      if (!result.code) {
        conversation.title = title;
      }
      this.conversationManager.saveConversationTitle(conversation, result);
    }
  }

  /**
   * 发送代码解释、代码注释、单元测试到chat
   * @param data
   */
  private askedByCodeSelection(data: CodeSelectionParams) {
    const response = {
      command: WebViewRspCommand.CODE_SELECTION_ASKED,
      data: {
        ...data,
        cancelLastQuestion: !!this.curReqId,
      },
    };

    // webview实例已生成
    if (this.view?.webview) {
      // 打开chat窗口
      vscode.commands.executeCommand('srd-copilot.chat.focus');
      if (this.curReqId) {
        return;
      }
      this.sendMessageToWebView(response);
    } else {
      // 未生成实例：打开chat窗口并生成实例, 缓存最新的选中指令，等webview生成好了再发送
      vscode.commands.executeCommand('workbench.view.extension.srd-chat');
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== WebViewRspCommand.CODE_SELECTION_ASKED
      );

      this.cachedWebViewRspList.push(response);
    }
  }

  /**
   * 获取当前选中代码
   */
  private retriveCodeSelection() {
    const activeEditor = vscode.window.activeTextEditor;
    if (!activeEditor) {
      return;
    }
    let filePath = activeEditor.document.uri.fsPath;
    const code = EditorUtil.getCurrentSelectedCode();
    let { startLine, endLine } = EditorUtil.getCurrentSelectedCodeLines();
    // code为空时，对filePath，startLine和endLine做如下设置
    // 这里startLine设置为-1是因为startLine是从0开始的，即第0行有意义，所以选择传-1
    if (!code) {
      filePath = '';
      startLine = -1;
      endLine = -1;
    }
    this.sendMessageToWebView({
      command: WebViewRspCommand.RETURN_CODE_SELECTION,
      data: {
        code,
        filePath,
        startLine,
        endLine,
      },
    });
  }

  /**
   * 推送当前选中代码变更到webview
   * @param code
   */
  private pushCodeSelectionChanged(code: { code: string; filePath: string }) {
    let { startLine, endLine } = EditorUtil.getCurrentSelectedCodeLines();
    if (!code.code) {
      code.filePath = '';
      startLine = -1;
      endLine = -1;
    }
    this.sendMessageToWebView({
      command: WebViewRspCommand.CODE_SELECTION_CHANGED,
      data: {
        code: code.code,
        filePath: code.filePath,
        startLine: startLine,
        endLine: endLine,
      },
    });
  }

  /**
   * 插入代码
   */
  private insertTextEditor(data: { code: string }, isNewFile?: boolean) {
    const { code } = data;

    if (!code) {
      vscode.window.showInformationMessage('插入代码为空，请重试', '关闭');
      return;
    }

    EditorUtil.insertTextEditor(code, isNewFile);
  }

  /**
   * 获取html内容
   * @param webview webview实例
   * @returns
   */
  private getHtmlForWebview(webview: vscode.Webview) {
    // Get the local path to main script run in the webview, then convert it to a uri we can use in the webview.
    const scriptUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this.extensionUri, 'dist', 'chat', 'index.js')
    );

    // Do the same for the stylesheet.
    const styleUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this.extensionUri, 'dist', 'chat', 'index.css')
    );

    // Use a nonce to only allow a specific script to be run.
    const nonce = getNonce();
    // <meta http-equiv="Content-Security-Policy" content="img-src * 'self' blob: data:; default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: gap: content:; style-src * 'self' 'unsafe-inline' ${webview.cspSource}; script-src 'nonce-${nonce}';">
    // <script type="module" crossorigin nonce="${nonce}" src="${scriptUri}"></script>

    return `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <link href="${styleUri}" crossorigin rel="stylesheet">

        <title>SRD Chat</title>
      </head>
      <body>
        <div id="app"></div>

        <script type="module" crossorigin src="${scriptUri}"></script>
      </body>
      </html>`;
  }
}
