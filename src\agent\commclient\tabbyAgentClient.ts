import { IAgentMessageReceiver } from './MessageReceiver';
import { Logger } from '../../utils/logger';
import { AgentCommClient } from './agentcommclient';
import { endWith } from 'rxjs';
/**
 * Tabby Agent 专用客户端
 * 扩展了基本的 AgentCommClient，添加了 Tabby 特定的方法
 */
export class TabbyAgentClient extends AgentCommClient {
  // 覆盖基类的 generatorTypes，提供 tabby Agent 特定的类型
  protected generatorTypes: string[] = [];

  constructor(process: any, messageReceiver: IAgentMessageReceiver) {
    super(process, messageReceiver);
  }

  /**
   * 重写请求方法，使用 Tabby 特有的 [] 格式
   */
  override request(
    messageType: string,
    data: any,
    messageId: string | null,

    onResponse: (data: any) => void
  ): void {
    const id = messageId || AgentCommClient.generateUuid();

    // Tabby 使用 [id, {func, args}] 格式
    const message = JSON.stringify([
      id,
      {
        func: messageType,
        args: Array.isArray(data) ? data : [data]
      }
    ]);

    Logger.debug("[Tabby Agent Client] 发送agent消息id:" + id)

    this.responseListeners.set(id, onResponse);
    this.write(message);
  }

  override async handleMessage(json: string): Promise<void> {

    this.messageReceiver.onAgentMessageHandler(json)

    if (json.trim().startsWith('["') && endWith('"]')) {
      const message = JSON.parse(json)
      Logger.debug("[Tabby Agent Client] 收到agent消息:" + json)

      const response = this.responseListeners.get(message[0])
      if (response != null) {
        Logger.debug("[Tabby Agent Client] 收到agent消息: " + message)

        // 回调response函数
        response(message)
      } else {

        // 回调函数丢失
        Logger.debug("[Tabby Agent Client] 丢失agent消息:" + message)
      }
    }
  }
}
