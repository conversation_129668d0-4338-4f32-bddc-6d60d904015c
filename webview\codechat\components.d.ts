/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AiFixBubble: typeof import('./src/components/AiFixBubble.vue')['default']
    CodeBubble: typeof import('./src/components/CodeBubble.vue')['default']
    CodeLoadingBubble: typeof import('./src/components/CodeLoadingBubble.vue')['default']
    HelpBubble: typeof import('./src/components/HelpBubble.vue')['default']
    IntroPanel: typeof import('./src/components/IntroPanel.vue')['default']
    LoginBubble: typeof import('./src/components/LoginBubble.vue')['default']
    LogoBubble: typeof import('./src/components/LogoBubble.vue')['default']
    LogoView: typeof import('./src/components/LogoView.vue')['default']
    MenuOptions: typeof import('./src/components/MenuOptions.vue')['default']
    NewBubble: typeof import('./src/components/NewBubble.vue')['default']
    PendingBubble: typeof import('./src/components/PendingBubble.vue')['default']
    PendingList: typeof import('./src/components/PendingList.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    Tab: typeof import('./src/components/Tab.vue')['default']
    UserBubble: typeof import('./src/components/UserBubble.vue')['default']
  }
}
