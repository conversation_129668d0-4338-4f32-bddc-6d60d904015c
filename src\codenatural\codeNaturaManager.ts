import * as vscode from 'vscode';
import {
  CodeNaturalDataType,
  CodeNaturalEventType,
  CodeNaturalTaskType,
  ICodeNaturalEventHandler,
} from './types';
import { APP_NAME, LoginStatus, RtnCode, SrdCommand } from '../common/constants';
import { SUPPORT_INTERACTIVE_VERSION } from '../common/config';
import NaturalCodeLensProvider from './naturalCodeLensProvider';
import { IStatusBarHandler } from '../statusbar/types';
import CodeNaturalTask from './codeNaturalTask';
import { LoginServiceInst } from '../service/loginService';

export default class CodeNaturalManager implements ICodeNaturalEventHandler, vscode.Disposable {
  private context: vscode.ExtensionContext;

  private disposables: vscode.Disposable[] = [];

  private naturalCodeLensProvider: NaturalCodeLensProvider;

  private statusBarHandler: IStatusBarHandler;

  private codeNaturalTaskMap: Map<string, CodeNaturalTask> = new Map();

  // interactive需要动态引入，所以不能提前指定类型，才用了any
  private srdInteractiveEditorSessionProvider: any;

  public constructor(context: vscode.ExtensionContext, statusBarHandler: IStatusBarHandler) {
    this.context = context;
    this.statusBarHandler = statusBarHandler;
    this.naturalCodeLensProvider = new NaturalCodeLensProvider(this);
  }

  /**
   * 初始化注册vscode组件和监听事件
   */
  public async init() {
    this.disposables.push(
      vscode.Disposable.from(
        this.registerInputEditor(),
        this.registerNaturalCodeLensProvider(),
        this.registerOnDidChangeActiveTextEditor(),
        this.registerOnDidChangeTabs(),
        this.registerOnDidChangeTextDocument()
      )
    );

    if (this.isSupportIntervative()) {
      this.disposables.push(
        vscode.Disposable.from(await this.registerInteractiveEditorSessionProvider())
      );
    }
  }

  public dispose() {
    this.disposables.forEach(disposable => disposable.dispose());
  }

  /**
   * 处理下层事件
   * @param eventType
   * @param params
   */
  public handleEvent(eventType: CodeNaturalEventType, params: unknown): void {
    switch (eventType) {
      case CodeNaturalEventType.ADD_TASK: {
        const data = params as {
          editor: vscode.TextEditor;
          input: string;
          type: CodeNaturalTaskType;
        };
        this.addTask(data.editor, data.input, data.type);
        break;
      }
      case CodeNaturalEventType.TASK_ON_ERROR:
      case CodeNaturalEventType.TASK_ON_FINISHED: {
        const data = params as { reqId: string; code: RtnCode };
        this.removeTask(data.reqId);
        break;
      }
      case CodeNaturalEventType.TASK_ON_ANSWER_END: {
        if (this.isSupportIntervative()) {
          this.srdInteractiveEditorSessionProvider.handleOnDidAnswerEnd(params);
        }
        break;
      }
      case CodeNaturalEventType.TASK_ON_REGENERATE: {
        const data = params as { oldReqId: string; newReqId: string };
        this.resetTask(data.oldReqId, data.newReqId);
        break;
      }
      default:
        break;
    }
  }

  /**
   * 获取不同类型数据实例
   * @param dataType
   * @param params
   * @returns
   */
  public getEventData(dataType: CodeNaturalDataType, params: unknown) {
    switch (dataType) {
      case CodeNaturalDataType.TASK:
        return this.getTaskByDocument(params as vscode.TextDocument);
      default:
        return;
    }
  }

  /**
   * 注册自然语言编程输入框组件
   * @returns
   */
  private registerInputEditor(): vscode.Disposable {
    return vscode.commands.registerTextEditorCommand(
      SrdCommand.SHOW_CODE_NATURAL_INPUT,
      async (editor: vscode.TextEditor) => {
        if (this.hasExistedTask(editor)) {
          return;
        }

        let newEditor = editor;

        // 当前文件不为空，则打开新文件; 为空时，刚在当前文件
        if (editor.document.getText().trim().length > 0) {
          const document = await vscode.workspace.openTextDocument({
            content: '',
            language: editor.document.languageId,
          });

          newEditor = await vscode.window.showTextDocument(document);
        }

        if (newEditor) {
          // 按vscode版本展示不同输入框效果
          if (!this.isSupportIntervative()) {
            // 顶部输入框
            await this.handleShowInputBox(newEditor);
          } else {
            // 行内输入框
            await this.handleShowInteractiveEditor(newEditor);
          }
        }
      }
    );
  }

  /**
   * 注册CodeLensProvider
   * @returns
   */
  private registerNaturalCodeLensProvider() {
    return vscode.languages.registerCodeLensProvider(
      { pattern: '**/*' },
      this.naturalCodeLensProvider
    );
  }

  /**
   * 注册InteractiveEditorSessionProvider
   * @returns
   */
  private async registerInteractiveEditorSessionProvider() {
    const SrdInteractiveEditorSessionProvider = (
      await import('./interactiveEditor/srdInteractiveEditorSessionProvider')
    ).default;

    this.srdInteractiveEditorSessionProvider = new SrdInteractiveEditorSessionProvider(this);

    return vscode.interactive.registerInteractiveEditorSessionProvider(
      this.srdInteractiveEditorSessionProvider,
      {
        label: APP_NAME,
      }
    );
  }

  /**
   * 注册监听ActiveEditor变化
   * @returns
   */
  private registerOnDidChangeActiveTextEditor() {
    return vscode.window.onDidChangeActiveTextEditor(editor => {
      if (editor) {
        const task = this.getTaskByDocument(editor.document);
        task?.setEditor(editor);
        task?.recoveryAnswer();
      }
    });
  }

  /**
   * 注册监听tab关闭事件
   * @returns
   */
  private registerOnDidChangeTabs() {
    return vscode.window.tabGroups.onDidChangeTabs(e => {
      // 移除已关闭文件的task
      if (e.closed.length > 0) {
        const editors = vscode.window.visibleTextEditors;
        const toRemoveReqId: string[] = [];
        for (const [reqId, task] of this.codeNaturalTaskMap) {
          const taskDocUri = task.getEditor().document.uri.toString();
          if (!editors.find(e => e.document.uri.toString() === taskDocUri)) {
            toRemoveReqId.push(reqId);
          }
        }

        if (toRemoveReqId.length > 0) {
          toRemoveReqId.forEach(reqId => {
            this.removeTask(reqId);
          });
        }
      }
    });
  }

  /**
   * 监听文档内容变更
   * @returns
   */
  private registerOnDidChangeTextDocument() {
    return vscode.workspace.onDidChangeTextDocument(e => {
      const { document, contentChanges } = e;
      const task = this.getTaskByDocument(document);
      task?.updateContentAfterAnswerEnd(contentChanges);
    });
  }

  /**
   * 实现顶部输入框效果
   */
  private async handleShowInputBox(editor: vscode.TextEditor) {
    const input = await vscode.window.showInputBox({
      title: 'CodeFree自然语言提问',
      prompt: 'CodeFree生成的回答有可能不符合您的期待',
      placeHolder: '请输入问题',
      validateInput(value: string) {
        if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
          return '请先登录CodeFree';
        }

        if (!value) {
          return '输入问题不能为空';
        }

        return undefined;
      },
    });

    if (input) {
      this.handleEvent(CodeNaturalEventType.ADD_TASK, {
        editor,
        input,
        type: CodeNaturalTaskType.INPUTBOX,
      });
    }
  }

  /**
   * 实现行内输入效果
   * @param editor
   */
  private async handleShowInteractiveEditor(editor: vscode.TextEditor) {
    await vscode.commands.executeCommand('vscode.editorChat.start', {
      initialRange: new vscode.Range(editor.selection.active, editor.selection.active),
      message: '',
      autoSend: false,
    });
  }

  /**
   * 新增提问任务
   * @param editor
   * @param input
   */
  private addTask(editor: vscode.TextEditor, input: string, type: CodeNaturalTaskType) {
    const task = new CodeNaturalTask(this, this.statusBarHandler, editor, type, input);
    const reqId = task.startQuestion();

    if (reqId) {
      this.codeNaturalTaskMap.set(reqId, task);
    }

    return reqId;
  }

  /**
   * 是否已存在提问任务
   * @param editor
   * @returns
   */
  private hasExistedTask(editor: vscode.TextEditor) {
    let existed = false;

    for (const [reqId, task] of this.codeNaturalTaskMap) {
      if (task.getEditor().document.uri.toString() === editor.document.uri.toString()) {
        existed = true;
        break;
      }
    }

    return existed;
  }

  /**
   * 移除提问任务
   * @param reqId
   */
  private removeTask(reqId: string) {
    this.codeNaturalTaskMap.delete(reqId);
  }

  /**
   * 重置任务，修改任务的reqId
   * @param oldReqId
   * @param newReqId
   */
  private resetTask(oldReqId: string, newReqId: string) {
    const task = this.codeNaturalTaskMap.get(oldReqId);

    if (task) {
      this.codeNaturalTaskMap.set(newReqId, task);
      this.removeTask(oldReqId);
    }
  }

  /**
   * 通过文档获取提问任务
   * @param document
   * @returns
   */
  private getTaskByDocument(document: vscode.TextDocument) {
    for (const [reqId, task] of this.codeNaturalTaskMap) {
      if (task.getEditor().document.uri.toString() === document.uri.toString()) {
        return task;
      }
    }

    return undefined;
  }

  /**
   * 是否支持行内输入框
   * @returns
   */
  private isSupportIntervative() {
    // TODO: 若vscode正式版支持Intervative接口，再按版本区分输入框效果
    // 目前暂不放开Intervative输入框效果
    //return vscode.version >= SUPPORT_INTERACTIVE_VERSION;
    return false;
  }
}
