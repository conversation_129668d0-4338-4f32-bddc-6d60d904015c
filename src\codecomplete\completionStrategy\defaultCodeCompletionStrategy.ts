import { AutocompleteParams, AutocompleteResult, CodeCompleteAnswer } from "../types";
import QuestionTask from '../../service/questionTask';
import { CodeCompletionStrategy } from "./codeCompletionStrategy";
import { IQuestionTaskEvent } from "../../service/types/questionTask";
import { CodeAIResponsePayload } from "../../service/types/codeAI";
import Defered from "../../utils/defer";
import { CodeCompleteEngin } from "../codeCompleteEngin";
import { AnswerMode, CodeCompleteStatus, IsAnswerEnd, QuestionType, RtnCode } from "../../common/constants";
import { Logger } from "../../utils/logger";
import TimeCounter from "../../utils/timeCounter";

export class DefaultCodeCompletionStrategy implements CodeCompletionStrategy {

  private curQuestion: QuestionTask | undefined;
  private taskEvent: DefaultCodeCompletionTaskEvent | undefined;

  isEnabled(): boolean {
    return true
  }

  async getCodeCompletion(codeCompleteEngin: CodeCompleteEngin, request: AutocompleteParams): Promise<AutocompleteResult | undefined> {

    this.taskEvent = new DefaultCodeCompletionTaskEvent(codeCompleteEngin)
    this.curQuestion = new QuestionTask(this.taskEvent, AnswerMode.SYNC);
    const questionType = request.isAuto ? QuestionType.CODE_GEN : QuestionType.CODE_GEN_MANUAL;
    const stopWords = codeCompleteEngin.getStopWords(questionType, request);

    const askResult = this.curQuestion.askQuestion({
      questionType,
      fileName: request.filename,
      language: request.language,
      prefix: request.before,
      suffix: request.after,
      stopWords,
      importSnippets: request.importSnippets,
    });

    if (!askResult.reqId) {
      codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.ERROR, askResult.rtnCode);
      return codeCompleteEngin.buildCodeCompleteResult();
    }

    const defered = new Defered<CodeCompleteAnswer>();
    this.taskEvent.getReqDeferedMap().set(askResult.reqId, defered);
    codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.START, request.isAuto);

    // 记录开始时间
    var timeCounter = new TimeCounter();
    Logger.debug(
      `[DefaultCodeCompletionStrategy] start askQuestion, [${request.line}, ${request.character
      }], reqId:${this.curQuestion?.getReqId()}`
    );


    // 等待回答推送回来
    const curAnswer = await defered.wait;

    this.taskEvent.getReqDeferedMap().delete(curAnswer.reqId);
    const isCurReqId = this.curQuestion?.getReqId() === curAnswer.reqId;

    if (isCurReqId) {
      if (curAnswer.rtnCode !== RtnCode.SUCCESS) {
        codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.ERROR, curAnswer.rtnCode);
      } else {
        codeCompleteEngin.getStatusBarHandler()?.onCodeCompleteStatusChange(CodeCompleteStatus.END);
      }

      // 记录请求时间
      const duration = timeCounter.getDuration();
      Logger.debug(
        `[DefaultCodeCompletionStrategy] askQuestion, reqId:${this.curQuestion?.getReqId()}, duration: ${duration}ms`
      );
    }

    // 构建回答
    const curQuestionParams = this.curQuestion?.getAskQuestionParams();
    const result = codeCompleteEngin.buildCodeCompleteResult(curAnswer.answer, isCurReqId, curQuestionParams);

    Logger.debug(
      `[DefaultCodeCompletionStrategy] askQuestion, reqId:${this.curQuestion?.getReqId()}, result:${JSON.stringify(
        result?.results?.[0].answer
      )}`
    );

    return result;
  }

  cancel(): void {

  }

}

class DefaultCodeCompletionTaskEvent implements IQuestionTaskEvent {
  private reqDeferedMap: Map<string, Defered<CodeCompleteAnswer>> = new Map();
  private codeCompleteEngin: CodeCompleteEngin;

  constructor(codeCompleteEngin: CodeCompleteEngin) {
    this.codeCompleteEngin = codeCompleteEngin;
  }

  public getReqDeferedMap(): Map<string, Defered<CodeCompleteAnswer>> {
    return this.reqDeferedMap;
  }

  onAnswer(reqId: string, isEnd: number, answer: string, payload?: CodeAIResponsePayload): void {
    /*****定制化处理代码补全模型返回之后的代码逻辑*****/
    let mAnswer = answer.replace(/(\n)+/g, '\n');
    mAnswer = this.codeCompleteEngin.handleCompleteAnswer(mAnswer);

    const defered = this.reqDeferedMap.get(reqId);
    if (defered) {
      defered.setFinished({
        reqId,
        isEnd,
        answer: mAnswer,
        rtnCode: RtnCode.SUCCESS,
      });
    }

  }

  onTaskError(reqId: string, rtnCode: number, errMsg?: string): void {
    Logger.error(`[DefaultCodeCompletionTask Event] onTaskError, reqId:${reqId}, rtnCode:${rtnCode}`);

    const defered = this.reqDeferedMap.get(reqId);

    if (defered) {
      defered.setFinished({ reqId, isEnd: IsAnswerEnd.YES, answer: '', rtnCode });
    }
  }

}