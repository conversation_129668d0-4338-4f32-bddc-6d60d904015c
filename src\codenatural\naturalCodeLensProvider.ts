import {
  CancellationToken,
  CodeLens,
  CodeLensProvider,
  Event,
  Position,
  ProviderResult,
  Range,
  TextDocument,
} from 'vscode';
import {
  CodeNaturalDataType,
  CodeNaturalTaskState,
  CodeNaturalTaskType,
  ICodeNaturalEventHandler,
} from './types';
import CodeNaturalTask from './codeNaturalTask';
import { SrdCommand } from '../common/constants';

export default class NaturalCodeLensProvider implements CodeLensProvider {
  public onDidChangeCodeLenses?: Event<void> | undefined;

  private eventHandler: ICodeNaturalEventHandler;

  public constructor(eventHandler: ICodeNaturalEventHandler) {
    this.eventHandler = eventHandler;
  }

  public provideCodeLenses(
    document: TextDocument,
    token: CancellationToken
  ): ProviderResult<CodeLens[]> {
    const items: CodeLens[] = [];
    const task = this.eventHandler.getEventData(
      CodeNaturalDataType.TASK,
      document
    ) as CodeNaturalTask;

    if (!task || task.getType() !== CodeNaturalTaskType.INPUTBOX) {
      return items;
    }

    switch (task.getCurrentState()) {
      case CodeNaturalTaskState.WaitingResp:
      case CodeNaturalTaskState.OnAnswering: {
        const startPosition = task.getStartPosition();

        if (startPosition) {
          items.push(
            new CodeLens(new Range(startPosition, startPosition), {
              command: '',
              title: '$(loading~spin)正在生成代码...',
            })
          );
        }

        break;
      }
      case CodeNaturalTaskState.OnAcceptOrReject: {
        const range = task.getRange();
        const hasValidCode = task.hasValidCode();

        if (range && task.getType() === CodeNaturalTaskType.INPUTBOX) {
          if (!hasValidCode) {
            items.push(
              new CodeLens(range, {
                command: '',
                title: '未生成有效代码',
              }),
              new CodeLens(range, {
                command: SrdCommand.REGENERATE_CODE_NATURAL_ANSWER,
                title: '$(srd-copilot-refresh) 重新生成代码',
                arguments: [task],
              }),
              new CodeLens(range, {
                command: SrdCommand.CANCEL_CODE_NATURAL_QUESTION,
                title: '$(srd-copilot-cancel) 取消提问',
                arguments: [task],
              })
            );
          } else {
            items.push(
              new CodeLens(range, {
                command: SrdCommand.ACCEPT_CODE_NATURAL_ANSWER,
                title: '$(srd-copilot-select) 确认生成代码',
                arguments: [task],
              }),
              new CodeLens(range, {
                command: SrdCommand.REJECT_CODE_NATURAL_ANSWER,
                title: '$(srd-copilot-delete) 删除高亮区域',
                arguments: [task],
              }),
              new CodeLens(range, {
                command: SrdCommand.REGENERATE_CODE_NATURAL_ANSWER,
                title: '$(srd-copilot-refresh) 重新生成代码',
                arguments: [task],
              })
            );
          }
        }

        break;
      }
      default:
        break;
    }

    return items;
  }

  public resolveCodeLens?(codeLens: CodeLens, token: CancellationToken): ProviderResult<CodeLens> {
    return;
  }
}
