body[arco-theme=dark]{--cf-input-border: #505356;--cf-input-disabled-cover-color: #000;--cf-ai-bubble-background: #35393da3;--vscode-input-border: #3c3c3c;--vscode-code-border: #616060d5;--vscode-font-family: -apple-system, BlinkMacSystemFont, sans-serif, gogogo;--vscode-font-weight: normal;--vscode-font-size: 13px;--vscode-editor-font-family: Menlo, Monaco, "Courier New", monospace;--vscode-editor-font-weight: normal;--vscode-editor-font-size: 15px;--text-link-decoration: none;--vscode-foreground: #cccccc;--vscode-disabledForeground: rgba(204, 204, 204, .5);--vscode-errorForeground: #f48771;--vscode-descriptionForeground: rgba(204, 204, 204, .7);--vscode-icon-foreground: #c5c5c5;--vscode-focusBorder: #007fd4;--vscode-textLink-foreground: #3794ff;--vscode-textLink-activeForeground: #3794ff;--vscode-textSeparator-foreground: rgba(255, 255, 255, .18);--vscode-textPreformat-foreground: #d7ba7d;--vscode-textPreformat-background: rgba(255, 255, 255, .1);--vscode-textBlockQuote-background: #222222;--vscode-textBlockQuote-border: rgba(0, 122, 204, .5);--vscode-textCodeBlock-background: rgba(10, 10, 10, .4);--vscode-sash-hoverBorder: #007fd4;--vscode-badge-background: #4d4d4d;--vscode-badge-foreground: #ffffff;--vscode-scrollbar-shadow: #000000;--vscode-scrollbarSlider-background: rgba(121, 121, 121, .4);--vscode-scrollbarSlider-hoverBackground: rgba(100, 100, 100, .7);--vscode-scrollbarSlider-activeBackground: rgba(191, 191, 191, .4);--vscode-progressBar-background: #0e70c0;--vscode-editor-background: #1e1e1e;--vscode-editor-foreground: #d4d4d4;--vscode-editorStickyScroll-background: #1e1e1e;--vscode-editorStickyScrollHover-background: #2a2d2e;--vscode-editorStickyScroll-shadow: #000000;--vscode-editorWidget-background: #252526;--vscode-editorWidget-foreground: #cccccc;--vscode-editorWidget-border: #454545;--vscode-editorError-foreground: #f14c4c;--vscode-editorWarning-foreground: #cca700;--vscode-editorInfo-foreground: #3794ff;--vscode-editorHint-foreground: rgba(238, 238, 238, .7);--vscode-editorLink-activeForeground: #4e94ce;--vscode-editor-selectionBackground: #264f78;--vscode-editor-inactiveSelectionBackground: #3a3d41;--vscode-editor-selectionHighlightBackground: rgba(173, 214, 255, .15);--vscode-editor-compositionBorder: #ffffff;--vscode-editor-findMatchBackground: #515c6a;--vscode-editor-findMatchHighlightBackground: rgba(234, 92, 0, .33);--vscode-editor-findRangeHighlightBackground: rgba(58, 61, 65, .4);--vscode-editor-hoverHighlightBackground: rgba(38, 79, 120, .25);--vscode-editorHoverWidget-background: #252526;--vscode-editorHoverWidget-foreground: #cccccc;--vscode-editorHoverWidget-border: #454545;--vscode-editorHoverWidget-statusBarBackground: #2c2c2d;--vscode-editorInlayHint-foreground: #969696;--vscode-editorInlayHint-background: rgba(77, 77, 77, .1);--vscode-editorInlayHint-typeForeground: #969696;--vscode-editorInlayHint-typeBackground: rgba(77, 77, 77, .1);--vscode-editorInlayHint-parameterForeground: #969696;--vscode-editorInlayHint-parameterBackground: rgba(77, 77, 77, .1);--vscode-editorLightBulb-foreground: #ffcc00;--vscode-editorLightBulbAutoFix-foreground: #75beff;--vscode-editorLightBulbAi-foreground: #ffcc00;--vscode-editor-snippetTabstopHighlightBackground: rgba(124, 124, 124, .3);--vscode-editor-snippetFinalTabstopHighlightBorder: #525252;--vscode-diffEditor-insertedTextBackground: rgba(156, 204, 44, .2);--vscode-diffEditor-removedTextBackground: rgba(255, 0, 0, .2);--vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, .2);--vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, .2);--vscode-diffEditor-diagonalFill: rgba(204, 204, 204, .2);--vscode-diffEditor-unchangedRegionBackground: #252526;--vscode-diffEditor-unchangedRegionForeground: #cccccc;--vscode-diffEditor-unchangedCodeBackground: rgba(116, 116, 116, .16);--vscode-widget-shadow: rgba(0, 0, 0, .36);--vscode-widget-border: #303031;--vscode-toolbar-hoverBackground: rgba(90, 93, 94, .31);--vscode-toolbar-activeBackground: rgba(99, 102, 103, .31);--vscode-breadcrumb-foreground: rgba(204, 204, 204, .8);--vscode-breadcrumb-background: #1e1e1e;--vscode-breadcrumb-focusForeground: #e0e0e0;--vscode-breadcrumb-activeSelectionForeground: #e0e0e0;--vscode-breadcrumbPicker-background: #252526;--vscode-merge-currentHeaderBackground: rgba(64, 200, 174, .5);--vscode-merge-currentContentBackground: rgba(64, 200, 174, .2);--vscode-merge-incomingHeaderBackground: rgba(64, 166, 255, .5);--vscode-merge-incomingContentBackground: rgba(64, 166, 255, .2);--vscode-merge-commonHeaderBackground: rgba(96, 96, 96, .4);--vscode-merge-commonContentBackground: rgba(96, 96, 96, .16);--vscode-editorOverviewRuler-currentContentForeground: rgba(64, 200, 174, .5);--vscode-editorOverviewRuler-incomingContentForeground: rgba(64, 166, 255, .5);--vscode-editorOverviewRuler-commonContentForeground: rgba(96, 96, 96, .4);--vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, .49);--vscode-editorOverviewRuler-selectionHighlightForeground: rgba(160, 160, 160, .8);--vscode-problemsErrorIcon-foreground: #f14c4c;--vscode-problemsWarningIcon-foreground: #cca700;--vscode-problemsInfoIcon-foreground: #3794ff;--vscode-minimap-findMatchHighlight: #d18616;--vscode-minimap-selectionOccurrenceHighlight: #676767;--vscode-minimap-selectionHighlight: #264f78;--vscode-minimap-infoHighlight: #3794ff;--vscode-minimap-warningHighlight: #cca700;--vscode-minimap-errorHighlight: rgba(255, 18, 18, .7);--vscode-minimap-foregroundOpacity: #000000;--vscode-minimapSlider-background: rgba(121, 121, 121, .2);--vscode-minimapSlider-hoverBackground: rgba(100, 100, 100, .35);--vscode-minimapSlider-activeBackground: rgba(191, 191, 191, .2);--vscode-charts-foreground: #cccccc;--vscode-charts-lines: rgba(204, 204, 204, .5);--vscode-charts-red: #f14c4c;--vscode-charts-blue: #3794ff;--vscode-charts-yellow: #cca700;--vscode-charts-orange: #d18616;--vscode-charts-green: #89d185;--vscode-charts-purple: #b180d7;--vscode-input-background: #3c3c3c;--vscode-input-foreground: #cccccc;--vscode-inputOption-activeBorder: #007acc;--vscode-inputOption-hoverBackground: rgba(90, 93, 94, .5);--vscode-inputOption-activeBackground: rgba(0, 127, 212, .4);--vscode-inputOption-activeForeground: #ffffff;--vscode-input-placeholderForeground: #a6a6a6;--vscode-inputValidation-infoBackground: #063b49;--vscode-inputValidation-infoBorder: #007acc;--vscode-inputValidation-warningBackground: #352a05;--vscode-inputValidation-warningBorder: #b89500;--vscode-inputValidation-errorBackground: #5a1d1d;--vscode-inputValidation-errorBorder: #be1100;--vscode-dropdown-background: #3c3c3c;--vscode-dropdown-foreground: #f0f0f0;--vscode-dropdown-border: #3c3c3c;--vscode-button-foreground: #ffffff;--vscode-button-separator: rgba(255, 255, 255, .4);--vscode-button-background: #0e639c;--vscode-button-hoverBackground: #1177bb;--vscode-button-secondaryForeground: #ffffff;--vscode-button-secondaryBackground: #3a3d41;--vscode-button-secondaryHoverBackground: #45494e;--vscode-radio-activeForeground: #ffffff;--vscode-radio-activeBackground: rgba(0, 127, 212, .4);--vscode-radio-activeBorder: #007acc;--vscode-radio-inactiveBorder: rgba(255, 255, 255, .2);--vscode-radio-inactiveHoverBackground: rgba(90, 93, 94, .5);--vscode-checkbox-background: #3c3c3c;--vscode-checkbox-selectBackground: #252526;--vscode-checkbox-foreground: #f0f0f0;--vscode-checkbox-border: #6b6b6b;--vscode-checkbox-selectBorder: #c5c5c5;--vscode-keybindingLabel-background: rgba(128, 128, 128, .17);--vscode-keybindingLabel-foreground: #cccccc;--vscode-keybindingLabel-border: rgba(51, 51, 51, .6);--vscode-keybindingLabel-bottomBorder: rgba(68, 68, 68, .6);--vscode-list-focusOutline: #007fd4;--vscode-list-activeSelectionBackground: #04395e;--vscode-list-activeSelectionForeground: #ffffff;--vscode-list-activeSelectionIconForeground: #ffffff;--vscode-list-inactiveSelectionBackground: #37373d;--vscode-list-hoverBackground: #2a2d2e;--vscode-list-dropBackground: #383b3d;--vscode-list-dropBetweenBackground: #c5c5c5;--vscode-list-highlightForeground: #2aaaff;--vscode-list-focusHighlightForeground: #2aaaff;--vscode-list-invalidItemForeground: #b89500;--vscode-list-errorForeground: #f88070;--vscode-list-warningForeground: #cca700;--vscode-listFilterWidget-background: #252526;--vscode-listFilterWidget-outline: rgba(0, 0, 0, 0);--vscode-listFilterWidget-noMatchesOutline: #be1100;--vscode-listFilterWidget-shadow: rgba(0, 0, 0, .36);--vscode-list-filterMatchBackground: rgba(234, 92, 0, .33);--vscode-list-deemphasizedForeground: #8c8c8c;--vscode-tree-indentGuidesStroke: #585858;--vscode-tree-inactiveIndentGuidesStroke: rgba(88, 88, 88, .4);--vscode-tree-tableColumnsBorder: rgba(204, 204, 204, .13);--vscode-tree-tableOddRowsBackground: rgba(204, 204, 204, .04);--vscode-editorActionList-background: #252526;--vscode-editorActionList-foreground: #cccccc;--vscode-editorActionList-focusForeground: #ffffff;--vscode-editorActionList-focusBackground: #04395e;--vscode-menu-border: #454545;--vscode-menu-foreground: #cccccc;--vscode-menu-background: #252526;--vscode-menu-selectionForeground: #ffffff;--vscode-menu-selectionBackground: #0078d4;--vscode-menu-separatorBackground: #454545;--vscode-quickInput-background: #252526;--vscode-quickInput-foreground: #cccccc;--vscode-quickInputTitle-background: rgba(255, 255, 255, .1);--vscode-pickerGroup-foreground: #3794ff;--vscode-pickerGroup-border: #3f3f46;--vscode-quickInputList-focusForeground: #ffffff;--vscode-quickInputList-focusIconForeground: #ffffff;--vscode-quickInputList-focusBackground: #04395e;--vscode-search-resultsInfoForeground: rgba(204, 204, 204, .65);--vscode-searchEditor-findMatchBackground: rgba(234, 92, 0, .22);--vscode-editor-lineHighlightBorder: #282828;--vscode-editor-rangeHighlightBackground: rgba(255, 255, 255, .04);--vscode-editor-symbolHighlightBackground: rgba(234, 92, 0, .33);--vscode-editorCursor-foreground: #aeafad;--vscode-editorMultiCursor-primary\.foreground: #aeafad;--vscode-editorMultiCursor-secondary\.foreground: #aeafad;--vscode-editorWhitespace-foreground: rgba(227, 228, 226, .16);--vscode-editorLineNumber-foreground: #858585;--vscode-editorIndentGuide-background: rgba(227, 228, 226, .16);--vscode-editorIndentGuide-activeBackground: rgba(227, 228, 226, .16);--vscode-editorIndentGuide-background1: #404040;--vscode-editorIndentGuide-background2: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-background3: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-background4: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-background5: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-background6: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-activeBackground1: #707070;--vscode-editorIndentGuide-activeBackground2: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-activeBackground3: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-activeBackground4: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-activeBackground5: rgba(0, 0, 0, 0);--vscode-editorIndentGuide-activeBackground6: rgba(0, 0, 0, 0);--vscode-editorActiveLineNumber-foreground: #c6c6c6;--vscode-editorLineNumber-activeForeground: #c6c6c6;--vscode-editorRuler-foreground: #5a5a5a;--vscode-editorCodeLens-foreground: #999999;--vscode-editorBracketMatch-background: rgba(0, 100, 0, .1);--vscode-editorBracketMatch-border: #888888;--vscode-editorOverviewRuler-border: rgba(127, 127, 127, .3);--vscode-editorGutter-background: #1e1e1e;--vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, .67);--vscode-editorGhostText-foreground: rgba(255, 255, 255, .34);--vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, .6);--vscode-editorOverviewRuler-errorForeground: rgba(255, 18, 18, .7);--vscode-editorOverviewRuler-warningForeground: #cca700;--vscode-editorOverviewRuler-infoForeground: #3794ff;--vscode-editorBracketHighlight-foreground1: #ffd700;--vscode-editorBracketHighlight-foreground2: #da70d6;--vscode-editorBracketHighlight-foreground3: #179fff;--vscode-editorBracketHighlight-foreground4: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-foreground5: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-foreground6: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-unexpectedBracket\.foreground: rgba(255, 18, 18, .8);--vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);--vscode-editorUnicodeHighlight-border: #cca700;--vscode-diffEditor-move\.border: rgba(139, 139, 139, .61);--vscode-diffEditor-moveActive\.border: #ffa500;--vscode-diffEditor-unchangedRegionShadow: #000000;--vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;--vscode-symbolIcon-arrayForeground: #cccccc;--vscode-symbolIcon-booleanForeground: #cccccc;--vscode-symbolIcon-classForeground: #ee9d28;--vscode-symbolIcon-colorForeground: #cccccc;--vscode-symbolIcon-constantForeground: #cccccc;--vscode-symbolIcon-constructorForeground: #b180d7;--vscode-symbolIcon-enumeratorForeground: #ee9d28;--vscode-symbolIcon-enumeratorMemberForeground: #75beff;--vscode-symbolIcon-eventForeground: #ee9d28;--vscode-symbolIcon-fieldForeground: #75beff;--vscode-symbolIcon-fileForeground: #cccccc;--vscode-symbolIcon-folderForeground: #cccccc;--vscode-symbolIcon-functionForeground: #b180d7;--vscode-symbolIcon-interfaceForeground: #75beff;--vscode-symbolIcon-keyForeground: #cccccc;--vscode-symbolIcon-keywordForeground: #cccccc;--vscode-symbolIcon-methodForeground: #b180d7;--vscode-symbolIcon-moduleForeground: #cccccc;--vscode-symbolIcon-namespaceForeground: #cccccc;--vscode-symbolIcon-nullForeground: #cccccc;--vscode-symbolIcon-numberForeground: #cccccc;--vscode-symbolIcon-objectForeground: #cccccc;--vscode-symbolIcon-operatorForeground: #cccccc;--vscode-symbolIcon-packageForeground: #cccccc;--vscode-symbolIcon-propertyForeground: #cccccc;--vscode-symbolIcon-referenceForeground: #cccccc;--vscode-symbolIcon-snippetForeground: #cccccc;--vscode-symbolIcon-stringForeground: #cccccc;--vscode-symbolIcon-structForeground: #cccccc;--vscode-symbolIcon-textForeground: #cccccc;--vscode-symbolIcon-typeParameterForeground: #cccccc;--vscode-symbolIcon-unitForeground: #cccccc;--vscode-symbolIcon-variableForeground: #75beff;--vscode-actionBar-toggledBackground: #383a49;--vscode-peekViewTitle-background: #252526;--vscode-peekViewTitleLabel-foreground: #ffffff;--vscode-peekViewTitleDescription-foreground: rgba(204, 204, 204, .7);--vscode-peekView-border: #3794ff;--vscode-peekViewResult-background: #252526;--vscode-peekViewResult-lineForeground: #bbbbbb;--vscode-peekViewResult-fileForeground: #ffffff;--vscode-peekViewResult-selectionBackground: rgba(51, 153, 255, .2);--vscode-peekViewResult-selectionForeground: #ffffff;--vscode-peekViewEditor-background: #001f33;--vscode-peekViewEditorGutter-background: #001f33;--vscode-peekViewEditorStickyScroll-background: #001f33;--vscode-peekViewResult-matchHighlightBackground: rgba(234, 92, 0, .3);--vscode-peekViewEditor-matchHighlightBackground: rgba(255, 143, 0, .6);--vscode-editor-foldBackground: rgba(38, 79, 120, .3);--vscode-editor-foldPlaceholderForeground: #808080;--vscode-editorGutter-foldingControlForeground: #c5c5c5;--vscode-editorSuggestWidget-background: #252526;--vscode-editorSuggestWidget-border: #454545;--vscode-editorSuggestWidget-foreground: #d4d4d4;--vscode-editorSuggestWidget-selectedForeground: #ffffff;--vscode-editorSuggestWidget-selectedIconForeground: #ffffff;--vscode-editorSuggestWidget-selectedBackground: #04395e;--vscode-editorSuggestWidget-highlightForeground: #2aaaff;--vscode-editorSuggestWidget-focusHighlightForeground: #2aaaff;--vscode-editorSuggestWidgetStatus-foreground: rgba(212, 212, 212, .5);--vscode-inlineEdit-indicator\.foreground: #cccccc;--vscode-inlineEdit-indicator\.background: #252526;--vscode-inlineEdit-indicator\.border: #454545;--vscode-inlineEdit-originalBackground: rgba(255, 0, 0, .08);--vscode-inlineEdit-modifiedBackground: rgba(156, 204, 44, .08);--vscode-inlineEdit-border: #3c3c3c;--vscode-editorMarkerNavigationError-background: #f14c4c;--vscode-editorMarkerNavigationError-headerBackground: rgba(241, 76, 76, .1);--vscode-editorMarkerNavigationWarning-background: #cca700;--vscode-editorMarkerNavigationWarning-headerBackground: rgba(204, 167, 0, .1);--vscode-editorMarkerNavigationInfo-background: #3794ff;--vscode-editorMarkerNavigationInfo-headerBackground: rgba(55, 148, 255, .1);--vscode-editorMarkerNavigation-background: #1e1e1e;--vscode-editor-linkedEditingBackground: rgba(255, 0, 0, .3);--vscode-editor-wordHighlightBackground: rgba(87, 87, 87, .72);--vscode-editor-wordHighlightStrongBackground: rgba(0, 73, 114, .72);--vscode-editor-wordHighlightTextBackground: rgba(87, 87, 87, .72);--vscode-editorOverviewRuler-wordHighlightForeground: rgba(160, 160, 160, .8);--vscode-editorOverviewRuler-wordHighlightStrongForeground: rgba(192, 160, 192, .8);--vscode-editorOverviewRuler-wordHighlightTextForeground: rgba(160, 160, 160, .8);--vscode-editorHoverWidget-highlightForeground: #2aaaff;--vscode-editor-placeholder\.foreground: rgba(255, 255, 255, .34);--vscode-tab-activeBackground: #1e1e1e;--vscode-tab-unfocusedActiveBackground: #1e1e1e;--vscode-tab-inactiveBackground: #2d2d2d;--vscode-tab-unfocusedInactiveBackground: #2d2d2d;--vscode-tab-activeForeground: #ffffff;--vscode-tab-inactiveForeground: rgba(255, 255, 255, .5);--vscode-tab-unfocusedActiveForeground: rgba(255, 255, 255, .5);--vscode-tab-unfocusedInactiveForeground: rgba(255, 255, 255, .25);--vscode-tab-border: #252526;--vscode-tab-lastPinnedBorder: rgba(204, 204, 204, .2);--vscode-tab-selectedBackground: #222222;--vscode-tab-selectedForeground: rgba(255, 255, 255, .63);--vscode-tab-dragAndDropBorder: #ffffff;--vscode-tab-activeModifiedBorder: #3399cc;--vscode-tab-inactiveModifiedBorder: rgba(51, 153, 204, .5);--vscode-tab-unfocusedActiveModifiedBorder: rgba(51, 153, 204, .5);--vscode-tab-unfocusedInactiveModifiedBorder: rgba(51, 153, 204, .25);--vscode-editorPane-background: #1e1e1e;--vscode-editorGroupHeader-tabsBackground: #252526;--vscode-editorGroupHeader-noTabsBackground: #1e1e1e;--vscode-editorGroup-border: #444444;--vscode-editorGroup-dropBackground: rgba(83, 89, 93, .5);--vscode-editorGroup-dropIntoPromptForeground: #cccccc;--vscode-editorGroup-dropIntoPromptBackground: #252526;--vscode-sideBySideEditor-horizontalBorder: #444444;--vscode-sideBySideEditor-verticalBorder: #444444;--vscode-panel-background: #1e1e1e;--vscode-panel-border: rgba(128, 128, 128, .35);--vscode-panelTitle-activeForeground: #e7e7e7;--vscode-panelTitle-inactiveForeground: rgba(231, 231, 231, .6);--vscode-panelTitle-activeBorder: #e7e7e7;--vscode-panel-dropBorder: #e7e7e7;--vscode-panelSection-dropBackground: rgba(83, 89, 93, .5);--vscode-panelSectionHeader-background: rgba(128, 128, 128, .2);--vscode-panelSection-border: rgba(128, 128, 128, .35);--vscode-panelStickyScroll-background: #1e1e1e;--vscode-panelStickyScroll-shadow: #000000;--vscode-banner-background: #04395e;--vscode-banner-foreground: #ffffff;--vscode-banner-iconForeground: #3794ff;--vscode-statusBar-foreground: #ffffff;--vscode-statusBar-noFolderForeground: #ffffff;--vscode-statusBar-background: #007acc;--vscode-statusBar-noFolderBackground: #68217a;--vscode-statusBar-focusBorder: #ffffff;--vscode-statusBarItem-activeBackground: rgba(255, 255, 255, .18);--vscode-statusBarItem-focusBorder: #ffffff;--vscode-statusBarItem-hoverBackground: rgba(255, 255, 255, .12);--vscode-statusBarItem-hoverForeground: #ffffff;--vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, .2);--vscode-statusBarItem-prominentForeground: #ffffff;--vscode-statusBarItem-prominentBackground: rgba(0, 0, 0, .5);--vscode-statusBarItem-prominentHoverForeground: #ffffff;--vscode-statusBarItem-prominentHoverBackground: rgba(0, 0, 0, .3);--vscode-statusBarItem-errorBackground: #c72e0f;--vscode-statusBarItem-errorForeground: #ffffff;--vscode-statusBarItem-errorHoverForeground: #ffffff;--vscode-statusBarItem-errorHoverBackground: rgba(255, 255, 255, .12);--vscode-statusBarItem-warningBackground: #7a6400;--vscode-statusBarItem-warningForeground: #ffffff;--vscode-statusBarItem-warningHoverForeground: #ffffff;--vscode-statusBarItem-warningHoverBackground: rgba(255, 255, 255, .12);--vscode-activityBar-background: #333333;--vscode-activityBar-foreground: #ffffff;--vscode-activityBar-inactiveForeground: rgba(255, 255, 255, .4);--vscode-activityBar-activeBorder: #ffffff;--vscode-activityBar-dropBorder: #ffffff;--vscode-activityBarBadge-background: #007acc;--vscode-activityBarBadge-foreground: #ffffff;--vscode-activityBarTop-foreground: #e7e7e7;--vscode-activityBarTop-activeBorder: #e7e7e7;--vscode-activityBarTop-inactiveForeground: rgba(231, 231, 231, .6);--vscode-activityBarTop-dropBorder: #e7e7e7;--vscode-profileBadge-background: #4d4d4d;--vscode-profileBadge-foreground: #ffffff;--vscode-statusBarItem-remoteBackground: #16825d;--vscode-statusBarItem-remoteForeground: #ffffff;--vscode-statusBarItem-remoteHoverForeground: #ffffff;--vscode-statusBarItem-remoteHoverBackground: rgba(255, 255, 255, .12);--vscode-statusBarItem-offlineBackground: #6c1717;--vscode-statusBarItem-offlineForeground: #ffffff;--vscode-statusBarItem-offlineHoverForeground: #ffffff;--vscode-statusBarItem-offlineHoverBackground: rgba(255, 255, 255, .12);--vscode-extensionBadge-remoteBackground: #007acc;--vscode-extensionBadge-remoteForeground: #ffffff;--vscode-sideBar-background: #252526;--vscode-sideBarTitle-background: #252526;--vscode-sideBarTitle-foreground: #bbbbbb;--vscode-sideBar-dropBackground: rgba(83, 89, 93, .5);--vscode-sideBarSectionHeader-background: rgba(0, 0, 0, 0);--vscode-sideBarSectionHeader-border: rgba(204, 204, 204, .2);--vscode-sideBarActivityBarTop-border: rgba(204, 204, 204, .2);--vscode-sideBarStickyScroll-background: #252526;--vscode-sideBarStickyScroll-shadow: #000000;--vscode-titleBar-activeForeground: #cccccc;--vscode-titleBar-inactiveForeground: rgba(204, 204, 204, .6);--vscode-titleBar-activeBackground: #3c3c3c;--vscode-titleBar-inactiveBackground: rgba(60, 60, 60, .6);--vscode-menubar-selectionForeground: #cccccc;--vscode-menubar-selectionBackground: rgba(90, 93, 94, .31);--vscode-commandCenter-foreground: #cccccc;--vscode-commandCenter-activeForeground: #cccccc;--vscode-commandCenter-inactiveForeground: rgba(204, 204, 204, .6);--vscode-commandCenter-background: rgba(255, 255, 255, .05);--vscode-commandCenter-activeBackground: rgba(255, 255, 255, .08);--vscode-commandCenter-border: rgba(204, 204, 204, .2);--vscode-commandCenter-activeBorder: rgba(204, 204, 204, .3);--vscode-commandCenter-inactiveBorder: rgba(204, 204, 204, .15);--vscode-notificationCenter-border: #303031;--vscode-notificationToast-border: #303031;--vscode-notifications-foreground: #cccccc;--vscode-notifications-background: #252526;--vscode-notificationLink-foreground: #3794ff;--vscode-notificationCenterHeader-background: #303031;--vscode-notifications-border: #303031;--vscode-notificationsErrorIcon-foreground: #f14c4c;--vscode-notificationsWarningIcon-foreground: #cca700;--vscode-notificationsInfoIcon-foreground: #3794ff;--vscode-inlineChat-foreground: #cccccc;--vscode-inlineChat-background: #252526;--vscode-inlineChat-border: #454545;--vscode-inlineChat-shadow: rgba(0, 0, 0, .36);--vscode-inlineChatInput-border: #454545;--vscode-inlineChatInput-focusBorder: #007fd4;--vscode-inlineChatInput-placeholderForeground: #a6a6a6;--vscode-inlineChatInput-background: #3c3c3c;--vscode-inlineChatDiff-inserted: rgba(156, 204, 44, .1);--vscode-editorOverviewRuler-inlineChatInserted: rgba(156, 204, 44, .12);--vscode-inlineChatDiff-removed: rgba(255, 0, 0, .1);--vscode-editorOverviewRuler-inlineChatRemoved: rgba(255, 0, 0, .12);--vscode-extensionButton-background: #0e639c;--vscode-extensionButton-foreground: #ffffff;--vscode-extensionButton-hoverBackground: #1177bb;--vscode-extensionButton-separator: rgba(255, 255, 255, .4);--vscode-extensionButton-prominentBackground: #0e639c;--vscode-extensionButton-prominentForeground: #ffffff;--vscode-extensionButton-prominentHoverBackground: #1177bb;--vscode-chat-requestBorder: rgba(255, 255, 255, .1);--vscode-chat-requestBackground: rgba(30, 30, 30, .62);--vscode-chat-slashCommandBackground: rgba(52, 65, 75, .56);--vscode-chat-slashCommandForeground: #40a6ff;--vscode-chat-avatarBackground: #1f1f1f;--vscode-chat-avatarForeground: #cccccc;--vscode-chat-editedFileForeground: #e2c08d;--vscode-terminal-foreground: #cccccc;--vscode-terminal-selectionBackground: #264f78;--vscode-terminal-inactiveSelectionBackground: #3a3d41;--vscode-terminalCommandDecoration-defaultBackground: rgba(255, 255, 255, .25);--vscode-terminalCommandDecoration-successBackground: #1b81a8;--vscode-terminalCommandDecoration-errorBackground: #f14c4c;--vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, .8);--vscode-terminal-border: rgba(128, 128, 128, .35);--vscode-terminalOverviewRuler-border: rgba(127, 127, 127, .3);--vscode-terminal-findMatchBackground: #515c6a;--vscode-terminal-hoverHighlightBackground: rgba(38, 79, 120, .13);--vscode-terminal-findMatchHighlightBackground: rgba(234, 92, 0, .33);--vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, .49);--vscode-terminal-dropBackground: rgba(83, 89, 93, .5);--vscode-terminal-initialHintForeground: rgba(255, 255, 255, .34);--vscode-terminalStickyScrollHover-background: #2a2d2e;--vscode-scmGraph-historyItemRefColor: #3794ff;--vscode-scmGraph-historyItemRemoteRefColor: #b180d7;--vscode-scmGraph-historyItemBaseRefColor: #ea5c00;--vscode-scmGraph-historyItemHoverDefaultLabelForeground: #cccccc;--vscode-scmGraph-historyItemHoverDefaultLabelBackground: #4d4d4d;--vscode-scmGraph-historyItemHoverLabelForeground: #ffffff;--vscode-scmGraph-historyItemHoverAdditionsForeground: #81b88b;--vscode-scmGraph-historyItemHoverDeletionsForeground: #c74e39;--vscode-scmGraph-foreground1: #ffb000;--vscode-scmGraph-foreground2: #dc267f;--vscode-scmGraph-foreground3: #994f00;--vscode-scmGraph-foreground4: #40b0a6;--vscode-scmGraph-foreground5: #b66dff;--vscode-commentsView-resolvedIcon: rgba(204, 204, 204, .5);--vscode-commentsView-unresolvedIcon: #007fd4;--vscode-editorCommentsWidget-replyInputBackground: #252526;--vscode-editorCommentsWidget-resolvedBorder: rgba(204, 204, 204, .5);--vscode-editorCommentsWidget-unresolvedBorder: #007fd4;--vscode-editorCommentsWidget-rangeBackground: rgba(0, 127, 212, .1);--vscode-editorCommentsWidget-rangeActiveBackground: rgba(0, 127, 212, .1);--vscode-editorGutter-commentRangeForeground: #37373d;--vscode-editorOverviewRuler-commentForeground: #37373d;--vscode-editorOverviewRuler-commentUnresolvedForeground: #37373d;--vscode-editorGutter-commentGlyphForeground: #d4d4d4;--vscode-editorGutter-commentUnresolvedGlyphForeground: #d4d4d4;--vscode-activityWarningBadge-foreground: #000000;--vscode-activityWarningBadge-background: #cca700;--vscode-activityErrorBadge-foreground: #000000;--vscode-activityErrorBadge-background: #f14c4c;--vscode-ports-iconRunningProcessForeground: #369432;--vscode-editorWatermark-foreground: rgba(212, 212, 212, .6);--vscode-settings-headerForeground: #e7e7e7;--vscode-settings-settingsHeaderHoverForeground: rgba(231, 231, 231, .7);--vscode-settings-modifiedItemIndicator: #0c7d9d;--vscode-settings-headerBorder: rgba(128, 128, 128, .35);--vscode-settings-sashBorder: rgba(128, 128, 128, .35);--vscode-settings-dropdownBackground: #3c3c3c;--vscode-settings-dropdownForeground: #f0f0f0;--vscode-settings-dropdownBorder: #3c3c3c;--vscode-settings-dropdownListBorder: #454545;--vscode-settings-checkboxBackground: #3c3c3c;--vscode-settings-checkboxForeground: #f0f0f0;--vscode-settings-checkboxBorder: #6b6b6b;--vscode-settings-textInputBackground: #3c3c3c;--vscode-settings-textInputForeground: #cccccc;--vscode-settings-numberInputBackground: #3c3c3c;--vscode-settings-numberInputForeground: #cccccc;--vscode-settings-focusedRowBackground: rgba(42, 45, 46, .6);--vscode-settings-rowHoverBackground: rgba(42, 45, 46, .3);--vscode-settings-focusedRowBorder: #007fd4;--vscode-keybindingTable-headerBackground: rgba(204, 204, 204, .04);--vscode-keybindingTable-rowsBackground: rgba(204, 204, 204, .04);--vscode-debugToolBar-background: #333333;--vscode-debugIcon-startForeground: #89d185;--vscode-notebook-cellBorderColor: #37373d;--vscode-notebook-focusedEditorBorder: #007fd4;--vscode-notebookStatusSuccessIcon-foreground: #89d185;--vscode-notebookEditorOverviewRuler-runningCellForeground: #89d185;--vscode-notebookStatusErrorIcon-foreground: #f48771;--vscode-notebookStatusRunningIcon-foreground: #cccccc;--vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, .35);--vscode-notebook-selectedCellBackground: #37373d;--vscode-notebook-selectedCellBorder: #37373d;--vscode-notebook-focusedCellBorder: #007fd4;--vscode-notebook-inactiveFocusedCellBorder: #37373d;--vscode-notebook-cellStatusBarItemHoverBackground: rgba(255, 255, 255, .15);--vscode-notebook-cellInsertionIndicator: #007fd4;--vscode-notebookScrollbarSlider-background: rgba(121, 121, 121, .4);--vscode-notebookScrollbarSlider-hoverBackground: rgba(100, 100, 100, .7);--vscode-notebookScrollbarSlider-activeBackground: rgba(191, 191, 191, .4);--vscode-notebook-symbolHighlightBackground: rgba(255, 255, 255, .04);--vscode-notebook-cellEditorBackground: #252526;--vscode-notebook-editorBackground: #1e1e1e;--vscode-debugIcon-breakpointForeground: #e51400;--vscode-debugIcon-breakpointDisabledForeground: #848484;--vscode-debugIcon-breakpointUnverifiedForeground: #848484;--vscode-debugIcon-breakpointCurrentStackframeForeground: #ffcc00;--vscode-debugIcon-breakpointStackframeForeground: #89d185;--vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 0, .2);--vscode-editor-focusedStackFrameHighlightBackground: rgba(122, 189, 122, .3);--vscode-multiDiffEditor-headerBackground: #262626;--vscode-multiDiffEditor-background: #1e1e1e;--vscode-multiDiffEditor-border: rgba(204, 204, 204, .2);--vscode-editorGutter-modifiedBackground: #1b81a8;--vscode-editorGutter-addedBackground: #487e02;--vscode-editorGutter-deletedBackground: #f14c4c;--vscode-minimapGutter-modifiedBackground: #1b81a8;--vscode-minimapGutter-addedBackground: #487e02;--vscode-minimapGutter-deletedBackground: #f14c4c;--vscode-editorOverviewRuler-modifiedForeground: rgba(27, 129, 168, .6);--vscode-editorOverviewRuler-addedForeground: rgba(72, 126, 2, .6);--vscode-editorOverviewRuler-deletedForeground: rgba(241, 76, 76, .6);--vscode-interactive-activeCodeBorder: #007acc;--vscode-interactive-inactiveCodeBorder: #37373d;--vscode-testing-iconFailed: #f14c4c;--vscode-testing-iconErrored: #f14c4c;--vscode-testing-iconPassed: #73c991;--vscode-testing-runAction: #73c991;--vscode-testing-iconQueued: #cca700;--vscode-testing-iconUnset: #848484;--vscode-testing-iconSkipped: #848484;--vscode-testing-peekBorder: #f14c4c;--vscode-testing-messagePeekBorder: #3794ff;--vscode-testing-peekHeaderBackground: rgba(241, 76, 76, .1);--vscode-testing-messagePeekHeaderBackground: rgba(55, 148, 255, .1);--vscode-testing-coveredBackground: rgba(156, 204, 44, .2);--vscode-testing-coveredBorder: rgba(156, 204, 44, .15);--vscode-testing-coveredGutterBackground: rgba(156, 204, 44, .12);--vscode-testing-uncoveredBranchBackground: #781212;--vscode-testing-uncoveredBackground: rgba(255, 0, 0, .2);--vscode-testing-uncoveredBorder: rgba(255, 0, 0, .15);--vscode-testing-uncoveredGutterBackground: rgba(255, 0, 0, .3);--vscode-testing-coverCountBadgeBackground: #4d4d4d;--vscode-testing-coverCountBadgeForeground: #ffffff;--vscode-testing-message\.error\.decorationForeground: #f14c4c;--vscode-testing-message\.error\.lineBackground: rgba(255, 0, 0, .1);--vscode-testing-message\.info\.decorationForeground: rgba(212, 212, 212, .5);--vscode-testing-iconErrored\.retired: rgba(241, 76, 76, .7);--vscode-testing-iconFailed\.retired: rgba(241, 76, 76, .7);--vscode-testing-iconPassed\.retired: rgba(115, 201, 145, .7);--vscode-testing-iconQueued\.retired: rgba(204, 167, 0, .7);--vscode-testing-iconUnset\.retired: rgba(132, 132, 132, .7);--vscode-testing-iconSkipped\.retired: rgba(132, 132, 132, .7);--vscode-debugExceptionWidget-border: #a31515;--vscode-debugExceptionWidget-background: #420b0d;--vscode-editor-inlineValuesForeground: rgba(255, 255, 255, .5);--vscode-editor-inlineValuesBackground: rgba(255, 200, 0, .2);--vscode-statusBar-debuggingBackground: #cc6633;--vscode-statusBar-debuggingForeground: #ffffff;--vscode-commandCenter-debuggingBackground: rgba(204, 102, 51, .26);--vscode-debugTokenExpression-name: #c586c0;--vscode-debugTokenExpression-type: #4a90e2;--vscode-debugTokenExpression-value: rgba(204, 204, 204, .6);--vscode-debugTokenExpression-string: #ce9178;--vscode-debugTokenExpression-boolean: #4e94ce;--vscode-debugTokenExpression-number: #b5cea8;--vscode-debugTokenExpression-error: #f48771;--vscode-debugView-exceptionLabelForeground: #cccccc;--vscode-debugView-exceptionLabelBackground: #6c2022;--vscode-debugView-stateLabelForeground: #cccccc;--vscode-debugView-stateLabelBackground: rgba(136, 136, 136, .27);--vscode-debugView-valueChangedHighlight: #569cd6;--vscode-debugConsole-infoForeground: #3794ff;--vscode-debugConsole-warningForeground: #cca700;--vscode-debugConsole-errorForeground: #f48771;--vscode-debugConsole-sourceForeground: #cccccc;--vscode-debugConsoleInputIcon-foreground: #cccccc;--vscode-debugIcon-pauseForeground: #75beff;--vscode-debugIcon-stopForeground: #f48771;--vscode-debugIcon-disconnectForeground: #f48771;--vscode-debugIcon-restartForeground: #89d185;--vscode-debugIcon-stepOverForeground: #75beff;--vscode-debugIcon-stepIntoForeground: #75beff;--vscode-debugIcon-stepOutForeground: #75beff;--vscode-debugIcon-continueForeground: #75beff;--vscode-debugIcon-stepBackForeground: #75beff;--vscode-mergeEditor-change\.background: rgba(155, 185, 85, .2);--vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, .2);--vscode-mergeEditor-changeBase\.background: #4b1818;--vscode-mergeEditor-changeBase\.word\.background: #6f1313;--vscode-mergeEditor-conflict\.unhandledUnfocused\.border: rgba(255, 166, 0, .48);--vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;--vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, .29);--vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, .8);--vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, .93);--vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;--vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, .28);--vscode-mergeEditor-conflict\.input1\.background: rgba(64, 200, 174, .2);--vscode-mergeEditor-conflict\.input2\.background: rgba(64, 166, 255, .2);--vscode-extensionIcon-starForeground: #ff8e00;--vscode-extensionIcon-verifiedForeground: #3794ff;--vscode-extensionIcon-preReleaseForeground: #1d9271;--vscode-extensionIcon-sponsorForeground: #d758b3;--vscode-terminal-ansiBlack: #000000;--vscode-terminal-ansiRed: #cd3131;--vscode-terminal-ansiGreen: #0dbc79;--vscode-terminal-ansiYellow: #e5e510;--vscode-terminal-ansiBlue: #2472c8;--vscode-terminal-ansiMagenta: #bc3fbc;--vscode-terminal-ansiCyan: #11a8cd;--vscode-terminal-ansiWhite: #e5e5e5;--vscode-terminal-ansiBrightBlack: #666666;--vscode-terminal-ansiBrightRed: #f14c4c;--vscode-terminal-ansiBrightGreen: #23d18b;--vscode-terminal-ansiBrightYellow: #f5f543;--vscode-terminal-ansiBrightBlue: #3b8eea;--vscode-terminal-ansiBrightMagenta: #d670d6;--vscode-terminal-ansiBrightCyan: #29b8db;--vscode-terminal-ansiBrightWhite: #e5e5e5;--vscode-simpleFindWidget-sashBorder: #454545;--vscode-terminalCommandGuide-foreground: #37373d;--vscode-welcomePage-tileBackground: #252526;--vscode-welcomePage-tileHoverBackground: #2c2c2d;--vscode-welcomePage-tileBorder: rgba(255, 255, 255, .1);--vscode-welcomePage-progress\.background: #3c3c3c;--vscode-welcomePage-progress\.foreground: #3794ff;--vscode-walkthrough-stepTitle\.foreground: #ffffff;--vscode-walkThrough-embeddedEditorBackground: rgba(0, 0, 0, .4);--vscode-profiles-sashBorder: rgba(128, 128, 128, .35);--vscode-gitDecoration-addedResourceForeground: #81b88b;--vscode-gitDecoration-modifiedResourceForeground: #e2c08d;--vscode-gitDecoration-deletedResourceForeground: #c74e39;--vscode-gitDecoration-renamedResourceForeground: #73c991;--vscode-gitDecoration-untrackedResourceForeground: #73c991;--vscode-gitDecoration-ignoredResourceForeground: #8c8c8c;--vscode-gitDecoration-stageModifiedResourceForeground: #e2c08d;--vscode-gitDecoration-stageDeletedResourceForeground: #c74e39;--vscode-gitDecoration-conflictingResourceForeground: #e4676b;--vscode-gitDecoration-submoduleResourceForeground: #8db9e2;--vscode-dart-closingLabels: rgba(255, 255, 255, .5);--vscode-dart-flutterUiGuides: #a3a3a3;overscroll-behavior-x:none;background-color:transparent;color:var(--vscode-editor-foreground);font-family:var(--vscode-font-family);font-weight:var(--vscode-font-weight);font-size:var(--vscode-font-size)}
