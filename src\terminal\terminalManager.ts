import * as vscode from 'vscode';
import { TerminalParams } from '../codechat/types';
import { TerminalEventType } from '../codechat/types';


export class TerminalManager {
  private terminal: vscode.Terminal | undefined;
  private actionMap = new Map<TerminalEventType, (params: TerminalParams) => void>();

  constructor() {
    this.registerHandlers();
  }

  private registerHandlers(): void {
    this.actionMap.set(
      TerminalEventType.EXECUTE_COMMAND,
      (params) => { 
        this.executeCommand(params.command)
      }
    );
  }

  // 方法1：直接判断
  private isTerminalExists(): boolean {
    return this.terminal !== undefined &&
      vscode.window.terminals.includes(this.terminal);
  }


  //create terminal
  private getTerminal(): vscode.Terminal | undefined {
    if (!this.isTerminalExists()) {
      this.terminal = vscode.window.createTerminal();
    }
    return this.terminal;
  }

  //execute command
  public executeCommand(command: string) {
    const terminal = this.getTerminal();
    if (terminal) { 
      terminal.show();
      terminal.sendText(command);
    }
  }

  //run npm script
  public runNpmScript(scriptName: string) {
    this.executeCommand(`npm run ${scriptName}`);
  }

  public handleTerminalInvocation(request: TerminalParams) { 
    const handler = this.actionMap.get(request.reqType as TerminalEventType);
    if (handler) {
      handler(request);
    }
  }

  //clean resource
  public dispose() {
    if (this.terminal) {
      this.terminal.dispose();
      this.terminal = undefined;
    }
  }
}

