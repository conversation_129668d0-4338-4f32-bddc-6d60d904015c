import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { extract } from 'zip-lib';
import { Logger } from '../../utils/logger';
import * as crypto from 'crypto';

export class AgentDownloader {
  private readonly maxRetries: number;
  private readonly timeout: number;
  private readonly concurrentDownloads: number;
  private activeDownloads: number = 0;
  
  public constructor(maxRetries = 3, timeout = 120000, concurrentDownloads = 2) {
    this.maxRetries = maxRetries;
    this.timeout = timeout; // 2 minutes timeout
    this.concurrentDownloads = concurrentDownloads;
  }

  /**
   * 从指定URL下载Agent
   * @param downloadUrl 下载地址
   * @param agentName Agent名称
   * @param targetPath 目标路径
   * @param expectedMd5 期望的MD5值，用于验证文件完整性
   * @returns 是否下载成功
   */
  public async downloadAgent(
    downloadUrl: string,
    agentName: string,
    targetPath: string,
    expectedMd5?: string
  ): Promise<boolean> {
    let retries = 0;

    // 等待如果已达到最大并发下载数
    while (this.activeDownloads >= this.concurrentDownloads) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.activeDownloads++;
    
    try {
      while (retries < this.maxRetries) {
        try {
          // 确保目标目录存在
          if (!fs.existsSync(targetPath)) {
            fs.mkdirSync(targetPath, { recursive: true, mode: 0o755 });
          }

          // 下载文件
          const zipPath = path.join(targetPath, `${agentName}.zip`);
          const response = await axios({
            method: 'get',
            url: downloadUrl,
            responseType: 'arraybuffer',
            timeout: this.timeout, // 两分钟
            headers: {
              'Connection': 'keep-alive',
              'Keep-Alive': 'timeout=120',
            },
          });

        await fs.promises.writeFile(zipPath, response.data);
        // 暂时注释掉MD5校验的代码，之后再用
        
        if (expectedMd5) {
          const fileBuffer = await fs.promises.readFile(zipPath);
          const actualMd5 = crypto.createHash('md5').update(fileBuffer).digest('hex');

          if (actualMd5.toLowerCase() !== expectedMd5.toLowerCase()) {
            Logger.error(`[AgentDownloader] ZIP文件MD5校验失败: 期望=${expectedMd5}, 实际=${actualMd5}`);
            // 删除已下载的文件
            await fs.promises.unlink(zipPath);
            throw new Error('文件完整性验证失败，MD5不匹配');
          }

          Logger.debug(`[AgentDownloader] ZIP文件MD5校验通过: ${actualMd5}`);
        } else {
          Logger.warn(`[AgentDownloader] 未提供MD5值，跳过文件完整性验证`);
        }
        

        // if (expectedMd5) {
        //   Logger.warn(`[AgentDownloader] MD5校验功能已暂时禁用`);
        // }

          // 解压文件
          await extract(zipPath, targetPath);

          // 删除zip文件
          await fs.promises.unlink(zipPath);

          return true;
        } catch (error) {
          retries++;
          const waitTime = Math.pow(2, retries) * 1000; // 指数退避策略
          Logger.error(`[AgentDownloader] 下载失败(重试 ${retries}/${this.maxRetries}): ${error}`);

          if (retries === this.maxRetries) {
            throw new Error(`下载Agent ${agentName} 失败，已重试 ${this.maxRetries} 次`);
          }

          // 等待更长时间后重试
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      return false;
    } finally {
      this.activeDownloads--;
    }
  }
}
