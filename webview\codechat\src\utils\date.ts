// 获取当前时间
export function getCurrentTime() {
  const date = new Date();
  return formatDate(date);
}

// 格式化时间
export function formatDate(date: Date, format = 'yyyy-MM-dd hh:mm:ss') {
  const o = {
    'M+': date.getMonth() + 1, //月份
    'd+': date.getDate(), //日
    'h+': date.getHours(), //小时
    'm+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
    'q+': Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };

  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }

  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      const val = (o as any)[k];
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? val : ('00' + val).substr(('' + val).length)
      );
    }
  }

  return format;
}
