import HttpClient from '../commclient/httpClient';
import {
  OAUTH2_REDIRECT_PATH,
  OAUTH2_CODE_URI,
  OAUTH2_AUTH_PATH,
  OAUTH2_TOKEN_PATH,
  USER_INFO_PATH,
} from '../common/config';
import * as vscode from 'vscode';
import { Logger } from '../utils/logger';
import { HttpError, IHttpResponseHandler } from '../commclient/types/http';
import {
  getCurrentUser,
  getGlobalContext,
  setCurrentUser,
  setCodeAIConfig,
  clearCurrentUser,
  setAgentVersion,
} from '../common/globalContext';
import {
  HttpStatusCode,
  LoginStatus,
  RtnCode,
  SecretStorageKey,
  SrdCommand,
} from '../common/constants';
import { parseUriQuery } from '../utils/common';
import {
  IServiceObserver,
  EventType,
  IServiceObservable,
  LoginUserInfo,
  AuthResponse,
  UInfo,
} from './types/login';
import CodeAIRequestSender from './codeAIRequestSender';
import { CodeAIResponseConfig, CodeAIResponsePayload, ICodeAIReceiverResult } from './types/codeAI';
import CodeAIRequestReceiver from './codeAIRequestReceiver';
import { once, EventEmitter } from 'events';
import VersionChecker from './versionChecker';
import { isRunInCloudIDE } from '../adapter/common';
import { agentConfig } from './types/codeAI';
import { AgentManager } from '../agent/agentmanager';
import { getHttpServerHost } from '../utils/envUtil';
import {
  userInfoManager,
  UserInfo as SecideaUserInfo,
  LoginStatus as SecideaLoginStatus,
} from '../login/loginUtils';

enum LoginState {
  NotLogin = 0,
  WaitingCode = 1,
  WaitingAccessToken = 2,
  WaitingUserInfo = 3,
  WaitingCodeAIRegister = 4,
  WaitingApiKey = 5,
  Logined = 6,
  WaitingDisconnect = 7,
}

const PROGRESS_LOGIN_END = 'progress_login_end';

/**
 * 登录服务，登录流程如下：
 *
 * 1.调起浏览器发起oauth2认证获取code
 * 2.通过调用vscode.UriHandler传回code，获取用户userId, sessionId, access_token;
 * 3.获取用户信息，如userAccount
 * 4.发起WS注册
 * 5.通过WS通道获取apiKey，clientLatestVersion，config等，此时登录成功
 * 6.登录成功后，检测插件是否需要更新
 */
class LoginService
  implements IHttpResponseHandler, IServiceObservable, ICodeAIReceiverResult, vscode.Disposable
{
  private httpClient: HttpClient = new HttpClient(this);

  private currentState: LoginState = LoginState.NotLogin;

  private code: string | undefined;

  private userId: string | undefined;

  private sessionId: string | undefined;

  private apiKey: string | undefined;

  private userAccount: string | undefined;

  private config: CodeAIResponseConfig | undefined;

  private agentVersion: agentConfig[] | undefined;

  private hasOpened = false;

  private hasRegistered = false;

  private loginObservers: IServiceObserver[] = [];

  private onDidLogin = new EventEmitter();

  private versionChecker = new VersionChecker();

  private disposable: vscode.Disposable | undefined;

  public constructor() {
    CodeAIRequestReceiver.setReceiverResult(this);
  }

  public initDisposable() {
    if (!isRunInCloudIDE()) {
      this.disposable = vscode.Disposable.from(this.onSecretsChange(), this.registerThemeChange());
    }
  }

  public dispose() {
    this.disposable?.dispose();
  }

  /**
   * 注册观察者
   * @param observer
   */
  public registerObserver(observer: IServiceObserver): void {
    this.loginObservers.push(observer);
  }

  private registerThemeChange(): vscode.Disposable {
    return vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration('workbench.colorTheme')) {
        const changedTheme = vscode.workspace.getConfiguration().get('workbench.colorTheme');
        this.fireServiceChanged(EventType.THEME_CHANGED, changedTheme);
        // 根据主题变化更新UI或其他逻辑
      }
    });
  }

  /**
   * 取消注册观察者
   * @param observer
   */
  public unregisterObserver(observer: IServiceObserver): void {
    const index = this.loginObservers.indexOf(observer);
    this.loginObservers.splice(index, 1);
  }

  /**
   * 通知事件变更
   * @param eventType
   * @param data
   */
  public fireServiceChanged(eventType: EventType, data?: unknown): void {
    this.loginObservers.forEach(async observer => {
      await observer.onServiceChanged(eventType, data);
    });
  }

  /**
   * 获取对外的登录状态
   * @returns
   */
  public getLoginStatus() {
    if (process.env.ISSEC !== 'false') {
      return userInfoManager.getLoginStatusForC10();
    }
    if (this.currentState === LoginState.Logined) {
      return LoginStatus.OK;
    }

    return LoginStatus.NOT_OK;
  }

  /**
   * http请求错误处理
   * @param error
   */
  public handleResponseError(error: HttpError): void {
    switch (this.currentState) {
      case LoginState.WaitingAccessToken:
        this.handleEvent(EventType.LOGIN_FAILED, RtnCode.OAUTH2_ERROR);
        break;
      case LoginState.WaitingUserInfo:
        if (
          error.status === HttpStatusCode.UNAUTHORIZED ||
          error.status === HttpStatusCode.NOT_ACCEPTABLE
        ) {
          this.handleEvent(EventType.LOGIN_EXPIRED, RtnCode.INVALID_SESSION_ID);
        } else {
          this.handleEvent(EventType.LOGIN_FAILED, RtnCode.OAUTH2_ERROR);
        }
        break;
      default:
        break;
    }
  }

  /**
   * 处理注册通道响应
   * @param code
   */
  public async onRegisterChannelResult(code: number) {
    if (code === RtnCode.SUCCESS) {
      Logger.info(`[LoginService] register channel success`);
      this.handleEvent(EventType.CHANNEL_REGISTERED, code);
    } else if (code === HttpStatusCode.UNAUTHORIZED || code === HttpStatusCode.AUTH_FAIL) {
      Logger.info(`[LoginService] register channel unauthorized`);
      await this.handleEvent(EventType.LOGIN_EXPIRED, RtnCode.INVALID_SESSION_ID);
    } else {
      Logger.info(`[LoginService] register channel failed`);
      await this.handleEvent(EventType.LOGIN_FAILED, RtnCode.CONNECTED_ERROR);
    }
  }

  /**
   * 处理获取用户ApiKey响应
   * @param code
   */
  public async onGetApiKeyResult(code: number, payload?: CodeAIResponsePayload) {
    if (code === RtnCode.SUCCESS && payload?.apiKey) {
      Logger.info(`[LoginService] received apiKey, login success`);
      await this.handleEvent(EventType.LOGIN_SUCCESS, payload);
    } else if (code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN) {
      Logger.info(`[LoginService] received apiKey failed, code:${code}, login failed`);
      await this.handleEvent(EventType.LOGIN_FAILED, code);
    } else if (code === RtnCode.INVALID_SESSION_ID) {
      Logger.info(`[LoginService] received apiKey failed, code:${code}, login expired`);
      await this.handleEvent(EventType.LOGIN_EXPIRED, code);
    } else {
      Logger.info(`[LoginService] received apiKey failed, code:${code}, login failed`);
      await this.handleEvent(EventType.LOGIN_FAILED, code);
    }
  }

  public async onWSReconnect(isTerminate: boolean, code: number) {
    this.fireServiceChanged(EventType.WSSERVER_RECONNECT, code);
  }

  /**
   * 处理通道异常
   * @param isTerminate 通道是否被终止
   */
  public async onTaskErrorOrClose(isTerminate: boolean, code: number) {
    Logger.error(`[LoginService] onTaskErrorOrClose, code:${code}, isTerminate: ${isTerminate}`);

    // 1) 收到通道响应(登录过期//在海云安侧为账号未授权)：过期处理
    if (!isTerminate && code === RtnCode.INVALID_SESSION_ID) {
      this.handleEvent(EventType.LOGIN_EXPIRED, RtnCode.INVALID_SESSION_ID);
    } else if (
      !isTerminate &&
      code === RtnCode.NO_CHANNEL &&
      this.currentState === LoginState.Logined
    ) {
      // 2) 收到通道断开（保持重连)：只变更状态栏
      this.fireServiceChanged(EventType.WSSERVER_ERROR, code);
    } else if (
      !isTerminate &&
      code === RtnCode.NOT_LOGIN &&
      this.currentState === LoginState.Logined
    ) {
      // 3) 发送消息（本地信息被清除,状态栏已变更）：按过期处理
      this.handleEvent(EventType.LOGIN_EXPIRED, RtnCode.INVALID_SESSION_ID);
    } else if (isTerminate && this.currentState === LoginState.WaitingDisconnect) {
      // 4) 主动登出后，收到通道断开（不再重连）：只需修改LoginState
      this.switchState(LoginState.NotLogin);
    } else if (isTerminate && this.currentState !== LoginState.WaitingDisconnect) {
      // 5) 通道主动断开（不再重连）: 修改LoginState;清除用户信息;变更状态栏;
      if (this.currentState !== LoginState.Logined) {
        this.onDidLogin.emit(PROGRESS_LOGIN_END);
      }

      // 先变更状态，再清用户信息
      this.switchState(LoginState.NotLogin);
      await this.clearLoginInfo();
      this.fireServiceChanged(EventType.LOGIN_FAILED, RtnCode.NO_CHANNEL);
    }
  }

  /**
   * 登录过程的事件处理
   * @param eventType
   * @param data
   */
  public async handleEvent(eventType: EventType, data: unknown = {}) {
    switch (eventType) {
      case EventType.LOGIN:
        this.login();
        break;
      case EventType.BROWSER_OPENED:
        if (this.currentState === LoginState.NotLogin) {
          this.hasOpened = true;
          this.switchState(LoginState.WaitingCode);
        }
        break;
      // 接收浏览器返回的code
      case EventType.CODE_RECEIVED:
        if (this.currentState === LoginState.WaitingCode) {
          this.code = data as string;
          Logger.debug(`[LoginService] received code: ${this.code}`);
          this.getAccessTokenByCode();
          this.switchState(LoginState.WaitingAccessToken);
        }
        break;
      case EventType.ACCESS_TOKEN_RECEIVED:
        if (this.currentState === LoginState.WaitingAccessToken) {
          const result = data as AuthResponse;
          this.userId = result.uid;
          this.sessionId = result.ori_session_id;
          await this.setCurrentUserToStore();
          Logger.debug(
            `[LoginService] received userId: ${this.userId}, sessionId:${this.sessionId}`
          );
          this.getUserInfo();
          this.switchState(LoginState.WaitingUserInfo);
        }
        break;
      case EventType.USER_INFO_RECEIVED:
        if (this.currentState === LoginState.WaitingUserInfo) {
          this.userAccount = (data as LoginUserInfo).userAccount;
          Logger.debug(`[LoginService] received userAccount: ${this.userAccount}`);
          this.registerToCodeAI();
          this.switchState(LoginState.WaitingCodeAIRegister);
        } else if (process.env.ISSEC !== 'false') {
          const userInfo = data as SecideaUserInfo;
          if (data === null) {
            this.handleEvent(EventType.LOGIN_FAILED, RtnCode.INVALID_SESSION_ID);
          }
          this.userAccount = userInfo.userName;
          this.userId = userInfo.userId;
          this.sessionId = data as string;
          Logger.debug(
            `[LoginService] received userId: ${this.userId}, sessionId:${this.sessionId},userAccount: ${this.userAccount}`
          );
          await this.setCurrentUserToStore();
          this.registerToCodeAI();
          this.switchState(LoginState.WaitingCodeAIRegister);
        }
        break;
      case EventType.CHANNEL_REGISTERED:
        if (this.currentState === LoginState.WaitingCodeAIRegister) {
          this.hasRegistered = true;
          this.getApiKey();
          this.switchState(LoginState.WaitingApiKey);
        }
        break;
      case EventType.LOGIN_SUCCESS:
        if (this.currentState === LoginState.WaitingApiKey) {
          const payload = data as CodeAIResponsePayload;
          this.apiKey = payload.apiKey;
          this.config = payload.config as CodeAIResponseConfig;
          this.agentVersion = payload.agentVersion;
          await setCodeAIConfig(this.config);
          await setAgentVersion(this.agentVersion);
          await this.setCurrentUserToStore(true);
          this.switchState(LoginState.Logined);
          const latestVersionDesc = payload.clientLatestVersionContent;
          const versionDesc = payload.clientVersionContent;

          this.onDidLogin.emit(PROGRESS_LOGIN_END);
          // this.fireServiceChanged(eventType, this.getUInfo());
          this.fireServiceChanged(eventType, {
            ...this.getUInfo(),
            config: this.config,
            versionDesc,
          });
          this.checkVersion(
            payload.clientLatestVersion,
            latestVersionDesc,
            payload.clientLatestVersionDownloadUrl
          );
        }
        break;
      // 清除此前登录流程，从头开始登录流程
      case EventType.LOGIN_CANCELED:
      case EventType.LOGIN_FAILED:
      case EventType.LOGIN_EXPIRED:
        this.onDidLogin.emit(PROGRESS_LOGIN_END);
        await this.clearAndDisconnect();
        this.fireServiceChanged(eventType, data);
        userInfoManager.logout();
        break;
      case EventType.LOGOUT:
        await this.clearAndDisconnect();
        AgentManager.getInstance().shutdown();
        this.fireServiceChanged(eventType, data);
        userInfoManager.logout();
        break;
      case EventType.QUESTION_NOT_LOGIN:
        this.onTaskErrorOrClose(false, data as number);
        break;
      default:
        break;
    }
  }

  /**
   * 临听SecretStorage变化，以同步多窗口状态栏的“登录状态”
   * @returns
   */
  private onSecretsChange(): vscode.Disposable {
    return getGlobalContext().secrets.onDidChange(async e => {
      if (e.key === SecretStorageKey.HAS_LOGINED) {
        const storeVal = await getGlobalContext().secrets.get(SecretStorageKey.HAS_LOGINED);
        const hasLogined = storeVal === 'true';

        if (this.currentState === LoginState.Logined && !hasLogined) {
          this.handleEvent(EventType.LOGIN_EXPIRED, RtnCode.INVALID_SESSION_ID);
        } else if (this.currentState < LoginState.Logined && hasLogined) {
          vscode.commands.executeCommand(SrdCommand.LOGIN);
        }
      }
    });
  }

  /**
   * 开始登录
   * @returns
   */
  private async login() {
    await this.initState();
    Logger.debug(`[LoginService] login, initState: ${this.currentState}`);

    if (this.currentState !== LoginState.Logined) {
      this.fireServiceChanged(EventType.LOGIN);
    }

    switch (this.currentState) {
      case LoginState.NotLogin:
        this.showLoginProgress();
        if (process.env.ISSEC !== 'false') {
          userInfoManager.login();
        } else {
          this.openOauth2Addr();
        }
        break;
      case LoginState.WaitingAccessToken:
        this.showLoginProgress();
        this.getAccessTokenByCode();
        break;
      case LoginState.WaitingUserInfo:
        this.showLoginProgress();
        this.getUserInfo();
        break;
      case LoginState.WaitingCodeAIRegister:
        this.registerToCodeAI();
        break;
      case LoginState.WaitingApiKey:
        this.getApiKey();
        break;
      case LoginState.Logined:
        break;
      default:
        break;
    }
  }

  /**
   * 切换登录状态
   * @param state
   */
  private switchState(state: LoginState) {
    this.currentState = state;
  }

  /**
   * 初始化当前登录状态
   */
  private async initState() {
    const currentUser = await getCurrentUser();

    // ！！注意：apiKey不从store中赋值，因为每次初始化实例时都需要建立WS链接
    this.userId = this.userId || currentUser.userId;
    this.sessionId = this.sessionId || currentUser.sessionId;
    this.userAccount = this.userAccount || currentUser.userAccount;

    if (this.apiKey && this.userAccount && this.userId && this.sessionId) {
      this.currentState = LoginState.Logined;
    } else if (this.userAccount && this.userId && this.sessionId && this.hasRegistered) {
      this.currentState = LoginState.WaitingApiKey;
    } else if (this.userAccount && this.userId && this.sessionId) {
      this.currentState = LoginState.WaitingCodeAIRegister;
    } else if (this.userId && this.sessionId) {
      this.currentState = LoginState.WaitingUserInfo;
    } else if (this.code && this.hasOpened) {
      this.currentState = LoginState.WaitingAccessToken;
    } else {
      this.currentState = LoginState.NotLogin;
    }
  }

  /**
   * 用浏览器打开oauth2认证地址
   */
  private async openOauth2Addr() {
    const host = getHttpServerHost();
    const path = host + OAUTH2_AUTH_PATH;
    const vscodeUriParsed = vscode.Uri.parse(`${vscode.env.uriScheme}://${OAUTH2_CODE_URI}`);
    let callbackUri = await vscode.env.asExternalUri(vscodeUriParsed);
    if (process.env.ISSEC !== 'false') {
      callbackUri = vscode.Uri.parse('http://localhost:61000/api/oauth-redirect');
    }

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.OAUTH2_CLIENT_ID as string,
      redirect_uri: host + OAUTH2_REDIRECT_PATH,
      state: Buffer.from(callbackUri.toString()).toString('base64'),
    });

    const uri = `${path}?${params.toString()}`;
    Logger.debug(`openOauth2Addr: ${uri}`);

    const success = await vscode.env.openExternal(vscode.Uri.parse(uri));
    if (success) {
      this.handleEvent(EventType.BROWSER_OPENED);
    }
  }

  /**
   * 通过code获取access_token, userId, sessionId
   */
  private async getAccessTokenByCode() {
    if (!this.code) {
      return;
    }

    const host = getHttpServerHost();
    const params: Record<string, string> = {
      grant_type: 'authorization_code',
      client_id: process.env.OAUTH2_CLIENT_ID as string,
      client_secret: process.env.OAUTH2_CLIENT_SECRET as string,
      code: this.code,
      redirect_uri: host + OAUTH2_REDIRECT_PATH,
    };

    const data = await this.httpClient.requestByGet<string>(OAUTH2_TOKEN_PATH, params);

    if (data && typeof data === 'string') {
      const result = parseUriQuery(data) as AuthResponse;
      await this.handleEvent(EventType.ACCESS_TOKEN_RECEIVED, result);
    } else if (process.env.ISSEC !== 'false') {
      return false;
    }
  }

  /**
   * 获取用户信息
   */
  private async getUserInfo() {
    if (!this.userId) {
      return;
    }

    const data = await this.httpClient.requestByGet<LoginUserInfo>(
      `${USER_INFO_PATH}/${this.userId}`,
      {},
      {
        headers: {
          contentType: 'application/json',
          projectId: '0',
        },
      }
    );

    if (data && data.userAccount) {
      this.handleEvent(EventType.USER_INFO_RECEIVED, {
        name: data.name,
        userAccount: data.userAccount,
      });
    }
  }

  /**
   * 发起通道注册
   */
  private async registerToCodeAI() {
    const sendMsgRtn = await CodeAIRequestSender.sendRegisterChannel();

    if (sendMsgRtn?.rtnCode !== RtnCode.SUCCESS) {
      this.handleEvent(EventType.LOGIN_FAILED, RtnCode.INVALID_SESSION_ID);
    }
  }

  /**
   * 获取用户ApiKey
   */
  private async getApiKey() {
    const sendMsgRtn = await CodeAIRequestSender.sendGetUserApiKey();

    if (sendMsgRtn?.rtnCode !== RtnCode.SUCCESS) {
      this.handleEvent(EventType.LOGIN_FAILED, RtnCode.INVALID_SESSION_ID);
    }
  }

  /**
   * 检查是否最新版本并提示
   */
  private async checkVersion(
    latestVersion?: string,
    versionDesc?: string,
    clientLatestVersionDownloadUrl?: string
  ) {
    const verResult = await this.versionChecker.checkIfUpgradeVersion(latestVersion);

    if (verResult.hasNewVersion) {
      vscode.commands.executeCommand(
        SrdCommand.UPGRADE_VERSION,
        verResult.latestVersion,
        versionDesc,
        clientLatestVersionDownloadUrl
      );
    }
  }

  /**
   * 退出登录，清除本地用户信息
   */
  private async clearAndDisconnect() {
    // 先去断开连接修改状态(已建立ws连接时)或修改到未登录状态; 再清用户信息;
    if (
      this.currentState === LoginState.WaitingCodeAIRegister ||
      this.currentState === LoginState.WaitingApiKey ||
      this.currentState === LoginState.Logined
    ) {
      this.disconnectChannel();
    } else {
      this.switchState(LoginState.NotLogin);
    }

    await this.clearLoginInfo();
  }

  /**
   * 关闭通道
   */
  private disconnectChannel() {
    CodeAIRequestSender.disconnectChannel();
    this.switchState(LoginState.WaitingDisconnect);
  }

  /**
   * 清除内存及本地储存登录信息
   */
  private async clearLoginInfo() {
    this.userId = undefined;
    this.sessionId = undefined;
    this.userAccount = undefined;
    this.apiKey = undefined;
    this.config = undefined;

    this.code = undefined;
    this.hasOpened = false;
    this.hasRegistered = false;

    await clearCurrentUser();
    await setCodeAIConfig(this.config);

    if (!isRunInCloudIDE()) {
      await getGlobalContext().secrets.delete(SecretStorageKey.HAS_LOGINED);
    }
  }

  /**
   * 保存当前用户信息至本地
   */
  private async setCurrentUserToStore(isSetLogined?: boolean) {
    const info: UInfo = this.getUInfo();

    await setCurrentUser(info);

    if (isSetLogined && !isRunInCloudIDE()) {
      await getGlobalContext().secrets.store(SecretStorageKey.HAS_LOGINED, 'true');
    }
  }

  /**
   * 从本地储存获取当前用户
   * @returns
   */
  private getUInfo(): UInfo {
    return {
      userId: this.userId,
      sessionId: this.sessionId,
      userAccount: this.userAccount,
      apiKey: this.apiKey,
    } as UInfo;
  }

  /**
   * 等待登录结束
   * @returns
   */
  private async waitingLoginEnd() {
    return await once(this.onDidLogin, PROGRESS_LOGIN_END);
  }

  /**
   *  显示登录进度条
   */
  private async showLoginProgress() {
    vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Notification,
        title: '正在登录认证中...',
        cancellable: true,
      },
      async (progress, token) => {
        token.onCancellationRequested(() => {
          this.handleEvent(EventType.LOGIN_CANCELED);
        });

        progress.report({});

        await this.waitingLoginEnd();

        return Promise.resolve();
      }
    );
  }
}

export const LoginServiceInst = new LoginService();
