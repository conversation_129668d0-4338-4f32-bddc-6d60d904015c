import { ChatMessageType, RtnCode, WebViewRspCode } from '@/constants/common';

export type Msg = {
  text: string;
  id?: string;
  role: 'user' | 'assistant' | 'system';
  createTime?: string;
  code?: WebViewRspCode;
  type?: ChatMessageType;
  reqId?: string;
  feedback?: string;
  errMsg?: string
};

export type Conv = {
  title?: string;
  id?: string;
  systemPrompt?: any;
  lastAnswer?: string;
  subService?: string;
  updateTime?: string;
  createTime?: string;
  questions?: any;
  isNewConversation?: boolean
};

export type ConvProps = {
  conversations: Conv[];
};

export interface ChatBodyProps {
  answeringText: string;
}

export interface ConvMsg {
  message: Msg;
}

export interface ConvLoadingMsg {
  reqId?: string;
  content: string;
  logo: boolean;
  silent?: boolean; // 是否沉默，不打印
}

export type ChatBodyData = {
  answeringText: string;
  answerCode?: RtnCode;
  answerIsEnd?: boolean;
  askQuestions?: string[];
  askType?: ChatMessageType;
  askText?: string;
  askInNewConversation?: boolean;
  reqId?: string;
  error?: string;
  inValid?: boolean;
};

export interface CodeInfo {
  codeStrMap: { [x in string]: string };
}

/* 事件总线 */
export type BusEvents = {
  closeHorizonPanel: string;
  openHorizonPanel: string;
  setFooterInputMessage: string;
};

export enum DirectiveType {
  ALL = '',
  SRDCHAT = 'srdchat',
  PROGRAM = 'program',
}

export type DirectiveTemplate = {
  title: string;
  id: string;
  type: DirectiveType;
  content: string;
};

export enum ActivityType {
  CODE_COMPLETION_ACCEPTED = 'code_accepted',
  CODE_CHAT_GEN = 'chat_gen_code',
  CODE_CHAT_ACCEPTED = 'chat_accepted_code',

  COMPOSER_GEN_CODE = 'composer_gen_code',
  COMPOSER_ACCEPTED_CODE = 'composer_accepted_code'
}
