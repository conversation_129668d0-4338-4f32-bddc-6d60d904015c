<template>
  <div class="code-bubble">
    <div class="message">
      <div class="message-text" @click="triggerMessageText" v-html="htmlStr"></div>
    </div>
    <div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onBeforeUnmount, ref, watch, reactive } from 'vue';
import MarkdownIt from 'markdown-it';
import markdownitAttrs from 'markdown-it-attrs'
import hljs from 'highlight.js';
import { Message } from '@arco-design/web-vue';
import { useChatStore } from '@/store/chat';
import { CodeInfo, Msg } from '@/types/index';
import { hashCode } from '@/utils';

const emit = defineEmits(['linePrint', 'finishPrint']);
const chatStore = useChatStore();
const codeInfo = reactive<CodeInfo>({
  codeStrMap: {},
});
/**
 * props传参
 */
const props = defineProps({
  content: {
    type: String
  },
  isComplete: {
    type: Boolean
  }
});

/**
 * md & highlight
 */
const mdi = new MarkdownIt({
  linkify: true,
  highlight(code: string, language: string) {
    const validLang = !!(language && hljs.getLanguage(language));
    const hashCodeVal = hashCode(code);
    codeInfo.codeStrMap[hashCode(code)] = code;
    if (validLang) {
      const lang = language ?? '';
      return highlightBlock(hljs.highlight(lang, code, true).value, lang, code, hashCodeVal);
    }
    return highlightBlock(hljs.highlightAuto(code).value, '', code, hashCodeVal);
  },
  html: true
});

function highlightBlock(str: string, lang: string, code: string, hashCodeVal: number) {
  const blocks = code.split(/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/);
  let decimalStr = '<div class="code-decimal-index">';
  blocks.forEach((item, itemIndex) => {
    decimalStr += `<div class="index-item">${itemIndex + 1}</div>`;
  });
  decimalStr += '</div>';
  return `
    <div class="pre-code-box">
      <div class="pre-code-header">
        <span class="code-block-header__lang">${lang}</span>
        ${props.isComplete ?
      `<div class="code-block-header__icons" data-code="${hashCodeVal}">
          <div tooltip="插入" class="code-block-header__icon">
            <svg class="insert-icon" data-action="insert" t="1705560031313" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="38910" width="200" height="200"><path d="M85.333333 490.666667a21.333333 21.333333 0 0 1 21.333334-21.333334L609.833333 469.33333299l-134.253333-134.24666599a21.333333 21.333333 0 1 1 30.173333-30.17333399l170.666667 170.66666699a21.333333 21.333333 0 0 1 0 30.173333l-170.666667 170.666667a21.333333 21.333333 0 0 1-30.173333-30.173333L609.833333 512 106.66666699 512a21.333333 21.333333 0 0 1-21.33333399-21.333333zM384 885.333333l0-138.666666a21.333333 21.333333 0 0 1 42.666667 0L426.666667 885.333333a10.666667 10.666667 0 0 0 10.666666 10.666667L885.333333 896a10.666667 10.666667 0 0 0 10.666667-10.666667l0-789.333333a10.666667 10.666667 0 0 0-10.666667-10.666667l-448 0a10.666667 10.666667 0 0 0-10.666666 10.666667l0 138.666667a21.333333 21.333333 0 0 1-42.666667 0l0-138.666667a53.393333 53.393333 0 0 1 53.333333-53.33333301L885.333333 42.666667a53.393333 53.393333 0 0 1 53.333334 53.333333L938.666667 885.333333a53.393333 53.393333 0 0 1-53.333334 53.333334l-448 0a53.393333 53.393333 0 0 1-53.333333-53.333334z" fill="currentColor" fill-opacity="1" p-id="38911"></path></svg>
          </div>
          <div tooltip="复制" class="code-block-header__icon">
            <svg class="copy-icon" data-action="copy" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 8.75830078125 10"><g><path d="M2.50489,7.4916L0.474489,7.4916C0.212436,7.4916,0,7.27916,0,7.01711L0,0.474489C0,0.212436,0.212436,0,0.474489,0L5.779,0C6.04105,0,6.25349,0.212436,6.25349,0.474489L6.25349,2.50482L8.28389,2.50482C8.54594,2.50482,8.75838,2.71726,8.75838,2.97931L8.75838,9.52551C8.75838,9.78755,8.54594,9.99998,8.28389,9.99999L2.97938,9.99999C2.71733,9.99999,2.5049,9.78756,2.50489,9.52551L2.50489,7.4916ZM2.50489,6.63351L1.20876,6.63351C1.01643,6.6339,0.85998,6.47873,0.858795,6.28641L0.858795,1.21663C0.858789,1.01901,1.019,0.858805,1.21663,0.858805L5.02683,0.858805C5.22999,0.8588,5.39469,1.02349,5.39469,1.22665L5.39469,2.50482L2.97937,2.50482C2.71732,2.50482,2.50488,2.71726,2.50488,2.97931L2.50489,2.98289L2.50489,6.63351ZM3.69146,9.13761L7.56249,9.13761C7.75221,9.13604,7.90473,8.98095,7.90314,8.79123L7.90314,3.72288C7.90315,3.52526,7.74294,3.36505,7.54532,3.36505L3.71006,3.36505C3.51956,3.36505,3.36513,3.51949,3.36512,3.71L3.36512,8.81056C3.36512,8.99091,3.51111,9.13722,3.69146,9.13761Z" fill-rule="evenodd" fill="currentColor" fill-opacity="1"</path></g></svg>
          </div>
        </div>`
      : ''}
      </div>
      <div class="pre-code">
        ${decimalStr}
        <code class="hljs code-block-body ${lang}">${str}</code>
      </div>
    </div>
    `;
}
const innerText = ref('');
const getMdiText = (value: string) => {
  mdi.use(markdownitAttrs);
  return mdi.render(value);
};
const htmlStr = computed(() => {
  if (printingIndex.value < (props.content || '').length) {
    const len = innerText.value.length;
    if (
      len > 3 &&
      /\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/.test(
        innerText.value.slice(len - 3, len)
      )
    ) {
      return getMdiText(innerText.value);
    } else {
      return getMdiText(innerText.value + '▌');
    }
  } else {
    return getMdiText(innerText.value);
  }
});

/**
 * 定时打字效果
 */
const printingIndex = ref(0); // 截取的字符长度索引
const printInterval = ref<number | null>(null);

function printText(speed = 25) {
  if (printInterval.value !== null) return;
  printInterval.value = Number(
    setInterval(() => {
      // 判断接下来6个字符中是否包含代码块标识 ```，是的话，索引+=6，避免将符号 ``` 截断
      const testStr = props.content!.slice(printingIndex.value, printingIndex.value + 6);
      printingIndex.value += /```/.test(testStr) ? 6 : 3;
      innerText.value = props.content!.slice(0, printingIndex.value);
      // 判断当前打字效果是否应换行
      if (/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/.test(testStr)) {
        emit('linePrint');
      }
      // 结束打字
      if (printingIndex.value >= props.content!.length) {
        clearInterval(Number(printInterval.value));
        printInterval.value = null;
        emit('finishPrint');
      }
    }, speed)
  );
}

/**
 * 代码操作
 * @param event
 */
function triggerMessageText(event: any) {
  // 复制代码到剪贴板
  let code = event.target.parentElement.parentElement.dataset.code || '';
  const content = codeInfo.codeStrMap[code] || '';
  if (event.target.dataset.action === 'copy') {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(content).then(() => {
        Message.info('已复制');
      })
    } else if (document.queryCommandSupported('copy')) {
      const tempTextarea = document.createElement('textarea');
      tempTextarea.value = content;
      document.body.appendChild(tempTextarea);
      tempTextarea.select();
      document.execCommand('copy');
      document.body.removeChild(tempTextarea);
      Message.info('已复制');
    } else {
      Message.error('复制失败');
    }
  }
  // 插入代码到编辑器
  if (event.target.dataset.action === 'insert') {
    chatStore.handleInsertCode(content);
  }
}

watch(
  () => props.content,
  () => {
    printText();
  },
  {
    deep: true,
    immediate: true,
  }
);

onBeforeUnmount(() => {
  clearInterval(Number(printInterval.value));
});
</script>

<style lang="scss">
.is-printing .code-block-header__icon {
  display: none;
}

.yellow {
  color: rgb(255, 140, 0);
}

ol {
  list-style-type: none;
}

ol li::before {
  content: '•';
  color: rgb(0, 153, 255);
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

a:focus {
  outline: none;
}

.message {
  flex-grow: 1;
}

.custom_button {
  background-color: #307CFB;
  /* 蓝色背景 */
  border: none;
  /* 去掉默认边框 */
  color: white;
  /* 白色文字 */
  padding: 5px 10px;
  /* 内边距 */
  font-size: 11px;
  /* 字体大小 */
  border-radius: 15px;
  /* 圆角边框 */
  cursor: pointer;
  /* 鼠标指针样式 */
  transition: background-color 0.3s ease;
  /* 背景色过渡效果 */
  margin-right: 8px;

}

#introHelpDoc {
  text-decoration: none !important;
  padding-right: 10px;
}

#introQuestion {
  color: #468CFF;
  text-decoration: none !important;
  padding-left: 10px;
}

#helpDoc {
  color: #468CFF;
  text-decoration: none !important;
  padding-right: 10px;
}

#feedback {
  color: #468CFF;
  text-decoration: none !important;
  padding-left: 10px;
}

#login {
  background-color: #307CFB;
  /* 蓝色背景 */
  border: none;
  /* 去掉默认边框 */
  color: white;
  /* 白色文字 */
  padding: 5px 10px;
  /* 内边距 */
  font-size: 14px;
  /* 字体大小 */
  border-radius: 15px;
  /* 圆角边框 */
  cursor: pointer;
  /* 鼠标指针样式 */
  transition: background-color 0.3s ease;
  /* 背景色过渡效果 */
  width: 120px;
  height: 32px;
  overflow: hidden
}

.custom_button:hover {
  background-color: #0056b3;
  /* 鼠标悬停时的背景色 */
}

.button-group {
  display: flex;
  gap: 2px;
  padding-top: 5px;
  flex-wrap: wrap;
}

.button-group button {
  flex: 0 0 auto;
}

.intro-note {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}
</style>
