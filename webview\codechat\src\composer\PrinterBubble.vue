<template>
  <div class="code-bubble ai-bubble" :class="{ 'is-printing': isPrinting }">
    <div class="message-base-info">
      <div class="logo">
        <svg-icon :name="brandStore.isSec ? 'ai-logo-sec' : 'ai-logo'" size="24px"></svg-icon>
      </div>
      <div class="message-base-info-text">
        <span class="message-user-name">{{ brandStore.brandName }}</span>
      </div>
    </div>
    <div class="message">
      <div class="message-text code-msg-loading-text wait-for-copy-box" v-html="htmlStr" @click="handleClick"></div>
    </div>
  </div>
</template>
<script lang="js" setup>
import { computed, onBeforeUnmount, ref, watch, getCurrentInstance, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import markdownitAttrs from 'markdown-it-attrs'
import MarkdownItTable from 'markdown-it-multimd-table'
import MarkdownItMath from 'markdown-it-texmath'
import katex from 'katex'
import hljs from 'highlight.js';
import { useChatStore } from '@/store/chat';
import { useBrandStore } from '@/store/brand';
import { hasDiffOf, highlightDiffCode } from './diff-helper.js'

const emit = defineEmits(['linePrint', 'finishPrint', 'shortcut', 'submitMsg']);
/**
 * props传参
 */
const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  silent: Boolean
});
const chatStore = useChatStore()
const brandStore = useBrandStore();
const printingIndex = ref(0); // 截取的字符长度索引

/**
 * md & highlight
 */
const mdi = new MarkdownIt({
  linkify: true,
  highlight(code, language = '') {
    const hasDiff = hasDiffOf(code)

    if (hasDiff) {
      const diffCode = highlightDiffCode(code)
      return highlightBlock(diffCode.value, language, diffCode.lineCount, true);
    } else {
      const validLang = !!(language && hljs.getLanguage(language));
      let codeText = ''

      if (validLang) {
        codeText = hljs.highlight(language, code, true).value
      } else {
        codeText = hljs.highlightAuto(code).value
      }
      const lineCount = code.split(/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/);
      return highlightBlock(codeText, language, lineCount);

    }
  },
});

mdi.use(markdownitAttrs);
mdi.use(MarkdownItMath, {
  engine: katex,
  delimiters: 'brackets',
  katexOptions: {
    displayMode: true,
    output: 'mathml'
  }
})
mdi.use(MarkdownItTable)

function highlightBlock(str, lang, lineCount, hasDiff = false) {
  let decimalStr = '<div class="code-decimal-index">';
  for (let i = 0; i < lineCount; i++) {
    decimalStr += `<div class="index-item">${i + 1}</div>`;
  }
  decimalStr += '</div>';

  return `
    <div class="pre-code-box">
      <div class="pre-code-header">
        <span class="code-block-header__lang">${lang}</span>
      </div>
      <div class="pre-code">
        ${decimalStr}
        <code class="hljs ${hasDiff? 'diff-code-block-body': 'code-block-body'} ${lang}">${str}</code>
      </div>
    </div>
    `;
}
const innerText = ref('');
const getMdiText = (value) => {

  return mdi.render(value);
};
const htmlStr = computed(() => {
  if (props.silent) {
    return getMdiText(props.text)
  }
  if (printingIndex.value < (props.text || '').length) {
    const len = innerText.value.length;
    if (
      len > 3 &&
      /\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/.test(
        innerText.value.slice(len - 3, len)
      )
    ) {
      return getMdiText(innerText.value);
    } else {
      return getMdiText(innerText.value + '▌');
    }
  } else {
    return getMdiText(innerText.value);
  }
});

/**
 * 定时打字效果
 */
const isPrinting = computed(() => printingIndex.value < (props.text || '').length);
const printInterval = ref(null);

const app = getCurrentInstance();

const $EventBus = app?.appContext.config.globalProperties.$EventBus;


function printText() {
  const speed = chatStore.PrintCacheConfig.printInterval
  const chunkSize = chatStore.PrintCacheConfig.chunkSize

  if (printInterval.value !== null) return;
  printInterval.value = Number(
    setInterval(() => {
      // 判断接下来6个字符中是否包含代码块标识 ```，是的话，索引+=6，避免将符号 ``` 截断
      const testStr = props.text.slice(printingIndex.value, printingIndex.value + 6);
      printingIndex.value += /```/.test(testStr) ? 6 : chunkSize;
      chatStore.printIndex = printingIndex.value
      innerText.value = props.text.slice(0, printingIndex.value);
      // 判断当前打字效果是否应换行
      if (/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/.test(testStr)) {
        emit('linePrint');
      }
      // 结束打字
      if (printingIndex.value >= props.text.length) {
        clearInterval(Number(printInterval.value));
        printInterval.value = null;
        emit('finishPrint');
      }
    }, speed)
  );
}

watch(
  () => props.text,
  () => {
    if (!props.silent) {
      printText();
    } else {
      emit('finishPrint');
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
function handleClick(event) {
  if (event.target.id === 'login') {
    chatStore.handleSubmitLogin();
  }
}
onBeforeUnmount(()=> { 
  clearInterval(Number(printInterval.value));
})
</script>

<style lang="scss">
.is-printing .code-block-header__icon {
  display: none;
}

.yellow {
  color: rgb(255, 140, 0);
}

ol {
  list-style-type: none;
}

ol li::before {
  content: '•';
  color: rgb(0, 153, 255);
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

a:focus {
  outline: none;
}

.message {
  flex-grow: 1;
}

.custom_button {
  background-color: #307CFB;
  /* 蓝色背景 */
  border: none;
  /* 去掉默认边框 */
  color: white;
  /* 白色文字 */
  padding: 5px 10px;
  /* 内边距 */
  font-size: 11px;
  /* 字体大小 */
  border-radius: 15px;
  /* 圆角边框 */
  cursor: pointer;
  /* 鼠标指针样式 */
  transition: background-color 0.3s ease;
  margin-right: 8px;
  /* 背景色过渡效果 */
}

#introHelpDoc {
  text-decoration: none !important;
  padding-right: 10px;
}

#introQuestion {
  color: #468CFF;
  text-decoration: none !important;
  padding-left: 10px;
}

#helpDoc {
  color: #468CFF;
  text-decoration: none !important;
  padding-right: 10px;
}

#feedback {
  color: #468CFF;
  text-decoration: none !important;
  padding-left: 10px;
}

#login {
  background-color: #307CFB;
  /* 蓝色背景 */
  border: none;
  /* 去掉默认边框 */
  color: white;
  /* 白色文字 */
  padding: 5px 10px;
  /* 内边距 */
  font-size: 14px;
  /* 字体大小 */
  border-radius: 15px;
  /* 圆角边框 */
  cursor: pointer;
  /* 鼠标指针样式 */
  transition: background-color 0.3s ease;
  /* 背景色过渡效果 */
  width: 120px;
  height: 32px;
  overflow: hidden
}

.custom_button:hover {
  background-color: #0056b3;
  /* 鼠标悬停时的背景色 */
}

.button-group {
  display: flex;
  gap: 2px;
  padding-top: 5px;
  flex-wrap: wrap;
}

.button-group button {
  flex: 0 0 auto;
}

.intro-note {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}

.code-msg-loading-text {
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    text-align: left;

    th {
      font-weight: bold;
    }

    th,
    td {
      padding: 5px;
      border: 1px solid var(--vscode-panel-border);
    }

    caption {
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: bold;
      text-align: left;
    }

    th {
      font-weight: bold;
      color: var(--vscode-input-foreground)
    }

    tr:nth-child(even) {}

    tr:hover {}
  }
}
</style>
