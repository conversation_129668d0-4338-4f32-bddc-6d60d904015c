html {
  height: 100%;
  /* overflow: hidden; */ /* Removed */
  margin: 0; /* Ensure no default browser margin */
}

body {
  font-family: var(--vscode-font-family);
  padding: 10px;
  color: var(--vscode-foreground);
  height: 100%; /* Fill height of html element */
  margin: 0; /* Ensure no default browser margin */
  /* overflow: hidden; */ /* Removed */
  box-sizing: border-box; /* Include padding in height calculation */
  display: flex;
  flex-direction: column;
}

/* New style for the main app container */
#app {
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Allow #app to take all available space in body */
  min-height: 0; /* Important for flex children that might scroll */
}

.controls {
  display: flex;
  margin-bottom: 15px;
  gap: 8px;
}
.search-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid var(--vscode-input-border);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-radius: 2px;
  min-width: 0;
}
.refresh-button {
  padding: 6px 10px;
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  flex-shrink: 0;
}
.column-headers {
  display: grid;
  grid-template-columns: 0.3fr 2.2fr 2.8fr 1.2fr 0.8fr;
  gap: clamp(4px, 1.5vw, 12px);
  padding: 4px 10px;
  font-weight: bold;
  background-color: var(--vscode-editor-background);
  border-bottom: 1px solid var(--vscode-list-inactiveSelectionBackground);
  margin-bottom: 8px;
  align-items: center;
  line-height: 1.2;
  min-height: 24px;
}
.column-select {
  text-align: center;
}
.column-id {
  text-align: left;
}
.column-title {
  text-align: left;
}
.column-creator {
  text-align: left;
}
.column-action {
  text-align: left;
}
.item {
  display: grid;
  grid-template-columns: 0.3fr 2.2fr 2.8fr 1.2fr 0.8fr;
  gap: clamp(4px, 1.5vw, 12px);
  align-items: center;
  padding: 8px 10px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: var(--vscode-editor-background);
  line-height: 1.4;
  min-height: 32px;
}
.item:hover {
  background-color: var(--vscode-list-hoverBackground);
}
.item-checkbox {
  text-align: center;
}
.item-id {
  font-weight: 500;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 1); /* 1行的高度 */
}
.item-title {
  word-break: break-word;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 2); /* 2行的高度 */
}
.item-creator {
  color: var(--vscode-descriptionForeground);
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  max-height: calc(1.4em * 1);
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}
.item-link {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
  text-align: left;
}
.item-link:hover {
  text-decoration: underline;
}
button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  padding: 6px 12px;
  border-radius: 2px;
  cursor: pointer;
}
button:hover {
  background-color: var(--vscode-button-hoverBackground);
}
.confirm-button {
  width: 100%;
  margin-top: 12px;
}
input[type="checkbox"] {
  cursor: pointer;
}
#workItems {
  /* margin-bottom: 16px; */ /* Remove, spacing handled by flex or confirm button's margin-top */
  /* max-height: calc(100vh - 120px); */ /* Remove, flex-grow will manage height */
  overflow-y: auto;
  position: relative; /* For potential absolute positioning of children if needed */
  flex-grow: 1; /* Allow this to take available vertical space */
  min-height: 0; /* Important for scrollable flex children */
}
.loading {
  text-align: center;
  margin: 5px 0 20px;
  font-style: italic;
}
#workItemsList {
  margin-top: 15px;
}
.no-items {
  text-align: center;
  margin: 5px 0 20px;
  font-style: italic;
  color: var(--vscode-descriptionForeground);
}

/* Toast 样式 - 顶部中央显示 */
.toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  max-width: 300px;
  word-wrap: break-word;
  text-align: center;
}

.toast-show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.toast-error {
  background-color: #f56565;
  border-left: 4px solid #e53e3e;
}

.toast-success {
  background-color: #48bb78;
  border-left: 4px solid #38a169;
}

.toast-warning {
  background-color: #ed8936;
  border-left: 4px solid #dd6b20;
}

.toast-info {
  background-color: #4299e1;
  border-left: 4px solid #3182ce;
}

/* 链接图标样式 */
.link-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 2px;
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
  transition: all 0.2s ease;
}

.link-icon:hover {
  background-color: var(--vscode-list-hoverBackground);
  color: var(--vscode-textLink-activeForeground);
  text-decoration: none;
}

.link-icon svg {
  width: 14px;
  height: 14px;
  fill: currentColor;
}

/* Custom Scrollbar Styles for #workItems container */
#workItems::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

#workItems::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(144, 147, 153, 0.33); /* #90939955 */
}

#workItems::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.47); /* #90939977 */
}

#workItems::-webkit-scrollbar-thumb:active {
  background-color: rgba(144, 147, 153, 0.6); /* #90939999 */
}

#workItems::-webkit-scrollbar-corner {
  background-color: transparent;
}

#workItems::-webkit-scrollbar-track {
  background-color: rgba(210, 210, 210, 0.215); /* #d2d2d237 */
  /* display: none; */ /* You might want the track always visible or only on hover */
}
