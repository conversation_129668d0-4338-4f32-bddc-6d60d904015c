<template>
  <div class="chat-item">
    <UserBubble v-if="item.role === 'user' && userContent" :reqTime="item.reqTime"
      :msg="userContent.text || item.errMsg || ''" :createTime="item.createTime" :files="files"></UserBubble>
    <CodeBubble v-else-if="item.role !== 'user'" :userText="parentUserText" :msgType="item?.type" :item="item" :files="parentFiles" @like="onLike(item)"
      @unlike="onUnlike(item)" @on-regen="onReGen(parent)"
      @onSelectApi="onSelectApi" :parent="props.parent">
      <template #toolbar>
        <div class="switch-btn" v-if="showSwitch">
          <div tooltip="上一个答案" style="margin-right: 5px;" class="msg-icon-btn">
            <svg-icon v-if="hasPrev" name="arrow-left" class="msg-icon" @click="onPrev"></svg-icon>
            <svg-icon v-else name="arrow-left" class="msg-icon" style="opacity: 0.2;cursor: not-allowed;"></svg-icon>
          </div>
          <div>
            {{ data.index + 1 }} / {{ (children.length) }}
          </div>
          <div tooltip="下一个答案" style="margin-right: 5px;" class="msg-icon-btn">
            <svg-icon v-if="hasNext" name="arrow-right" class="msg-icon" @click="onNext"></svg-icon>
            <svg-icon v-else name="arrow-right" class="msg-icon" style="opacity: 0.2;cursor: not-allowed;"></svg-icon>
          </div>
        </div>
      </template>
    </CodeBubble>
  </div>

  <MessageItem v-if="showChild" @onLike="onLike"
    :parentUserText="userContent.text"
    :parent="item" :children="item.children" :parentFiles="files" @onUnlike="onUnlike" @onReGen="onReGen" @onSelectApi="onSelectApi"/>
</template>
<script setup>
import UserBubble from '@/components/UserBubble.vue';
import CodeBubble from '@/components/CodeBubble.vue';
import MessageItem from './MessageItem.vue';
import { computed, reactive, watch } from 'vue';
import { useChatStore } from '@/store/chat';
import { FileTypeIcon, getFileNameFromPath, getFolderNameFromPath } from '@/utils';

const chatStore = useChatStore();
const props = defineProps({
  children: {
    type: Array,
    default: () => []
  },
  parent: {
    type: Object,
    default: () => ({})
  },
  parentFiles: {
    type: Array,
    default: () => []
  },
  parentUserText: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['onUnlike', 'onLike', 'onReGen', 'onSelectApi']);
const data = reactive({
  index: 0
})


const item = computed(() => {
  return props.children[data.index] || { children: [] }
})


const userContent = computed(() => {
  return (item.value.content||[]).find(c=>c.type === 'text')
})


const files = computed(()=> {
  const content = item.value.content
  if (Array.isArray(content)) {
    return content.filter(item => item.type === 'local_file' || item.type === 'work_item').map(item => {
      if (item.type === 'work_item') {
        return {
          ...item.work_item,
          icon: FileTypeIcon.workItem,
          name: item.text,
        }

      }


      const path = item.local_file.path

      let name = ''
      let icon = FileTypeIcon.file
      if(item.local_file.type === 'folder') {
        name = getFolderNameFromPath(path)
        icon = FileTypeIcon.folder
      }else if(item.local_file.type === 'codebase') {
        name = '当前工程'
        icon = FileTypeIcon.codebase
      } else /* if(item.local_file.type === 'file') */ {
        name = getFileNameFromPath(path)
        const type = name.split('.')[name.split('.').length - 1]
        icon = FileTypeIcon[type] || FileTypeIcon.file
      } 
      
      return {
        ...item.local_file,
        icon,
        name,
        text: item.text
      }
    })
  }
  return []
})

const showSwitch = computed(() => {
  return props.children.length > 1
})


const showReGenBtn = computed(() => {
  return item.value.children && item.value.children.length === 0;
})

const showChild = computed(() => {
  if (chatStore.isReAskReqId) {
    if (chatStore.isReAskReqId === item.value.reqId) {
      if(item.value.role === 'user') return false
    }
  }
  return item.value.children.length
  
})

const hasNext = computed(() => {
  return showSwitch.value && data.index < props.children.length - 1;
})

const hasPrev = computed(() => {
  return showSwitch.value && data.index > 0;
})

watch(() => props.parent.reqId, () => {
  if (props.parent.role === 'user' && props.parent.content) {
    const k = props.parent.content.find(c => c.type === 'knowledge_base')
    if (k) {
      chatStore.currentLib.id = k.knowledge_base_id
      chatStore.currentLib.lastTime = Date.now()
      console.log('最新的knowledge_base_id', k.knowledge_base_id)
    } else {
      if (props.parent.from !== 'local') {
        chatStore.currentLib.id = ''
        chatStore.currentLib.lastTime = Date.now()
        console.log('最新的knowledge_base_id', '没有')
      }
     
    }
  }
}, {immediate: true})


watch(() => item.value.reqId, () => {
  if (showReGenBtn.value) {
    chatStore.lastParentReqId = item.value.reqId
  }

}, { immediate: true })

const childIds = computed(() => {
  return props.children.map(item=>item.reqId).join('_')
})

watch(childIds, () => {
  data.index = props.children.length - 1
}, { immediate: true, deep: true })


function onNext() {
  if(chatStore.answering) return
  if (data.index < props.children.length - 1) data.index++
}
function onPrev() {
  if(chatStore.answering) return
  if (data.index > 0) data.index--
}

function onSelectApi(item) {
  emit('onSelectApi', item)
}
function onUnlike(item) {
  emit('onUnlike', item);
}
function onLike(item) {
  emit('onLike', item);
}

function onReGen(item) {
  emit('onReGen', item);
}

</script>

<style lang="scss">
.switch-btn {
  display: flex;
  align-items: center;
}
</style>