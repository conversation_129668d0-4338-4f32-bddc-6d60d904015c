import Code<PERSON>ICommHand<PERSON> from '../commclient/codeAICommHandler';
import { SendMessageRtn } from '../commclient/types/webSocket';
import {
  ASYNC_MSG_RECV_TIMEOUT,
  CHANNEL_TYPE,
  MAX_NEW_TOKENS_AI_AUTO,
  MAX_NEW_TOKENS_AI_MANUAL,
  MAX_NEW_TOKENS_CHAT,
  SYNC_MSG_RECV_TIMEOUT,
} from '../common/config';
import {
  AnswerMode,
  ChannelStatus,
  ChannelType,
  IsAnswerEnd,
  LoginStatus,
  QuestionType,
  RtnCode,
} from '../common/constants';
import { generateUUID } from '../utils/common';
import { Logger } from '../utils/logger';
import CodeAIRequestReceiver from './codeAIRequestReceiver';
import CodeAIRequestSender from './codeAIRequestSender';
import { LoginServiceInst } from './loginService';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { AskQuestionParams, AskQuestionResult, IQuestionTaskEvent } from './types/questionTask';
import { ChatHistoryServiceInst } from './chatHistoryService';

export default class QuestionTask implements IAnswerHandler {
  private reqId!: string;

  private iQstTskEvent: IQuestionTaskEvent;

  private answerMode: AnswerMode;

  private askTimeout: NodeJS.Timer | undefined;

  private askQuestionParams: AskQuestionParams | undefined;

  public constructor(iQstTskEvent: IQuestionTaskEvent, answerMode: AnswerMode) {
    this.iQstTskEvent = iQstTskEvent;
    this.answerMode = answerMode;
  }

  /**
   * 获取reqId
   */
  public getReqId(): string {
    return this.reqId;
  }

  /**
   * 获取请求参数
   * @returns
   */
  public getAskQuestionParams(): AskQuestionParams | undefined {
    return this.askQuestionParams;
  }

  /**
   * 发起请求
   */
  public askQuestion(params: AskQuestionParams): AskQuestionResult {
    if (LoginServiceInst.getLoginStatus() === LoginStatus.NOT_OK) {
      Logger.error('[QuestionTask] askQuestion, not login');

      return {
        rtnCode: RtnCode.NOT_LOGIN,
      };
    }

    if (CodeAICommHandler.getInstance().getChannelStatus() === ChannelStatus.DISCONNECTED) {
      Logger.error('[QuestionTask] askQuestion, ws is not ready.');

      return {
        rtnCode: RtnCode.NO_CHANNEL_CHAT,
      };
    }

    this.reqId = generateUUID();
    this.askQuestionParams = params;

    switch (CHANNEL_TYPE) {
      case ChannelType.WS:
        this.askByWSChannel(params);
        break;
      default:
        break;
    }

    return {
      reqId: this.reqId,
      rtnCode: RtnCode.SUCCESS,
    };
  }

  /**
   * 取消请求
   */
  public cancelQuestion() {
    this.cancelReqIdTimeoutTask(this.reqId, true);
    this.iQstTskEvent.onTaskError(this.reqId, RtnCode.CANCEL);
  }

  /**
   * 停止chat回答
   */

  public async stopAnswer(reqId: string, request: any) {
    // this.cancelReqIdTimeoutTask(this.reqId, true);
    // this.iQstTskEvent.onTaskError(this.reqId, RtnCode.STOP_ANSWER);
    try {
      this.cancelReqIdTimeoutTask(reqId, true);
      const files = this.getAskQuestionParams()?.relatedFiles;
      await ChatHistoryServiceInst.stopAnswer({ ...request, reqId, files });
      this.iQstTskEvent.onTaskError(reqId, RtnCode.STOP_ANSWER);
    } catch (e) { 
      Logger.debug(`[questionTask] stopAnswer error" ${e}`);
    }
    
  }

  /**
   * 处理CodeReceiver接收的响应体
   * @param reqId
   * @param rtnCode
   * @param isEnd
   * @param answer
   */
  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    const { isEnd = 0, answer = '', errMsg = '' } = payload || {};
    if (rtnCode !== RtnCode.SUCCESS) {
      this.cancelReqIdTimeoutTask(reqId, true);
      this.iQstTskEvent.onTaskError(reqId, rtnCode, errMsg);

      return;
    }

    if (isEnd === IsAnswerEnd.NO) {
      switch (this.answerMode) {
        case AnswerMode.ASYNC:
          this.cancelReqIdTimeoutTask(reqId, false);
          this.iQstTskEvent.onAnswer(reqId, isEnd, answer, payload);
          this.askTimeout = setTimeout(() => this.handleResvTimeout(), ASYNC_MSG_RECV_TIMEOUT);
          break;
        default:
          break;
      }
    } else if (isEnd === IsAnswerEnd.YES) {
      this.cancelReqIdTimeoutTask(reqId, true);
      this.iQstTskEvent.onAnswer(reqId, isEnd, answer, payload);
    }
  }

  /**
   * WS通道请求问题
   * @param params 请求参数
   */
  private async askByWSChannel(params: AskQuestionParams) {
    // 新增请求的AnswerHandler
    CodeAIRequestReceiver.addAnswerHandler(this);

    // 创建请求超时定时任务
    this.askTimeout = setTimeout(() => this.handleResvTimeout(), SYNC_MSG_RECV_TIMEOUT);

    // 参数请求解构
    const {
      questionType,
      question,
      fileName,
      language,
      prefix,
      suffix,
      manualType,
      prompts,
      stopWords,
      dialogId,
      questionAskType,
      parentReqId,
      templateId,
      kbId,
      quote,
      importSnippets,
      relatedFiles,
      selectedWorkItems
    } = params;
    let sndMsgRtn: SendMessageRtn | null = null;
    const codeChatParams = { kbId, quote, dialogId, questionAskType, parentReqId, templateId, relatedFiles, selectedWorkItems };

    switch (questionType) {
      // 发起自动代码补全请求
      case QuestionType.CODE_GEN:
        sndMsgRtn = await CodeAIRequestSender.sendCodeGenRequest(
          this.reqId,
          fileName || '',
          prefix || '',
          suffix || '',
          language || '',
          MAX_NEW_TOKENS_AI_AUTO,
          stopWords,
          importSnippets
        );
        break;
      // 发起手动代码补全请求
      case QuestionType.CODE_GEN_MANUAL:
        sndMsgRtn = await CodeAIRequestSender.sendCodeGenRequest(
          this.reqId,
          fileName || '',
          prefix || '',
          suffix || '',
          language || '',
          MAX_NEW_TOKENS_AI_MANUAL,
          stopWords
        );
        break;
      // 发起chat聊天请求
      case QuestionType.CODE_CHAT:
        sndMsgRtn = await CodeAIRequestSender.sendChatGenRequest(
          this.reqId,
          question || '',
          prompts || [],
          MAX_NEW_TOKENS_CHAT,
          manualType,
          codeChatParams
        );
        break;
      // 发起自然语言编辑请求
      case QuestionType.NATURAL_LANG:
        sndMsgRtn = await CodeAIRequestSender.sendChatGenRequest(
          this.reqId,
          question || '',
          prompts || [],
          MAX_NEW_TOKENS_CHAT,
          manualType
        );
        break;
      default:
        break;
    }

    if (sndMsgRtn?.rtnCode !== RtnCode.SUCCESS) {
      this.cancelReqIdTimeoutTask(this.reqId, true);
      this.iQstTskEvent.onTaskError(this.reqId, sndMsgRtn?.rtnCode || RtnCode.SEND_ERROR);
    }
  }

  /**
   * 处理请求超时, 注销answerHandler, 上报请求异常
   */
  private handleResvTimeout() {
    if (this.reqId) {
      CodeAIRequestReceiver.deleteAnswerHandler(this.reqId);
      this.iQstTskEvent.onTaskError(this.reqId, RtnCode.RECV_TIMEOUT);
    }
  }

  /**
   * 取消请求超时定时任务
   * @param reqId 请求Id
   * @param isDelAnsHandler 是否注销answerHandler
   */
  private cancelReqIdTimeoutTask(reqId: string, isDelAnsHandler: boolean) {
    if (isDelAnsHandler && reqId) {
      CodeAIRequestReceiver.deleteAnswerHandler(reqId);
    }

    clearTimeout(this.askTimeout);
  }
}
