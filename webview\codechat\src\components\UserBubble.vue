<template>
  <div class="code-bubble user-bubble">
    <div class="message-base-info">
      <div class="logo user-logo">
        <svg-icon name="icon-user-shape" size="11px"></svg-icon>
      </div>
      <div class="message-base-info-text">
        <span class="message-user-name">Me</span>
        <span class="message-time">{{ getTime(props.reqTime!) }}</span>
      </div>
    </div>
    <div class="message">
      <div class="message-text user-text-content" @click="triggerMessageText" v-html="htmlStr"></div>

      <div v-if="files && files.length" class="file-list">
        <a-button :disabled="item.type !== 'file'" v-for="(item, index) in files" size="mini" @click.stop="chatStore.openFile(item)">
          <svg-icon class="lib-name-icon" :name="item.icon" style="margin-right: 5px;"></svg-icon>
          <span style="display: inline-block;max-width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
          {{ item.name }} 
          </span>
          <template v-if="typeof item.startLine === 'number' && typeof item.endLine === 'number' && item.startLine >=0 && item.endLine >= 0">
            <span style="margin-left: 4px;">
              ({{ item.startLine + 1 }} - {{ item.endLine + 1 }})
            </span>
          </template>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { dateFormat } from '@/utils';
import { Message } from '@arco-design/web-vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import { CodeInfo } from '@/types/index';
import { hashCode } from '@/utils';
import { computed, reactive } from 'vue';
import { useChatStore } from '@/store/chat';

const chatStore = useChatStore()


const props = defineProps<{ msg: string; reqTime?: string, files?: any[]}>();
const codeInfo = reactive<CodeInfo>({
  codeStrMap: {},
});

function getTime(time: string) {
  return dateFormat(new Date(time), 'YYYY-mm-dd HH:MM:SS');
}

/**
 * md & highlight
 */
const mdi = new MarkdownIt({
  linkify: true,
  highlight(code: string, language: string) {
    const validLang = !!(language && hljs.getLanguage(language));
    const hashCodeVal = hashCode(code);
    codeInfo.codeStrMap[hashCode(code)] = code;
    if (validLang) {
      const lang = language ?? '';
      return highlightBlock(hljs.highlight(lang, code, true).value, lang, code, hashCodeVal);
    }
    return highlightBlock(hljs.highlightAuto(code).value, '', code, hashCodeVal);
  },
});
function highlightBlock(str: string, lang: string, code: string, hashCodeVal: number) {
  const blocks = code.split(/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/);
  let decimalStr = '<div class="code-decimal-index">';
  blocks.forEach((item, itemIndex) => {
    decimalStr += `<div class="index-item">${itemIndex + 1}</div>`;
  });
  decimalStr += '</div>';
  return `
    <div class="pre-code-box">
      <div class="pre-code-header">
        <span class="code-block-header__lang">${lang}</span>
        <div class="code-block-header__icons">
          <div tooltip="复制" class="code-block-header__icon">
            <svg class="copy-icon" data-action="copy" data-code="${hashCodeVal}" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 8.75830078125 10"><g><path d="M2.50489,7.4916L0.474489,7.4916C0.212436,7.4916,0,7.27916,0,7.01711L0,0.474489C0,0.212436,0.212436,0,0.474489,0L5.779,0C6.04105,0,6.25349,0.212436,6.25349,0.474489L6.25349,2.50482L8.28389,2.50482C8.54594,2.50482,8.75838,2.71726,8.75838,2.97931L8.75838,9.52551C8.75838,9.78755,8.54594,9.99998,8.28389,9.99999L2.97938,9.99999C2.71733,9.99999,2.5049,9.78756,2.50489,9.52551L2.50489,7.4916ZM2.50489,6.63351L1.20876,6.63351C1.01643,6.6339,0.85998,6.47873,0.858795,6.28641L0.858795,1.21663C0.858789,1.01901,1.019,0.858805,1.21663,0.858805L5.02683,0.858805C5.22999,0.8588,5.39469,1.02349,5.39469,1.22665L5.39469,2.50482L2.97937,2.50482C2.71732,2.50482,2.50488,2.71726,2.50488,2.97931L2.50489,2.98289L2.50489,6.63351ZM3.69146,9.13761L7.56249,9.13761C7.75221,9.13604,7.90473,8.98095,7.90314,8.79123L7.90314,3.72288C7.90315,3.52526,7.74294,3.36505,7.54532,3.36505L3.71006,3.36505C3.51956,3.36505,3.36513,3.51949,3.36512,3.71L3.36512,8.81056C3.36512,8.99091,3.51111,9.13722,3.69146,9.13761Z" fill-rule="evenodd" fill="currentColor" fill-opacity="1"/></g></svg>
          </div>
        </div>
      </div>
      <div class="pre-code">
        ${decimalStr}
        <code class="hljs code-block-body ${lang}">${str.replace(/\t/g, ' ')}</code>
      </div>
    </div>
    `;
}

const getMdiText = (value: string) => {
  return mdi.render(value);
};
const htmlStr = computed(() => {
  return getMdiText(props.msg);
});

/**
 * 代码操作
 * @param event
 */
function triggerMessageText(event: any) {
  if (event.target.dataset.action === 'copy') {
    let code = event.target.dataset.code;
    const content = codeInfo.codeStrMap[code] || '';
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(content).then(() => {
        Message.info('已复制');
      })
    } else if (document.queryCommandSupported('copy')) {
      const tempTextarea = document.createElement('textarea');
      tempTextarea.value = content;
      document.body.appendChild(tempTextarea);
      tempTextarea.select();
      document.execCommand('copy');
      document.body.removeChild(tempTextarea);
      Message.info('已复制');
    } else {
      Message.error('复制失败');
    }
  }
}

function copyMessage() {
  var content = props.msg
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(content).then(() => {
      Message.info('已复制');
    })
  } else if (document.queryCommandSupported('copy')) {
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = content;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    document.execCommand('copy');
    document.body.removeChild(tempTextarea);
    Message.info('已复制');
  } else {
    Message.error('复制失败');
  }
}
</script>

<style lang="scss">
.message-base-info {
  display: flex;
  align-items: center;
}
.message-base-info-text {

}
.user-bubble {
  .file-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 5px;
    gap: 5px;
    margin-top: 10px;
  }
  .message {
    &-text {
      // white-space: pre-wrap;

      p:first-child,
      pre:first-child {
        margin-top: 0;
      }
    }
  }
}
</style>
