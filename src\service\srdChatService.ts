import HttpClient from '../commclient/httpClient';
import { RtnCode, ClientCommunicateType } from '../common/constants';
import { CodeAIResponsePayload, IAnswerHandler } from './types/codeAI';
import { SrdChatResult, AskQuestionResp } from './types/srdChat';
import { ASK_QUESTION_PATH, RECORD_LIST_PATH} from '../common/config';
import { HttpError } from '../commclient/types/http';
import { Logger } from '../utils/logger';
import { getCurrentUser } from '../common/globalContext';

/**
 * 文件服务
 */
export default class SrdChatService implements IAnswerHandler {
  private type: ClientCommunicateType = ClientCommunicateType.HTTP;

  private httpClient: HttpClient | undefined;

  /**
   * 通过哪种方式上传
   * @param type
   */
  public constructor(type?: ClientCommunicateType) {
    if (type) {
      this.type = type;
    }

    if (this.type === ClientCommunicateType.HTTP) {
      this.httpClient = new HttpClient(undefined, 'srdChat');
    }
  }

  private generateXDUPID() {
    function S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    const timeStamp = Math.round(Date.now() / 1000);
    return timeStamp + '-' + S4() + S4();
  }

  public async askQuestion(question?: string): Promise<SrdChatResult> { 
    if (!this.httpClient) { 
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const params: Record<string, string> = {
     question: question ||'',
    };
    const urlParams = new URLSearchParams(params).toString();
    const mergedPath = `${ASK_QUESTION_PATH}${urlParams ? '?' + urlParams : ''}`;
    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<AskQuestionResp>(mergedPath, null, 
      {
        headers: {
          invokerId: uInfo.userId as string,
          apiKey: uInfo.apiKey as string,
          'x-dup-id': this.generateXDUPID(),
        },
      }
    );
    if (!result || result instanceof HttpError) {
      Logger.error(`[SrdChatService] askQuestion failed, ${JSON.stringify(result)}`);

      return {
        code: result?.code || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[SrdChatService] askQuestion result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result
    };
  }

  public async recordList(payload: {timeBefore: string, currentPage: number, pageSize: number}): Promise<SrdChatResult> { 
    if (!this.httpClient) { 
      return {
        code: RtnCode.HTTP_CLIENT_ERROR,
      };
    }
    const params: Record<string, string> = {
      timeBefore: payload.timeBefore,
      currentPage: payload.currentPage.toString(),
      pageSize: payload.pageSize.toString()

    };
    const urlParams = new URLSearchParams(params).toString();
    const mergedPath = `${RECORD_LIST_PATH}${urlParams ? '?' + urlParams : ''}`;

    const uInfo = await getCurrentUser();
    const result = await this.httpClient.requestByPost<AskQuestionResp>(mergedPath, null, {
      headers: {
        invokerId: uInfo.userId as string,
        apiKey: uInfo.apiKey as string,
        'x-dup-id': this.generateXDUPID(),
      },
    });
    if (!result || result instanceof HttpError) {
      Logger.error(`[SrdChatService] recordList failed, ${JSON.stringify(result)}`);

      return {
        code: result?.code || RtnCode.HTTP_REQUEST_ERROR,
      };
    }
    Logger.debug(`[SrdChatService] recordList result: ${JSON.stringify(result)}`);
    return {
      code: RtnCode.SUCCESS,
      data: result
    };
  }


  public onAnswer(reqId: string, rtnCode: number, payload?: CodeAIResponsePayload): void {
    throw new Error('Method not implemented.');
  }

  public getReqId(): string {
    throw new Error('Method not implemented.');
  }

  
}
