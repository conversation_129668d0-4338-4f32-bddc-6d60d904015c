export interface DiffMsgData {
  path: string;
  beforeContent: string;
  afterContent: string;
}

export interface DiffMessage {
  messageType: string;
  data: DiffMsgData[];
  messageId: string;
}

export interface VirtualFile {
  path: string;
  name: string;
}

export interface SimpleDiffRequest {
  beforeContent: string;
  afterContent: string;
}

export interface FileAction {
  apply: () => void;
  revert: () => void;
}


//参考DiffContext.ts
export interface DiffFile {
  path: string;
  name: string;
  content: string;
  originalContent: string;
  status: DiffStatus;
}

export type DiffStatus = 'accepted' | 'undecided' | 'rejected' | 'partial_accepted';

export interface DiffContextType {
  diffFiles: DiffFile[];
  addDiff: (diff: DiffFile) => void;
  clearDiffs: () => void;
  openDiffEditor: (diff: DiffFile) => void;
  rejectDiff: (path: string) => void;
  toggleFileStatus: (file: DiffFile) => void;
  toggleAllFiles: () => void;
  updateFileStatus: (filePath: string, newStatus: 'accepted' | 'undecided') => void;
  getAllItemsStatus: () => 'none' | 'all_accepted' | 'partial_accepted';
}
