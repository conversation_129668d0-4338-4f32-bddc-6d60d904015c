import * as vscode from 'vscode';
import {
  Disposable,
  ExtensionContext,
  TextEditorSelectionChangeEvent,
  commands,
  window,
} from 'vscode';
import { CodeSelectionEventType, ICodeSelectionHandler, diagnosticCodeType } from '../codechat/types';
import { ChatMessageType, ChatMessageTypeDesc, SrdCommand } from '../common/constants';
import EditorUtil from '../codechat/editorUtil';

/**
 * 选中代码，触发代码解释、代码注释、代码单元测试流程管理
 */
export default class CodeExceptionManager implements Disposable {
  private context: ExtensionContext;

  private selectionHandler: ICodeSelectionHandler;

  private disposable: Disposable;

  public constructor(context: ExtensionContext, selectionHandler: ICodeSelectionHandler) {
    this.context = context;
    this.selectionHandler = selectionHandler;
    this.disposable = Disposable.from(...this.registerCommands());
  }

  public dispose() {
    this.disposable.dispose();
  }

  /**
   * 注册代码解释、代码注释、单元测试指令
   * @returns
   */
  private registerCommands(): Disposable[] {
    const disposable: Disposable[] = [];
    disposable.push(
      commands.registerCommand(SrdCommand.EXCEPTION_FIX, (document: vscode.TextDocument, diagnostic: vscode.Diagnostic, diagnostics: vscode.Diagnostic[]) => {
        let minStartLine = Number.MAX_SAFE_INTEGER, maxEndLine = -Number.MAX_SAFE_INTEGER;
        diagnostics.forEach(diagnostic => {
          const { start, end } = diagnostic.range;
          if (start.line < minStartLine) {
            minStartLine = start.line;
          }

          // 更新最大的终止行
          if (end.line > maxEndLine) {
            maxEndLine = end.line;
          }
        });
        let { startLine, endLine } = EditorUtil.getCurrentSelectedCodeLines();
        const code = EditorUtil.getCurrentSelectedCode();
        if (code) { 
          minStartLine = Math.min(minStartLine, startLine);
          maxEndLine = Math.max(maxEndLine, endLine);
        }
        EditorUtil.setLineCodeSelected(minStartLine, maxEndLine);
        const errDesc = this.getErrorDesc(diagnostics);
        this.sendCodeSelectionMessage(ChatMessageType.EXCEPTION_FIX, errDesc);
      }),
    );

    return disposable;
  }

  private getSelectedDiagnostics(diagnostics: vscode.Diagnostic[]) { 
    const editor = window.activeTextEditor;
    const selection = editor?.selection;
    if (!editor || !selection) {
      return [];
    }
    const lines = [selection.start.line, selection.end.line];
    const relevantDiagnostics = diagnostics.filter((diagnostic) => {
      const range = diagnostic.range;
      const startLine = range.start.line;
      const endLine = range.end.line;
      return startLine >= lines[0] && endLine <= lines[1];
    });
    return relevantDiagnostics;
  }

  private getErrorDesc(diagnostics: vscode.Diagnostic[]) { 
    const errDesc = diagnostics.reduce((acc, diagnostic) => {
      let code: string | number = '', lineRanges: string = '';
      if (diagnostic.code) { 
        code = this.isDiagnosticCodeType(diagnostic.code) ? diagnostic.code.value : diagnostic.code;
        code = '(' + code + ')';
      }
      if (diagnostic.range) { 
        const startLine = diagnostic.range.start.line + 1;
        const startCol = diagnostic.range.start.character + 1;
        lineRanges = ` [Ln ${startLine}, Col ${startCol}]`;
      }
      const errMsg = diagnostic.message + ' ' + diagnostic.source + code + lineRanges + '\n';
      return acc + errMsg;
    }, '');
    return errDesc;
  }

  private isDiagnosticCodeType(value: any): value is diagnosticCodeType { 
     return (
        typeof value === 'object' &&
        value !== null &&
        ('value' in value) &&
        ('target' in value) &&
        (typeof value.value === 'string' || typeof value.value === 'number') &&
        value.target instanceof vscode.Uri
    );
  }


  /**
   * 发送代码片断给webview处理相应命令
   * @param type
   */
  private sendCodeSelectionMessage(type: ChatMessageType, errDesc?: string) {
    const code = EditorUtil.getCurrentSelectedCode();
    if (!code) {
      window.showInformationMessage('当前没有异常代码', '关闭');
    } else {
      this.selectionHandler.handleCodeSelectionEvent(CodeSelectionEventType.CODE_SELECTION_ASKED, {
        code,
        type,
        errDesc,
      });
    }
  }

  private getCodeLinesByRange(range: vscode.Range) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return 0;
    }
    const startLineNumber = range.start.line;
    const endLineNumber = range.end.line;
    return endLineNumber - startLineNumber + 1;
  }
  
}
