import {
  Disposable,
  ExtensionContext,
  commands,
  window,
  Range,
} from 'vscode';
import { CodeSelectionEventType, ICodeSelectionHandler } from '../codechat/types';
import { SrdCommand, ActionTypeMap, WebViewRspCommand } from '../common/constants';


export default class CodeLensManager implements Disposable {
  private context: ExtensionContext;

  private selectionHandler: ICodeSelectionHandler;

  private disposable: Disposable;

  public constructor(context: ExtensionContext, selectionHandler: ICodeSelectionHandler) {
    this.context = context;
    this.selectionHandler = selectionHandler;
    this.disposable = Disposable.from(...this.registerCommands());
  }

  public dispose() {
    this.disposable.dispose();
  }

  /**
  * 注册Codelens指令
  * @returns
  */
  private registerCommands(): Disposable[] {
    const disposable: Disposable[] = [];
    disposable.push(
      commands.registerCommand(SrdCommand.CODELENS_ACTION, this.handleFunctionAction.bind(this)),
    );

    return disposable;
  }

  private async handleFunctionAction(action: string, range: Range, code: string) {
    const editor = window.activeTextEditor;
    if (!editor) return;

    const filePath = editor.document.uri.fsPath;
    const startLine = range.start.line;
    const endLine = range.end.line;

    const actions = ['解释代码', '生成单元测试', '生成代码注释', '生成代码优化建议'];
    if (actions.includes(action)) {
      const type = ActionTypeMap[action];

      this.selectionHandler.sendMessageToWebView({
            command: WebViewRspCommand.CODE_SELECTION_CHANGED,
            data: {
              code: code,
              filePath: filePath,
              startLine: startLine,
              endLine: endLine,
            },
          });

      this.selectionHandler.handleCodeSelectionEvent(CodeSelectionEventType.CODE_SELECTION_ASKED, {
        code,
        type,
        filePath,
        startLine,
        endLine
      });
      
    } else {
      window.showErrorMessage(`未知动作: ${action}`);
    }
  }
  
}
