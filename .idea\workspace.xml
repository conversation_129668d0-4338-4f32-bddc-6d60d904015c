<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="caad4871-e125-4878-94e0-d6bec3848aef" name="Changes" comment="【优化】设置按钮合并到状态栏里，与webview保持两套入口">
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/settings/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/settings/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/settings/type.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/settings/type.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/settings/utils.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/settings/utils.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/webview/codechat/auto-imports.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/webview/codechat/auto-imports.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/webview/codechat/brand.json" beforeDir="false" afterPath="$PROJECT_DIR$/webview/codechat/brand.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/webview/codechat/components.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/webview/codechat/components.d.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="recover-branch" />
        <entry key="$PROJECT_DIR$" value="jh-oscap-dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitRewordedCommitMessages">
    <option name="commitMessagesMapping">
      <RewordedCommitMessageMapping>
        <option name="originalMessage" value="【预测补全】vscode基础版本" />
        <option name="rewordedMessage" value="【预测补全】vscode基础版" />
      </RewordedCommitMessageMapping>
    </option>
    <option name="currentCommit" value="1" />
    <option name="onto" value="d6ac73b001ca8ab4ff5f19ed256f0bb002aa84db" />
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="1slr7nfy" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2v3opZocA6OlkDR30lPrCgDc5mO" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "version__hya/c10__250430__01fd__3.5.0",
    "junie.onboarding.icon.badge.shown": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/CodeRepos/srd-copilot-jetbrains",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "yarn",
    "org.rust.cargo.project.model.impl.CargoExternalSystemProjectAware.subscribe.first.balloon": "",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.keymap",
    "ts.external.directory.path": "E:\\CodeRepos\\srd-copilot-vscode\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="Push.Settings">
    <force-push-targets>
      <force-push-target remote-path="origin" branch="version_hya/c10_250430_01fd_3.5.0" />
    </force-push-targets>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="srd-copilot-vscode" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="srd-copilot-vscode" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="srd-copilot-vscode" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <module name="srd-copilot-vscode" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="srd-copilot-vscode" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="caad4871-e125-4878-94e0-d6bec3848aef" name="Changes" comment="" />
      <created>1743383302523</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743383302523</updated>
      <workItem from="1743383303799" duration="7960000" />
      <workItem from="1743577272532" duration="203000" />
      <workItem from="1743577526344" duration="3992000" />
      <workItem from="1743646243581" duration="10050000" />
      <workItem from="1744004258964" duration="2489000" />
      <workItem from="1744094092320" duration="1692000" />
      <workItem from="1744179303322" duration="7636000" />
      <workItem from="1745569743386" duration="7145000" />
      <workItem from="1746500731130" duration="10041000" />
      <workItem from="1746670022600" duration="37719000" />
      <workItem from="1747618707317" duration="10985000" />
      <workItem from="1748227422193" duration="50339000" />
      <workItem from="1749434338320" duration="6976000" />
      <workItem from="1749462194239" duration="11952000" />
      <workItem from="1749545370170" duration="4732000" />
      <workItem from="1749796956731" duration="1440000" />
      <workItem from="1750036263658" duration="6322000" />
      <workItem from="1750052755404" duration="13735000" />
      <workItem from="1750152964181" duration="135000" />
      <workItem from="1750153131824" duration="1186000" />
      <workItem from="1750298862627" duration="1192000" />
      <workItem from="1750317524120" duration="155000" />
      <workItem from="1750400840235" duration="9153000" />
      <workItem from="1750668467450" duration="3121000" />
      <workItem from="1750814477695" duration="5565000" />
    </task>
    <task id="LOCAL-00001" summary="feat：tabby-agent通信部分与core-agent调整一致">
      <option name="closed" value="true" />
      <created>1743573804842</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1743573804842</updated>
    </task>
    <task id="LOCAL-00002" summary="feat：tabby-agent通信部分与core-agent调整一致">
      <option name="closed" value="true" />
      <created>1743574469769</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1743574469769</updated>
    </task>
    <task id="LOCAL-00003" summary="feat：tabby-agent通信部分与core-agent调整一致">
      <option name="closed" value="true" />
      <created>1743576333381</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1743576333381</updated>
    </task>
    <task id="LOCAL-00004" summary="feat：tabby-agent通信部分与core-agent调整一致">
      <option name="closed" value="true" />
      <created>1743577166450</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1743577166450</updated>
    </task>
    <task id="LOCAL-00005" summary="feat：tabby-agent通信部分与core-agent对齐">
      <option name="closed" value="true" />
      <created>1743651115370</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1743651115370</updated>
    </task>
    <task id="LOCAL-00006" summary="tabby-agent返回格式是[]而不是{},对此优化">
      <option name="closed" value="true" />
      <created>1744004811154</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1744004811154</updated>
    </task>
    <task id="LOCAL-00007" summary="索引调通">
      <option name="closed" value="true" />
      <created>1744182670764</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1744182670764</updated>
    </task>
    <task id="LOCAL-00008" summary="forceReIndex参数对齐jetbrain">
      <option name="closed" value="true" />
      <created>1744188421130</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744188421130</updated>
    </task>
    <task id="LOCAL-00009" summary="修复初始框架的env名称不匹配问题">
      <option name="closed" value="true" />
      <created>1744247492697</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1744247492697</updated>
    </task>
    <task id="LOCAL-00010" summary="测试codefree的embed和rerank结果正常，getContextItems可以返回结果">
      <option name="closed" value="true" />
      <created>1744254082409</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1744254082409</updated>
    </task>
    <task id="LOCAL-00011" summary="【新增】支持本地开发环境登录；支持secidea/codefree开关">
      <option name="closed" value="true" />
      <created>1746697517753</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746697517753</updated>
    </task>
    <task id="LOCAL-00012" summary="【新增】支持本地开发环境登录；支持secidea/codefree开关">
      <option name="closed" value="true" />
      <created>1746698351835</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746698351835</updated>
    </task>
    <task id="LOCAL-00013" summary="【优化】对codefree和secidea的切换新增支持，复制assets、readme、package.json等资源。流水线需要修改.env内的ISSEC以及复制package-sec.json为package.json即可。（webview内的文字和图片替换还没开始）">
      <option name="closed" value="true" />
      <created>1746783025090</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746783025090</updated>
    </task>
    <task id="LOCAL-00014" summary="【改名】">
      <option name="closed" value="true" />
      <created>1746784221126</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1746784221126</updated>
    </task>
    <task id="LOCAL-00015" summary="【新增】开关切换时，顺便更新webview下的brand.json，该json后续用来切换logo和文本等公司信息。初始模板">
      <option name="closed" value="true" />
      <created>1746784791854</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1746784791854</updated>
    </task>
    <task id="LOCAL-00016" summary="【替换】新增brandStore，如果issec=true，则替换相关文字描述和logo。">
      <option name="closed" value="true" />
      <created>1747014879642</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747014879642</updated>
    </task>
    <task id="LOCAL-00017" summary="【bugfix】@文件时，如果webview窗口很窄。悬停到文件item时会在x轴出现overflow且选取不到想要的文件。修复此问题">
      <option name="closed" value="true" />
      <created>1747098668615</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1747098668615</updated>
    </task>
    <task id="LOCAL-00018" summary="【新增】代码补全上报字段新增：展示新增自动/手动+延时；接受新增自动/手动">
      <option name="closed" value="true" />
      <created>1747123535241</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1747123535241</updated>
    </task>
    <task id="LOCAL-00019" summary="【修改】插件包命名规则">
      <option name="closed" value="true" />
      <created>1747270814517</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1747270814517</updated>
    </task>
    <task id="LOCAL-00020" summary="【新增】if sec，展示左下角设置按钮，用于跳转sec的设置页面。代码补全相关变更的监听对接。todo：runCompletion时对变更设置的利用">
      <option name="closed" value="true" />
      <created>1747278399145</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1747278399145</updated>
    </task>
    <task id="LOCAL-00021" summary="【tabby-agent共建】插件端发送必要的调用，传递rag相关信息，port写死9999（要优化）">
      <option name="closed" value="true" />
      <created>1747893230782</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1747893230782</updated>
    </task>
    <task id="LOCAL-00022" summary="【预测补全】寻找适合的vscode api进行类似cursor的展示，基础版本">
      <option name="closed" value="true" />
      <created>1748400586129</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1748400586129</updated>
    </task>
    <task id="LOCAL-00023" summary="【预测补全】寻找适合的vscode api进行类似cursor的展示，基础版本">
      <option name="closed" value="true" />
      <created>1748410599503</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1748410599503</updated>
    </task>
    <task id="LOCAL-00024" summary="【预测补全】前后端联调 + 插件展示diff range + 删除线展示 demo">
      <option name="closed" value="true" />
      <created>1748942514603</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1748942514603</updated>
    </task>
    <task id="LOCAL-00025" summary="【预测补全】working demo">
      <option name="closed" value="true" />
      <created>1749003877829</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1749003877829</updated>
    </task>
    <task id="LOCAL-00026" summary="【预测补全】working demo">
      <option name="closed" value="true" />
      <created>1749022847298</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1749022847298</updated>
    </task>
    <task id="LOCAL-00027" summary="【预测补全】working demo">
      <option name="closed" value="true" />
      <created>1749434402464</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1749434402464</updated>
    </task>
    <task id="LOCAL-00028" summary="【预测补全】vscode基础版本">
      <option name="closed" value="true" />
      <created>1749438330364</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1749438330364</updated>
    </task>
    <task id="LOCAL-00029" summary="【预测补全】优化速度">
      <option name="closed" value="true" />
      <created>1749518785086</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1749518785086</updated>
    </task>
    <task id="LOCAL-00030" summary="【预测补全】vscode基础版">
      <option name="closed" value="true" />
      <created>1749535334525</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1749535334525</updated>
    </task>
    <task id="LOCAL-00031" summary="【修改】设置页面的补全配置变更，发送到tabby-agent内">
      <option name="closed" value="true" />
      <created>1750043601665</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1750043601665</updated>
    </task>
    <task id="LOCAL-00032" summary="【修复】补充遗漏的issec开关，解决永远使用codefree目录内的node二进制的问题">
      <option name="closed" value="true" />
      <created>1750668373014</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1750668373014</updated>
    </task>
    <task id="LOCAL-00033" summary="【修复】补充遗漏的issec开关，解决永远使用codefree目录内的node二进制的问题">
      <option name="closed" value="true" />
      <created>1750668949215</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1750668949215</updated>
    </task>
    <task id="LOCAL-00034" summary="【修复】tabby-agent的server url地址错误问题">
      <option name="closed" value="true" />
      <created>1750669004377</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1750669004377</updated>
    </task>
    <task id="LOCAL-00035" summary="【精简】keybinding无用逻辑移除">
      <option name="closed" value="true" />
      <created>1750835178619</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1750835178619</updated>
    </task>
    <task id="LOCAL-00036" summary="【优化】tabby-agent使用的serverUrl结尾的/trim掉">
      <option name="closed" value="true" />
      <created>1750835228186</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1750835228186</updated>
    </task>
    <task id="LOCAL-00037" summary="【优化】stopAll的null检查">
      <option name="closed" value="true" />
      <created>1750835254544</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1750835254544</updated>
    </task>
    <task id="LOCAL-00038" summary="【优化】设置页面补全快捷键设置的描述对齐jetbrain">
      <option name="closed" value="true" />
      <created>1750835369701</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1750835369701</updated>
    </task>
    <task id="LOCAL-00039" summary="【优化】设置按钮合并到状态栏里，与webview保持两套入口">
      <option name="closed" value="true" />
      <created>1750836739086</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1750836739086</updated>
    </task>
    <option name="localTasksCounter" value="40" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="9d6117e2-e05b-4474-a4cf-9597a64a2a66" value="TOOL_WINDOW" />
        <entry key="365782d6-20a8-4b2d-8561-e39e24d1952a" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/version_hya/c10_250430_01fd_3.5.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/feature/code-completion-optimize" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/version/v1.5.2-20250530" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="365782d6-20a8-4b2d-8561-e39e24d1952a">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="9d6117e2-e05b-4474-a4cf-9597a64a2a66">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="测试codefree的embed和rerank结果正常，getContextItems可以返回结果" />
    <MESSAGE value="【新增】支持本地开发环境登录；支持secidea/codefree开关" />
    <MESSAGE value="【优化】对codefree和secidea的切换新增支持，复制assets、readme、package.json等资源。流水线需要修改.env内的ISSEC以及复制package-sec.json为package.json即可。（webview内的文字和图片替换还没开始）" />
    <MESSAGE value="【改名】" />
    <MESSAGE value="【新增】开关切换时，顺便更新webview下的brand.json，该json后续用来切换logo和文本等公司信息。初始模板" />
    <MESSAGE value="【替换】新增brandStore，如果issec=true，则替换相关文字描述和logo。" />
    <MESSAGE value="【bugfix】@文件时，如果webview窗口很窄。悬停到文件item时会在x轴出现overflow且选取不到想要的文件。修复此问题" />
    <MESSAGE value="【新增】代码补全上报字段新增：展示新增自动/手动+延时；接受新增自动/手动" />
    <MESSAGE value="【修改】插件包命名规则" />
    <MESSAGE value="【新增】if sec，展示左下角设置按钮，用于跳转sec的设置页面。代码补全相关变更的监听对接。todo：runCompletion时对变更设置的利用" />
    <MESSAGE value="【tabby-agent共建】插件端发送必要的调用，传递rag相关信息，port写死9999（要优化）" />
    <MESSAGE value="【预测补全】寻找适合的vscode api进行类似cursor的展示，基础版本" />
    <MESSAGE value="【预测补全】前后端联调 + 插件展示diff range + 删除线展示 demo" />
    <MESSAGE value="【预测补全】working demo" />
    <MESSAGE value="【预测补全】vscode基础版本" />
    <MESSAGE value="【预测补全】优化速度" />
    <MESSAGE value="【预测补全】vscode基础版" />
    <MESSAGE value="【修改】设置页面的补全配置变更，发送到tabby-agent内" />
    <MESSAGE value="【修复】补充遗漏的issec开关，解决永远使用codefree目录内的node二进制的问题" />
    <MESSAGE value="【修复】tabby-agent的server url地址错误问题" />
    <MESSAGE value="【精简】keybinding无用逻辑移除" />
    <MESSAGE value="【优化】tabby-agent使用的serverUrl结尾的/trim掉" />
    <MESSAGE value="【优化】stopAll的null检查" />
    <MESSAGE value="【优化】设置页面补全快捷键设置的描述对齐jetbrain" />
    <MESSAGE value="【优化】设置按钮合并到状态栏里，与webview保持两套入口" />
    <option name="LAST_COMMIT_MESSAGE" value="【优化】设置按钮合并到状态栏里，与webview保持两套入口" />
  </component>
</project>