// Place your settings in this file to overwrite default and user settings.
{
  "files.exclude": {
    "out": false, // set this to true to hide the "out" folder with the compiled JS files
    "dist": false // set this to true to hide the "dist" folder with the compiled JS files
  },
  "search.exclude": {
    "out": true, // set this to false to include "out" folder in search results
    "dist": true // set this to false to include "dist" folder in search results
  },
  // Turn off tsc task auto detection since we have the necessary tasks as npm scripts
  "typescript.tsc.autoDetect": "off",
  "editor.formatOnPaste": true,
  "editor.formatOnSave": false,
  "editor.formatOnType": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "eslint.validate": [
    "javascript",
    "typescript",
    "json",
    "vue"
  ],
  "eslint.format.enable": true,
  "eslint.run": "onType",
  "prettier.configPath": ".prettierrc",
  "editor.tabSize": 2,
  "editor.renderWhitespace": "all",
  "editor.rulers": [
    100
  ],
  "files.eol": "\n",
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
}