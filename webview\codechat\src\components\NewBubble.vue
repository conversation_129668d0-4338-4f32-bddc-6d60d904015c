<template>
  <div class="chat-item">
    <UserBubble :msg="`what's new?`"></UserBubble>
    <CodeLoadingBubble :content="helpMsg" :logo=true></CodeLoadingBubble>
  </div>
</template>
<script lang='ts' setup>
import UserBubble from '@/components/UserBubble.vue';
import CodeLoadingBubble from '@/components/CodeLoadingBubble.vue';
import { computed, onMounted } from 'vue'
import { useChatStore } from '@/store/chat';
const chatStore = useChatStore()

const helpMsg = computed(()=> {
  return `${chatStore.config.clientVersion}版本更新内容:\n` +
    chatStore.config.versionDesc
})

onMounted(() => { 
  const version = chatStore.config.clientVersion
  localStorage.setItem('__new__' + version, '1')
})
</script>
<style lang="scss">
</style>