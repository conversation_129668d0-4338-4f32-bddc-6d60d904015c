<template>
  <div class="guide-page-root">
    <LogoView />

    <div style="padding: 0 20px;">
      <DescriptionView v-if="chatStore.codeFreeLoginStatus"/>
      <div v-else class="unlogin-bubble">
        <CodeLoadingBubble style="border-radius: 4px;padding-top: 5px;" :silent="true" v-if="!chatStore.codeFreeLoginStatus" :content="ChatTips
        .LOGIN_MSG" :logo="false" />
      </div>
    </div>

  </div>
</template>

<script lang="js" setup>
import LogoView from './LogoView.vue';
import DescriptionView from './DescriptionView.vue'
import CodeLoadingBubble from '@/components/CodeLoadingBubble.vue';
import { useChatStore } from '@/store/chat';
import { ChatTips } from '@/constants/common';

const chatStore = useChatStore();

</script>

<style lang="scss">
.guide-page-root {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  .unlogin-bubble {
    background-color: var(--vscode-list-inactiveSelectionBackground);
    border-radius: 4px;
  }

}
</style>