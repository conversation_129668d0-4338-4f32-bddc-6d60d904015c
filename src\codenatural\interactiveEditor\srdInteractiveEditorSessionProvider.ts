import {
  Can<PERSON><PERSON>Token,
  InteractiveEditorMessageResponse,
  InteractiveEditorProgressItem,
  InteractiveEditorReplyFollowup,
  InteractiveEditorRequest,
  InteractiveEditorResponseFeedbackKind,
  InteractiveEditorSession,
  InteractiveEditorSessionProvider,
  Progress,
  ProviderResult,
  TextDocumentContext,
  window,
  Range,
  MarkdownString,
  Position,
  commands,
} from 'vscode';
import {
  SrdInteractiveEditorResponse,
  SrdInteractiveEditorSession,
  AnswerEndParams,
} from './srdInteractiveEditorTypes';
import { CodeNaturalDataType, CodeNaturalEventType, ICodeNaturalEventHandler } from '../types';
import { once, EventEmitter } from 'events';
import { Logger } from '../../utils/logger';
import CodeNaturalTask from '../codeNaturalTask';
import { SrdCommand } from '../../common/constants';

const EDITOR_SESSION_ANSWER_END = 'editor_session_answer_end';

export default class SrdInteractiveEditorSessionProvider<
  S extends InteractiveEditorSession,
  R extends InteractiveEditorMessageResponse
> implements InteractiveEditorSessionProvider
{
  private eventHandler: ICodeNaturalEventHandler;

  private onDidAnswerEnd = new EventEmitter();

  public constructor(eventHandler: ICodeNaturalEventHandler) {
    this.eventHandler = eventHandler;
  }

  public handleOnDidAnswerEnd(params: AnswerEndParams) {
    this.onDidAnswerEnd.emit(EDITOR_SESSION_ANSWER_END, params);
  }

  public prepareInteractiveEditorSession(
    context: TextDocumentContext,
    token: CancellationToken
  ): ProviderResult<InteractiveEditorSession> {
    Logger.debug(`[SrdInteractiveEditorSessionProvider] prepareInteractiveEditorSession`);
    const editor = window.activeTextEditor;

    if (editor?.document.uri.toString() !== context.document.uri.toString()) {
      return;
    }

    return new SrdInteractiveEditorSession(
      editor,
      context.document,
      '请输入问题',
      'CodeFree生成的回答有可能不符合您的期待'
    );
  }

  public async provideInteractiveEditorResponse(
    session: SrdInteractiveEditorSession,
    request: InteractiveEditorRequest,
    progress: Progress<InteractiveEditorProgressItem>,
    token: CancellationToken
  ): Promise<InteractiveEditorMessageResponse | undefined> {
    Logger.debug(
      `[SrdInteractiveEditorSessionProvider] provideInteractiveEditorResponse, session:${JSON.stringify(
        session.slashCommands
      )}`
    );

    this.eventHandler.handleEvent(CodeNaturalEventType.ADD_TASK, {
      editor: session.editor,
      input: request.prompt,
    });

    progress.report({});

    const answerEndParams = await this.waitingCodeNaturalAnswerEnd();
    const task = this.eventHandler.getEventData(
      CodeNaturalDataType.TASK,
      session.document
    ) as CodeNaturalTask;

    if (!task || task.getReqId() !== answerEndParams.reqId) {
      return;
    }

    const startPosition = task.getStartPosition() as Position;
    return new SrdInteractiveEditorResponse(
      new MarkdownString(),
      new Range(startPosition, startPosition)
    );
  }

  public handleInteractiveEditorResponseFeedback?(
    session: SrdInteractiveEditorSession,
    response: SrdInteractiveEditorResponse,
    kind: InteractiveEditorResponseFeedbackKind
  ): void {
    Logger.debug(
      `[SrdInteractiveEditorSessionProvider] handleInteractiveEditorResponseFeedback, kind:${kind}`
    );

    const task = this.eventHandler.getEventData(
      CodeNaturalDataType.TASK,
      session.document
    ) as CodeNaturalTask;

    if (!task) {
      return;
    }

    switch (kind) {
      case InteractiveEditorResponseFeedbackKind.Accepted:
        commands.executeCommand(SrdCommand.ACCEPT_CODE_NATURAL_ANSWER, task);
        break;
      case InteractiveEditorResponseFeedbackKind.Undone:
        commands.executeCommand(SrdCommand.REJECT_CODE_NATURAL_ANSWER, task);
        break;
      default:
        break;
    }
  }

  public provideFollowups?(
    session: SrdInteractiveEditorSession,
    response: SrdInteractiveEditorResponse,
    token: CancellationToken
  ): ProviderResult<InteractiveEditorReplyFollowup[]> {
    return;
  }

  /**
   * 等待回答执行结束
   */
  private async waitingCodeNaturalAnswerEnd(): Promise<AnswerEndParams> {
    return (await once(this.onDidAnswerEnd, EDITOR_SESSION_ANSWER_END))[0];
  }
}
