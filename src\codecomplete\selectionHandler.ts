import {
  CodeAction,
  CodeActionKind,
  commands,
  Selection,
  TextEditor,
  TextEditorEdit,
  workspace,
} from 'vscode';
import findImports from './findImports';
import { CompletionArguments } from './types';
import { DELAY_FOR_CODE_ACTION_PROVIDER } from '../common/config';
import { SrdCommand } from '../common/constants';
import { Logger } from '../utils/logger';
import { DataReportServiceInst } from '../service/dataReportService';
import { ActivityType } from '../service/types/dataReport';

export function selectionHandler(
  editor: TextEditor,
  _edit: TextEditorEdit,
  { currentCompletion, currentAnswer, suggestionTrigger, oldPrefix, isAuto }: CompletionArguments
): void {
  try {
    Logger.debug(
      `[selectionHandler]: currentCompletion: ${currentAnswer}, suggestionTrigger:${suggestionTrigger}`
    );

    // 上报代码补全被接受情况
    DataReportServiceInst.notifyUserActivity(
      ActivityType.CODE_COMPLETION_ACCEPTED,
      currentAnswer,
      oldPrefix,
      isAuto
    );

    void commands.executeCommand(SrdCommand.HANDLE_IMPORTS, {
      completion: currentCompletion,
    });
  } catch (error) {
    Logger.error(`[selectionHandler]: ${error}`);
  }
}

export function handleImports(
  editor: TextEditor,
  edit: TextEditorEdit,
  { completion }: { completion: string }
): void {
  const lines = completion.split('\n');

  const { selection } = editor;
  const completionSelection = new Selection(
    selection.active.translate(
      -(lines.length - 1),
      lines.length > 1 ? -selection.active.character : -completion.length
    ),
    selection.active
  );
  setTimeout(() => {
    void doAutoImport(editor, completionSelection, completion);
  }, DELAY_FOR_CODE_ACTION_PROVIDER);
}

async function doAutoImport(
  editor: TextEditor,
  completionSelection: Selection,
  completion: string
) {
  try {
    const codeActionCommands = await commands.executeCommand<CodeAction[]>(
      'vscode.executeCodeActionProvider',
      editor.document.uri,
      completionSelection,
      CodeActionKind.QuickFix.value
    );
    const importCommand = findImports(codeActionCommands)[0];

    if (importCommand && importCommand.edit) {
      await workspace.applyEdit(importCommand.edit);
      await commands.executeCommand(SrdCommand.HANDLE_IMPORTS, { completion });
    }
  } catch (error) {
    Logger.error(`[doAutoImport]: ${error}`);
  }
}
