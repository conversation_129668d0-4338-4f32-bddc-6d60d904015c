import { AgentManager } from '../agent/agentmanager';
import { Logger } from '../utils/logger';
import { getProjectInfo } from '../utils/textEditor';

export class CodeIndexService {
  private static instance: CodeIndexService | null = null;

  private status: 'idle' | 'running' = 'idle';

  private reindexInterval = 0;

  private reindexTimer: NodeJS.Timeout | null = null;

  private readonly agentManager: AgentManager;

  private constructor() {
    this.agentManager = AgentManager.getInstance();
  }

  public static getInstance(): CodeIndexService {
    if (CodeIndexService.instance === null) {
      CodeIndexService.instance = new CodeIndexService();
    }
    return CodeIndexService.instance;
  }

  /**
   * 启动索引进程并安排定期重新索引
   * @param intervalMinutes 重新索引操作之间的间隔时间（分钟）
   */
  public startIndexing(intervalMinutes: number): void {
    this.reindexInterval = intervalMinutes;
    this.triggerReindex();
  }

  /**
   * 处理来自核心代理的响应
   * @param data 来自核心代理的响应数据
   */
  public onResponse(data: any): void {
    try {
      const message = JSON.parse(data);
      if (message.messageType === 'indexProgress') {
        const progress = parseFloat(message.data.progress);
        const status = message.data.status;
        const desc = message.data.desc;

        Logger.debug(`Index progress: ${progress}, Status: ${status}, Description: ${desc}`);

        const progressPercent = Math.round(progress * 100);
        const agentManager = AgentManager.getInstance();

        if (status === 'done' || status === 'failed' || status === 'disabled') {
          Logger.info(`[CodeIndexService] Indexing complete, status: ${status}`);
          agentManager.onIndexingComplete();
          this.status = 'idle';
          this.scheduleNextReindex();
        } else {
          agentManager.updateIndexingProgress(progressPercent);
        }
      }
    } catch (error: any) {
      Logger.error(`Error handling agent message: ${JSON.stringify(error)}`);
    }
  }

  /**
   * 获取当前索引状态
   * @returns 索引进程的当前状态
   */
  public getStatus(): 'idle' | 'running' {
    return this.status;
  }

  /**
   * 停止索引服务
   */
  public stop(): void {
    if (this.reindexTimer) {
      clearTimeout(this.reindexTimer);
      this.reindexTimer = null;
    }
    this.status = 'idle';
  }

  /**
   * 触发重新索引操作
   */
  private triggerReindex(): void {
    if (this.status === 'running') {
      Logger.warn('Indexing is already in progress');
      return;
    }

    const coreAgentClient = this.agentManager.getAgentCommClient('core');
    if (!coreAgentClient) {
      Logger.error('Core agent client not found');
      return;
    }

    this.status = 'running';
    coreAgentClient.request('index/forceReIndex', getProjectInfo()?.projectPath, null, this.onResponse);
  }

  /**
   * 安排下一次重新索引操作
   */
  private scheduleNextReindex(): void {
    if (this.reindexTimer) {
      clearTimeout(this.reindexTimer);
    }

    this.reindexTimer = setTimeout(() => {
      this.triggerReindex();
      //this.scheduleNextReindex();
    }, this.reindexInterval * 60 * 1000);
  }
}
