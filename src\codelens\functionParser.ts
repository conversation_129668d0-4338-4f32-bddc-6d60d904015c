import * as vscode from 'vscode';

/**
 * 通过正则的方式匹配不同语言的函数声明第一行
 * 函数声明在多行的情形可能不适用
 * 经过简单用例测试，后续可持续优化
 */
export const languageRegexes: { [key: string]: RegExp } = {
    'javascript': /function\s+(\w+)\s*\([^)]*\)\s*{|(\w+)\s*=\s*function\s*\([^)]*\)\s*{|(\w+)\s*:\s*function\s*\([^)]*\)\s*{|\(.*\)\s*=>\s*{/g,
    'typescript': /function\s+(\w+)\s*\([^)]*\)\s*{|(\w+)\s*=\s*function\s*\([^)]*\)\s*{|(\w+)\s*:\s*function\s*\([^)]*\)\s*{|\(.*\)\s*=>\s*{|(\w+)\s*\([^)]*\):\s*\w+\s*{/g,
    'java': /(?:(?:public|protected|private|static) )+(?!return\b)[\w\<\>\[\]]+\s+(\w+) *\([^\)]*\) *(?:\{?|[^;])/g,
    'python': /def\s+(\w+)\s*\([^\)]*\)\s*:/g,
    'csharp': /(?:(?:public|protected|private|static) )+[\w\<\>\[\]]+\s+(\w+)\s*\([^\)]*\)\s*(?=\{)/g,
    'php': /function\s+(\w+)\s*\([^\)]*\)\s*{/g,
    'ruby': /[ \t]*def\s+(\w+)(\s*\([^\)]*\))?/g,
    'go': /func\s+(\w+)\s*\([^\)]*\)\s*(?:[\(]?.*[\)]?)?\s*{/g,
    'rust': /fn\s+(\w+)\s*\([^\)]*\)\s*(?:->\s*\w+)?\s*{/g,
    'swift': /func\s+(\w+)\s*\([^\)]*\)\s*(?:->\s*\w+)?\s*{/g,
    'kotlin': /fun\s+(\w+)\s*\([^\)]*\)\s*(?::\s*\w+)?\s*{/g,
    'scala': /def\s+(\w+)\s*\([^\)]*\)\s*(?::\s*\w+)?\s*=\s*{/g,
    'haskell': /(\w+)\s*::.+\n\1\s+(?:\w+\s.*?)?=/g,
    'r': /(\w+)\s*<-\s*function\s*\(/g,
    'matlab': /function\s+(?:\[?[\w,\s]+\]?\s*=\s*)?(\w+)\s*\(/g,
    'perl': /sub\s+(\w+)\s*{/g,
    'lua': /function\s*(\w+)\s*\([^\)]*\)/g,
    'dart': /(?:void\s+)?(\w+)\s*\([^\)]*\)\s*(?:async\s*)?\{/g,
    'Julia': /function\s+(\w+)\s*\([^\)]*\)/g,
    'powershell': /[fF]unction\s+([\w-]+)\s*(?:\([^\)]*\))?\s*{/g,
    'groovy': /(?:def\s+)?(\w+)\s*\([^\)]*\)\s*{/g,
    'objective-c': /[-+]\s*(\([^\)]+\))?\s*([\w_]*)(?:\s*:\s*\([^\)]+\)\s*[\w_]+).*?\{/g,
    'vb': /(?:Public\s+|Private\s+)?(?:Function|Sub)\s+(\w+)\s*\([^\)]*\)/gi,
    'sql': /CREATE\s+FUNCTION\s+(\w+)\s*\(/gmi,
    'f#': /let\s+(\w+)(?:\s+[^=]+)?\s*=\s*$/gm,
    'clojure': /\(defn\s+(\w+)/g,
    'elixir': /def\s+(\w+)\s*(?:\([^\)]*\))?.*do/g,
    'erlang': /^ *(\w+)\((?:[^)]*)\) *-> *.*$/gm,
    'lisp': /\(defun\s+(\w+)/g,
    'prolog': /(\w+)\s*\([^\)]*\)\s*:-/g
};

/**
 * 解析大部分语言的函数体
 * @param document 
 * @param startPos 
 * @returns 
 */
export function getFullFunctionRange(document: vscode.TextDocument, startPos: vscode.Position): vscode.Range | null {
    switch (document.languageId) {
        case 'javascript':
        case 'typescript':
        case 'java':
        case 'csharp':
        case 'php':
        case 'go':
        case 'rust':
        case 'swift':
        case 'kotlin':
        case 'scala':
        case 'dart':
        case 'groovy':
        case 'objective-c':
        case 'perl':
        case 'r':
        case 'powershell':
            return getBraceFunctionRange(document, startPos);
        case 'python':
        case 'haskell':
        case 'julia':
        case 'fsharp':
        case 'elixir':
        case 'ruby':
        case 'matlab':
        case 'lua':
        case 'f#':
            return getIndentBasedFunctionRange(document, startPos);
        case 'vb':
            return getKeywordEndedFunctionRange(document, startPos, ['End Function', 'End Sub']);
        case 'sql':
            return getKeywordEndedFunctionRange(document, startPos, ['END', '\\$\\$']);
        case 'clojure':
        case 'lisp':
            return getParenBasedFunctionRange(document, startPos);
        case 'erlang':
        case 'prolog':
            return getClauseBasedFunctionRange(document, startPos);
        default:
            console.log(`[secidea] Unsupported language: ${document.languageId}`);
            return null;
    }
}

function getBraceFunctionRange(document: vscode.TextDocument, startPos: vscode.Position): vscode.Range | null {
    const text = document.getText();
    let braceCount = 0;
    let endPos = startPos;
    var inString = false;
    var stringChar = ''

    for (let i = document.offsetAt(startPos); i < text.length; i++) {
        const char = text[i];

        // 跳过string中的字符
        if (char === '"' || char === '\'') {
            if (!inString) {
                inString = true;
                stringChar = char;
            } else if (char === stringChar) {
                inString = false;
            }
        }

        if (inString) {
            continue;
        }

        if (char === '{') {
            braceCount++;
        } else if (char === '}') {
            braceCount--;
            if (braceCount === 0) {
                endPos = document.positionAt(i + 1);
                break;
            }
        }
    }

    if (braceCount !== 0) {
        console.log('[secidea] Failed to find function end');
        return null;
    }

    return new vscode.Range(startPos, endPos);
}

function getKeywordEndedFunctionRange(document: vscode.TextDocument, startPos: vscode.Position, endKeywords: string[]): vscode.Range | null {
    const text = document.getText();
    const lines = text.split('\n');
    const startLine = startPos.line;
    let endLine = startLine;
    let bracketCount = 0;

    // 查找函数的开始
    let functionStart = startPos;
    if (!functionStart) {
        return null;
    }

    // 从函数开始的下一行开始查找
    for (let i = functionStart.line + 1; i < lines.length; i++) {
        const line = lines[i].trim().toLowerCase();

        // 检查括号平衡（针对可能的嵌套结构）
        bracketCount += (line.split('(').length - 1) - (line.split(')').length - 1);

        // 检查是否遇到结束关键字

        // 使用 "|" 作为分隔符，并在字符串的前后添加方括号
        const assembledString = `(${endKeywords.join('|')})`;

        const regex = new RegExp(`^\\W*${assembledString}\\W*$`, 'gi');
          

        if (regex.test(line) && bracketCount === 0) {
            endLine = i;
            break;
        }

        // 检查是否遇到新的函数定义
        if (line.search(languageRegexes[document.languageId]) !== -1 && bracketCount === 0) {
            endLine = i - 1;
            break;
        }
    }

    // 如果没有找到结束关键字
    if (endLine === startLine) {
        console.log('[secidea] Failed to find function end');
        return null;
    }

    // 返回函数范围
    return new vscode.Range(
        functionStart,
        new vscode.Position(endLine, lines[endLine].length)
    );
}

function getClauseBasedFunctionRange(document: vscode.TextDocument, startPos: vscode.Position): vscode.Range | null {
    const text = document.getText();
    const lines = text.split('\n');
    const startLine = startPos.line;
    let endLine = startLine;
    let functionName: string | null = null;

    // 查找函数的开始
    let functionStart = startPos;
    if (!functionStart) {
        return null;
    }

    // 提取函数名
    const startLineText = lines[functionStart.line];
    const nameMatch = startLineText.match(languageRegexes[document.languageId]);
    if (nameMatch && nameMatch[1]) {
        functionName = nameMatch[1];
    } else {
        console.log('[secidea] Failed to extract function name');
        return null;
    }

    // 从函数开始的下一行开始查找
    for (let i = functionStart.line + 1; i < lines.length; i++) {
        const line = lines[i].trim();

        // 对于 Prolog
        if (document.languageId === 'prolog') {
            // 检查是否是新的子句（以函数名开头）或新的谓词定义
            if (line.startsWith(functionName) || line.match(languageRegexes['erlang']) !== null) {
                endLine = i - 1;
                break;
            }
            // 检查是否是子句结束（以 '.' 结尾）
            if (line.endsWith('.')) {
                endLine = i;
                // 检查下一行是否是新的子句或新的谓词定义
                const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
                if (!nextLine.startsWith(functionName) && nextLine.match(languageRegexes['prolog']) !== null) {
                    break;
                }
            }
        }
        // 对于 Erlang
        else if (document.languageId === 'erlang') {
            // 检查是否是新的函数定义
            if (line.match(languageRegexes['erlang']) !== null) {
                endLine = i - 1;
                break;
            }
            // 检查是否是函数结束（以 '.' 结尾）
            if (line.endsWith('.')) {
                endLine = i;
                // 检查下一行是否是新的函数定义
                const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
                if (nextLine.match(languageRegexes['erlang']) !== null) {
                    break;
                }
            }
        }
    }

    // 处理函数定义在文件末尾的情况
    if (endLine === lines.length - 1) {
        // 如果是最后一行，包括它
        return new vscode.Range(
            functionStart,
            new vscode.Position(endLine, lines[endLine].length)
        );
    }

    // 否则，范围到下一行的开始
    return new vscode.Range(
        functionStart,
        new vscode.Position(endLine + 1, 0)
    );
}

function getParenBasedFunctionRange(document: vscode.TextDocument, startPos: vscode.Position): vscode.Range | null {
    const text = document.getText();
    const lines = text.split('\n');
    const startLine = startPos.line;
    let endLine = startLine;
    let parenCount = 0;
    let foundStart = false;

    // 查找函数的开始
    let functionStart = startPos;

    // 从函数开始位置开始扫描
    for (let i = functionStart.line; i < lines.length; i++) {
        const line = lines[i];

        for (let j = 0; j < line.length; j++) {
            const char = line[j];

            // 如果是函数开始的那一行，只在找到第一个左括号后才开始计数
            if (i === functionStart.line && !foundStart) {
                if (char === '(') {
                    foundStart = true;
                    parenCount++;
                }
                continue;
            }

            if (char === '(') {
                parenCount++;
            } else if (char === ')') {
                parenCount--;

                // 如果括号匹配完成，函数定义结束
                if (parenCount === 0) {
                    endLine = i;
                    return new vscode.Range(
                        functionStart,
                        new vscode.Position(endLine, j + 1)
                    );
                }
            }
        }
    }

    // 如果到文件末尾都没有找到匹配的括号，返回null
    console.log('[secidea] Failed to find function end');
    return null;
}

function getIndentBasedFunctionRange(document: vscode.TextDocument, startPos: vscode.Position): vscode.Range | null {
    const text = document.getText();
    const lines = text.split('\n');
    const startLine = startPos.line;
    let endLine = startLine;
    const startIndent = getIndentationLevel(lines[startLine]);

    // 查找函数的开始，包括装饰器
    let functionStart = startPos;

    let inMultiLineString = false;
    let stringDelimiter = '';

    for (let i = functionStart.line + 1; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        // 处理多行字符串
        if (inMultiLineString) {
            if (trimmedLine.endsWith(stringDelimiter)) {
                inMultiLineString = false;
            }
            endLine = i;
            continue;
        }

        // 检查是否开始多行字符串
        if (trimmedLine.startsWith('"""') || trimmedLine.startsWith("'''")) {
            inMultiLineString = true;
            stringDelimiter = trimmedLine.substring(0, 3);
            endLine = i;
            continue;
        }

        // 跳过空行和注释
        if (trimmedLine === '' || trimmedLine.startsWith('#')) {
            endLine = i;
            continue;
        }

        const currentIndent = getIndentationLevel(line);

        // 检查是否遇到了下一个函数定义或类定义
        if (currentIndent <= startIndent && (trimmedLine.startsWith('def ') || trimmedLine.startsWith('class '))) {
            endLine = i - 1;
            break;
        }

        // 当缩进级别回到与函数定义相同或更低时，认为函数结束
        if (currentIndent <= startIndent) {
            // 允许同级的装饰器
            if (!trimmedLine.startsWith('@')) {
                endLine = i - 1;
                break;
            }
        }

        endLine = i;
    }

    let lastNonCommentLine = endLine;

    // 回溯以找到最后一个非注释、非空行
    for (let i = endLine; i >= startLine; i--) {
        const line = lines[i].trim();
        if (line !== '' && !line.startsWith('#')) {
            lastNonCommentLine = i;
            break;
        }
    }


    // 如果语言为ruby/matlab/lua/，向下找到其中有end的行，作为行的结尾
    if (['ruby', 'matlab', 'lua', 'julia', 'elixir'].includes(document.languageId)) {
        for (let i = lastNonCommentLine + 1; i < lines.length; i++) {
            const line = lines[i].trim().toLowerCase();
            if (line === 'end' || line.startsWith('end ')) {
                lastNonCommentLine = i;
                break;
            }
        }
    }

    return new vscode.Range(functionStart, new vscode.Position(lastNonCommentLine + 1, 0));
}

function getIndentationLevel(line: string): number {
    const match = line.match(/^(\s*)/);
    return match ? match[1].length : 0;
}