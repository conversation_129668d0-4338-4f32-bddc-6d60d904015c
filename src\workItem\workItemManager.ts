import * as vscode from 'vscode';
import { getGitRepoSignature, getCurrentUser } from '../common/globalContext';
import HttpClient from '../commclient/httpClient';
import { WORK_ITEM_PATH } from '../common/config';
import { HttpError } from '../commclient/types/http';
import { WorkItem, WorkItemEventType, WebViewRspCommand } from './types';
import { WorkItemViewProvider } from './workItemViewProvider';
import * as fs from 'fs';
import { Logger } from '../utils/logger';
import { ChangeItem, ICommitMessageHandler } from './types';
import { CommitAnalysisService } from '../service/commitAnalysisService';
import { CodeAIResponsePayload } from '../service/types/codeAI';
import { IsAnswerEnd, RtnCode, RtnMessage } from '../common/constants';
import * as diff from 'diff';
import ChatViewProvider from '../codechat/chatViewProvider';
import { getCodeAIConfig } from '../common/globalContext';
import * as path from 'path';

// Define a set of common binary file extensions
const BINARY_EXTENSIONS = new Set([
  '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico', '.webp', // Images
  '.zip', '.tar', '.gz', '.rar', '.7z', '.jar', '.war', '.ear', // Archives & Packages
  '.pdf', // Documents
  '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', // MS Office (often treated as binary)
  '.exe', '.dll', '.so', '.dylib', '.app', // Executables & Libraries
  '.o', '.a', '.lib', '.obj', // Compiled objects/libraries
  '.class', // Java class files
  '.pyc', '.pyo', // Python compiled files
  '.mp3', '.wav', '.ogg', '.aac', // Audio
  '.mp4', '.mov', '.avi', '.mkv', '.webm', // Video
  '.sqlite', '.db', // Databases
  '.ttf', '.otf', '.woff', '.woff2', // Fonts
  '.DS_Store'
]);

export class WorkItemManager implements ICommitMessageHandler {
  private provider: WorkItemViewProvider | ChatViewProvider;
  private commitAnalysisService: CommitAnalysisService;
  private curAllAnswer = ''; // Track accumulated answer
  private gitRepo: any = null; // Store Git repository reference
  private currentWorkItemList: WorkItem[] | [] = []; // Track current work item
  private isAnalysisRunning = false; // Track analysis state

  constructor(provider: WorkItemViewProvider | ChatViewProvider) {
    this.provider = provider;
    this.commitAnalysisService = new CommitAnalysisService(this);
  }

  public async searchWorkItems(searchParam: string) {
    await this.getWorkItems(searchParam);
  }

  public async getWorkItems(searchParam?: string) {
    const projectCode = getGitRepoSignature();
    //暂时用假的projectCode
    // const projectCode = 'testleiy/ide1';
    
    if (!projectCode) {
      this.sendWorkItemResponse([], '当前工程未关联研发云项目，无法获取工作项');
      return;
    }

    const httpClient = new HttpClient();
    const uInfo = await getCurrentUser();

    try {
      const queryParams: Record<string, string> = { projectCode };
      
      // 如果有传入 searchParam，就添加到查询参数
      if (searchParam) {
        queryParams.searchText = searchParam;
      }

      const response = await httpClient.requestByGet<WorkItem[]>(
        WORK_ITEM_PATH, 
        queryParams, 
        {
          headers: {
            invokerId: uInfo.userId as string,
            apiKey: uInfo.apiKey as string,
            contentType: 'application/json',
          },
        }
      );

      if (response instanceof HttpError) {
        this.sendWorkItemResponse([], '获取工作项失败，请重试');
        return;
      }

      const workItems = response;
      if (workItems?.length === 0) {
        this.sendWorkItemResponse([], '当前项目不存在指派给你的工作项');
        return;
      }

      // 成功获取工作项
      this.sendWorkItemResponse(workItems);
      
    } catch (error) {
      this.sendWorkItemResponse([], '获取工作项失败，请重试');
    }
  }

  /**
   * 发送工作项响应的统一方法
   */
  private sendWorkItemResponse(workItemList: WorkItem[] = [], error?: string) {
    this.provider.handleWorkItemEvent({
      command: WebViewRspCommand.WORKITEM_RESPONSE,
      data: {
        reqType: WorkItemEventType.SEARCH_WORKITEMS,
        workItemList,
        error
      }
    });
  }

  public async selectWorkItem(workItemList: WorkItem[] | []) {
    const gitExtension = vscode.extensions.getExtension('vscode.git')?.exports;
    let errorMsgForUser: string | undefined;

    if (gitExtension) {
      const git = gitExtension.getAPI(1);
      if (git && git.repositories.length > 0) {
        const repo = git.repositories[0];
        this.gitRepo = repo; // Store reference to repo
        this.currentWorkItemList = workItemList; // Store reference to work item list
        try {
          // Get file changes first
          const fileChanges = await this.getGitChangesWithCommand();
          if (fileChanges.length === 0) {
            vscode.window.showErrorMessage('提交内容不能为空');
            this.provider.handleWorkItemEvent({
              command: WebViewRspCommand.WORKITEM_RESPONSE,
              data: {
                reqType: WorkItemEventType.ANSWER_RECVED,
                isEnd: 1
              }
            });
            return;
          }
          // Set initial commit message
          // This ensures it's set before any streaming responses arrive
          if (workItemList && workItemList.length > 0) {
            // Generate prefix for multiple work items
            const workItemKeys = workItemList.map(item => `%${item.workItemKey}`).join('');
            repo.inputBox.value = workItemKeys;
          } else {
            repo.inputBox.value = ''; // Set to empty if no work item
          }
          
          // Set analysis running state
          this.setAnalysisRunning(true);
          await this.sendCommitInfoToBackend(workItemList, fileChanges);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : String(err);
          errorMsgForUser = `Failed to process commit: ${errorMessage}`; 
          Logger.error(`[WorkItemManager] Error in selectWorkItem: ${errorMessage}`);
          this.setAnalysisRunning(false);
          this.provider.handleWorkItemEvent({
            command: WebViewRspCommand.WORKITEM_RESPONSE,
            data: {
              reqType: WorkItemEventType.ANSWER_RECVED,
              isEnd: 1,
            }
          });
          return;
        }
      } else {
        errorMsgForUser = "未找到 Git 仓库"; 
        Logger.error(`[WorkItemManager] Error in selectWorkItem: ${errorMsgForUser}`);
        this.provider.handleWorkItemEvent({
            command: WebViewRspCommand.WORKITEM_RESPONSE,
            data: {
                reqType: WorkItemEventType.ANSWER_RECVED,
                isEnd: 1,
            }
        });
        return;
      }
    } else {
      errorMsgForUser = "Git 扩展未启用"; 
      Logger.error(`[WorkItemManager] Error in selectWorkItem: ${errorMsgForUser}`)
      this.provider.handleWorkItemEvent({
          command: WebViewRspCommand.WORKITEM_RESPONSE,
          data: {
              reqType: WorkItemEventType.ANSWER_RECVED,
              isEnd: 1,
          }
      });
      return;
    }

    this.provider.handleWorkItemEvent({
      command: WebViewRspCommand.WORKITEM_RESPONSE,
      data: {
        reqType: WorkItemEventType.SELECT_WORKITEM,
        error: errorMsgForUser 
      }
    });
  }

  /**
   * Stop the current commit analysis
   */
  public stopCommitAnalysis(): void {
    if (this.isAnalysisRunning) {
      this.commitAnalysisService.stopAnalysis();
    }
  }

  /**
   * Check if analysis is currently running
   */
  public isCommitAnalysisRunning(): boolean {
    return this.isAnalysisRunning;
  }

  public openPanel() :void {
    vscode.commands.executeCommand('srd-copilot.workItemList.focus')
  }

  /**
   * Set analysis running state and update context
   */
  private setAnalysisRunning(running: boolean): void {
    this.isAnalysisRunning = running;
    vscode.commands.executeCommand('setContext', 'srd-copilot.commitAnalysisRunning', running);
  }

  /**
   * Send commit information including file changes to backend
   */
  private async sendCommitInfoToBackend(workItemList: WorkItem[] | [], changeList: ChangeItem[]): Promise<void> {
    try {
      // 使用CommitAnalysisService发送请求并处理响应
      await this.commitAnalysisService.analyzeCommit(workItemList, changeList);
    } catch (error) {
      Logger.error(`[WorkItemManager] Error in commit analysis: ${error}`);
      vscode.window.showErrorMessage(`分析提交失败: ${error}`);
    }
  }
  
  /**
   * Handle streaming responses for commit message generation
   */
  public onAnswer(
    reqId: string,
    isEnd: number,
    answer: string,
    payload?: CodeAIResponsePayload
  ): void {
    if (reqId !== this.commitAnalysisService.getReqId()) {
      return;
    }

    // Accumulate the answer chunks
    this.curAllAnswer += answer;
    
    try {
      // We should already have the gitRepo from selectWorkItem
      if (this.gitRepo) {
        if (this.currentWorkItemList && this.currentWorkItemList.length > 0) {
          // Update Git commit message box with work item keys + accumulated answer
          const workItemKeys = this.currentWorkItemList.map(item => `%${item.workItemKey}`).join('');
          this.gitRepo.inputBox.value = `${workItemKeys}\n\n${this.curAllAnswer}`;
        } else {
          // Update Git commit message box with just the accumulated answer
          this.gitRepo.inputBox.value = this.curAllAnswer;
        }
      }
      
      if (isEnd === IsAnswerEnd.YES) { // Successful completion
        this.curAllAnswer = '';
        this.currentWorkItemList = [];
        this.setAnalysisRunning(false);
        this.provider.handleWorkItemEvent({
          command: WebViewRspCommand.WORKITEM_RESPONSE,
          data: {
            reqType: WorkItemEventType.ANSWER_RECVED,
            isEnd: 1,
          }
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      Logger.error(`[WorkItemManager] Error updating commit message: ${errorMessage}`);
      this.setAnalysisRunning(false);
      this.provider.handleWorkItemEvent({
        command: WebViewRspCommand.WORKITEM_RESPONSE,
        data: {
          reqType: WorkItemEventType.ANSWER_RECVED,
          isEnd: 1
        }
      });
    }
  }

  /**
   * Handle errors during commit message generation
   */
  public onTaskError(reqId: string, rtnCode: number, errMsg?: string): void {
    if (reqId !== this.commitAnalysisService.getReqId()) {
      return;
    }

    this.setAnalysisRunning(false);

    let displayErrorMessage: string;
    const workItemKeyPrefix = (this.currentWorkItemList && this.currentWorkItemList.length > 0) 
      ? `${this.currentWorkItemList.map(item => `%${item.workItemKey}`).join('')}\n\n` 
      : '';
    
    if (rtnCode === RtnCode.STOP_ANSWER) {
      displayErrorMessage = '提交分析已停止';
    } else if (rtnCode === RtnCode.CANCEL) {
      displayErrorMessage = '提交分析已取消';
    } else if (rtnCode === RtnCode.RECV_TIMEOUT) {
      displayErrorMessage = '提交分析超时，请重试';
    } else {
      displayErrorMessage = '未成功生成提交信息，请重试';
    }
    
    const commitMessage = `${workItemKeyPrefix}${displayErrorMessage}`;
    Logger.error(`[WorkItemManager] onTaskError: ${displayErrorMessage}`);

    this.provider.handleWorkItemEvent({
      command: WebViewRspCommand.WORKITEM_RESPONSE,
      data: {
        reqType: WorkItemEventType.ANSWER_RECVED,
        isEnd: 1
      }
    });
    
    try {
      if (this.gitRepo) {
        // Update with appropriate message, maintaining the work item keys if present
        this.gitRepo.inputBox.value = commitMessage;
      } else if (rtnCode !== RtnCode.STOP_ANSWER && rtnCode !== RtnCode.CANCEL) {
        // 只有在真正的错误情况下，且没有 git repo 时才显示错误消息
        Logger.error(`[WorkItemManager] onTaskError: ${displayErrorMessage}`);
      }
    } catch (error) {
      Logger.error(`[WorkItemManager] Error displaying error message: ${error}`);
      // 发生异常时，只有在非用户操作的情况下才显示错误
      if (rtnCode !== RtnCode.STOP_ANSWER && rtnCode !== RtnCode.CANCEL) {
        Logger.error(`[WorkItemManager] Error displaying error message: ${error}`);
      }
    }
    
    // Reset accumulated answer
    this.curAllAnswer = '';
    
    // Reset current work item list
    this.currentWorkItemList = [];
  }
  
  public async getGitChangesWithCommand(): Promise<ChangeItem[]> {
    const changeList: ChangeItem[] = [];
    let totalLength = 0;
    const maxLength = +(getCodeAIConfig()?.ChatCharacterLimit || 80000);

    try {
      // 获取暂存区和工作区变更
      const indexChanges = this.gitRepo.state.indexChanges || [];
      const workingTreeChanges = this.gitRepo.state.workingTreeChanges || [];

      let changesToProcess: any[] = [];

      // 优先处理暂存区变更，如果暂存区有变更则只处理暂存区
      if (indexChanges.length > 0) {
        changesToProcess = indexChanges;
        Logger.debug(`[WorkItemManager] Processing ${indexChanges.length} staged changes`);
      } else if (workingTreeChanges.length > 0) {
        changesToProcess = workingTreeChanges;
        Logger.debug(`[WorkItemManager] No staged changes, processing ${workingTreeChanges.length} working tree changes`);
      } else {
        Logger.debug(`[WorkItemManager] No changes found`);
        return changeList;
      }

      // 处理选定的变更
      for (const change of changesToProcess) {
        try {
          const uri = change.uri;
          const filePath = uri.fsPath;
          let diffText = '';

          // Check if the file is binary based on its extension
          const fileExtension = path.extname(filePath).toLowerCase();
          if (BINARY_EXTENSIONS.has(fileExtension)) {
            continue; // Skip this binary file entirely
          }

          // 根据变更来源获取差异
          if (indexChanges.includes(change)) {
            // 暂存区变更：获取暂存区与 HEAD 的差异
            try {
              diffText = await this.gitRepo.diffIndexWithHEAD(filePath);
              // 如果无法获取差异，可能是新文件，尝试直接获取内容
              if (!diffText && fs.existsSync(filePath)) {
                const content = fs.readFileSync(filePath, 'utf8');
                diffText = diff.createPatch(
                  uri.fsPath.split(/[/\\]/).pop() || '',
                  '',  // 空字符串表示新文件
                  content
                );
              }
            } catch (error) {
              Logger.error(`[WorkItemManager] 获取暂存区差异失败: ${error}`);
            }
          } else {
            // 工作区变更：获取工作区与 HEAD 的差异
            try {
              diffText = await this.gitRepo.diffWithHEAD(filePath);
              
              // 如果无法获取差异，可能是新文件，尝试直接获取内容
              if (!diffText && fs.existsSync(filePath)) {
                const content = fs.readFileSync(filePath, 'utf8');
                diffText = diff.createPatch(
                  uri.fsPath.split(/[/\\]/).pop() || '',
                  '',  // 空字符串表示新文件
                  content
                );
              }
            } catch (error) {
              Logger.error(`[WorkItemManager] 获取工作区差异失败: ${error}`);
            }
          }

          // 如果成功获取差异，检查长度限制
          if (diffText) {
            const diffLength = diffText.length;
            
            // 检查是否会超出总长度限制
            if (totalLength + diffLength > maxLength) {
              const remainingLength = maxLength - totalLength;
              // 截断当前差异内容
              const truncatedDiff = diffText.substring(0, remainingLength);
              changeList.push(truncatedDiff);
              break;
            } else {
              // 未超出限制，正常添加
              changeList.push(diffText);
              totalLength += diffLength;
            }
          }
          
        } catch (error) {
          Logger.error(`[WorkItemManager] 处理文件变更时出错: ${error}`);
        }
      }
      
      Logger.debug(`[WorkItemManager] Successfully processed ${changeList.length} changes`);
      return changeList;
    } catch (error) {
      Logger.error(`[WorkItemManager] 获取Git变更列表时出错: ${error}`);
      throw error;
    }
  }


  public async viewWorkItemDetail(url: string) {
    let error: string | undefined;
    
    try {
      const uri = `${process.env.HTTP_SERVER_HOST}${url}`;
      // Open the URL in the system's default browser
      await vscode.env.openExternal(vscode.Uri.parse(uri));
    } catch (err) {
      error = `Failed to open URL: ${err}`;
      vscode.window.showErrorMessage(error);
    }
    
    this.provider.handleWorkItemEvent({
      command: WebViewRspCommand.WORKITEM_RESPONSE,
      data: {
        reqType: WorkItemEventType.VIEW_WORKITEM_DETAIL,
        error
      }
    });
  }
} 