Index: package.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>{\r\n  \"name\": \"Secidea\",\r\n  \"displayName\": \"海云安代码助手\",\r\n  \"publisher\": \"Secidea\",\r\n  \"description\": \"海云安代码助手插件是一款高效、可靠的缺陷扫描展示工具，与海云安源代码检测和开源组件检测平台无缝对接\",\r\n  \"version\": \"3.5.0-c10\",\r\n  \"private\": true,\r\n  \"engines\": {\r\n    \"vscode\": \"^1.68.1\"\r\n  },\r\n  \"icon\": \"assets/logo.png\",\r\n  \"license\": \"SEE LICENSE IN LICENSE\",\r\n  \"categories\": [\r\n    \"Programming Languages\",\r\n    \"Machine Learning\",\r\n    \"Education\",\r\n    \"Snippets\"\r\n  ],\r\n  \"keywords\": [\r\n    \"ai\",\r\n    \"openai\",\r\n    \"codex\",\r\n    \"pilot\",\r\n    \"snippets\",\r\n    \"documentation\",\r\n    \"autocomplete\",\r\n    \"intellisense\",\r\n    \"refactor\",\r\n    \"javascript\",\r\n    \"python\",\r\n    \"typescript\",\r\n    \"php\",\r\n    \"go\",\r\n    \"golang\",\r\n    \"ruby\",\r\n    \"c++\",\r\n    \"c#\",\r\n    \"java\",\r\n    \"kotlin\",\r\n    \"co-pilot\"\r\n  ],\r\n  \"homepage\": \"https://secidea.com/Secidea/secidea\",\r\n  \"repository\": {\r\n    \"type\": \"git\",\r\n    \"url\": \"https://secidea.com/Secidea/secidea\"\r\n  },\r\n  \"activationEvents\": [\r\n    \"onStartupFinished\"\r\n  ],\r\n  \"main\": \"./dist/extension.js\",\r\n  \"enabledApiProposals\": [\r\n    \"interactive\",\r\n    \"interactiveUserActions\"\r\n  ],\r\n  \"capabilities\": {\r\n    \"virtualWorkspaces\": true,\r\n    \"untrustedWorkspaces\": {\r\n      \"supported\": true\r\n    }\r\n  },\r\n  \"contributes\": {\r\n    \"commands\": [\r\n      {\r\n        \"command\": \"srd-copilot.settings\",\r\n        \"title\": \"设置\",\r\n        \"category\": \"海云安代码助手\",\r\n        \"icon\": \"$(gear)\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.startChat\",\r\n        \"title\": \"开始聊天\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.helpDocument\",\r\n        \"title\": \"帮助文档\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.login\",\r\n        \"title\": \"CodeFree登录\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeComplete.enableToggle\",\r\n        \"title\": \"代码补全启用/禁用\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.explain\",\r\n        \"title\": \"解释代码\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.unitTest\",\r\n        \"title\": \"生成单元测试\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.comment\",\r\n        \"title\": \"生成代码注释\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.optimization\",\r\n        \"title\": \"生成代码优化建议\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.openQuestion\",\r\n        \"title\": \"帮助\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/question-light-draw.svg\",\r\n          \"dark\": \"assets/toolbar/question-dark-draw.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.openFeedback\",\r\n        \"title\": \"反馈\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/conversation-light-draw.svg\",\r\n          \"dark\": \"assets/toolbar/conversation-dark-draw.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.closeSidebar\",\r\n        \"title\": \"关闭\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/close-draw-light.svg\",\r\n          \"dark\": \"assets/toolbar/close-draw-dark.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeDiff.acceptAllChanges\",\r\n        \"title\": \"Accept All Changes\",\r\n        \"icon\": \"$(check-all)\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeDiff.revertAllChanges\",\r\n        \"title\": \"Revert All Changes\",\r\n        \"icon\": \"$(discard)\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.aiScmCommand\",\r\n        \"title\": \"生成提交信息并关联工作项\",\r\n        \"icon\": \"assets/codefree.svg\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.stopCommitAnalysis\",\r\n        \"title\": \"停止提交分析\",\r\n        \"category\": \"研发云CodeFree\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/stopAiCommit-light.svg\",\r\n          \"dark\": \"assets/toolbar/stopAiCommit-dark.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeLensAction\",\r\n        \"title\": \"代码透镜\"\r\n      }\r\n    ],\r\n    \"keybindings\": [\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.trigger\",\r\n        \"key\": \"ctrl+enter\",\r\n        \"mac\": \"cmd+enter\",\r\n        \"when\": \"editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible\"\r\n      },\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.showPrevious\",\r\n        \"key\": \"alt+[\",\r\n        \"mac\": \"alt+[\",\r\n        \"when\": \"inlineSuggestionsVisible\"\r\n      },\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.showNext\",\r\n        \"key\": \"alt+]\",\r\n        \"mac\": \"alt+]\",\r\n        \"when\": \"inlineSuggestionsVisible\"\r\n      },\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.commit\",\r\n        \"key\": \"tab\",\r\n        \"mac\": \"tab\",\r\n        \"when\": \"inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeComplete.enableToggle\",\r\n        \"key\": \"ctrl+alt+shift+o\",\r\n        \"mac\": \"cmd+alt+shift+o\",\r\n        \"when\": \"srd-copilot.activated\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.chat.focus\",\r\n        \"key\": \"alt+shift+k\",\r\n        \"mac\": \"alt+shift+k\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.startChat\",\r\n        \"key\": \"alt+shift+k\",\r\n        \"mac\": \"alt+shift+k\"\r\n      }\r\n    ],\r\n    \"menus\": {\r\n      \"view/title\": [\r\n        {\r\n          \"command\": \"srd-copilot.openQuestion\",\r\n          \"group\": \"navigation@0\",\r\n          \"when\": \"view == srd-copilot.chat\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.openFeedback\",\r\n          \"group\": \"navigation@1\",\r\n          \"when\": \"view == srd-copilot.chat\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.closeSidebar\",\r\n          \"group\": \"navigation@2\",\r\n          \"when\": \"view == srd-copilot.chat\"\r\n        }\r\n      ],\r\n      \"editor/context\": [\r\n        {\r\n          \"submenu\": \"srd-copilot/editor/context/menuItems\",\r\n          \"group\": \"navigation\"\r\n        }\r\n      ],\r\n      \"srd-copilot/editor/context/menuItems\": [\r\n        {\r\n          \"command\": \"srd-copilot.helpDocument\",\r\n          \"group\": \"srd-copilot-menu-group@1\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.login\",\r\n          \"group\": \"srd-copilot-menu-group@2\",\r\n          \"when\": \"!srd-copilot.activated\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.startChat\",\r\n          \"group\": \"srd-copilot-menu-group@3\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeComplete.enableToggle\",\r\n          \"group\": \"srd-copilot-menu-group@4\",\r\n          \"when\": \"srd-copilot.activated\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.explain\",\r\n          \"group\": \"srd-copilot-menu-group@5\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.unitTest\",\r\n          \"group\": \"srd-copilot-menu-group@6\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.comment\",\r\n          \"group\": \"srd-copilot-menu-group@7\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.optimization\",\r\n          \"group\": \"srd-copilot-menu-group@8\"\r\n        }\r\n      ],\r\n      \"commandPalette\": [],\r\n      \"editor/title\": [\r\n        {\r\n          \"when\": \"isInDiffEditor\",\r\n          \"command\": \"srd-copilot.codeDiff.acceptAllChanges\",\r\n          \"group\": \"navigation@1\"\r\n        },\r\n        {\r\n          \"when\": \"isInDiffEditor\",\r\n          \"command\": \"srd-copilot.codeDiff.revertAllChanges\",\r\n          \"group\": \"navigation@2\"\r\n        }\r\n      ]\r\n    },\r\n    \"submenus\": [\r\n      {\r\n        \"id\": \"srd-copilot/editor/context/menuItems\",\r\n        \"label\": \"海云安代码助手\"\r\n      }\r\n    ],\r\n    \"viewsContainers\": {\r\n      \"activitybar\": [\r\n        {\r\n          \"id\": \"srd-chat\",\r\n          \"title\": \"海云安代码助手\",\r\n          \"icon\": \"assets/chat.svg\"\r\n        }\r\n      ]\r\n    },\r\n    \"views\": {\r\n      \"srd-chat\": [\r\n        {\r\n          \"id\": \"srd-copilot.chat\",\r\n          \"type\": \"webview\",\r\n          \"name\": \"海云安代码助手\"\r\n        }\r\n      ]\r\n    },\r\n    \"icons\": {\r\n      \"srd-copilot-unlogin\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e791\"\r\n        }\r\n      },\r\n      \"srd-copilot-error-info\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7a0\"\r\n        }\r\n      },\r\n      \"srd-copilot-unconnect\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e78d\"\r\n        }\r\n      },\r\n      \"srd-copilot-code-enabled\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e78e\"\r\n        }\r\n      },\r\n      \"srd-copilot-code-disabled\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e78f\"\r\n        }\r\n      },\r\n      \"srd-copilot-select\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7b4\"\r\n        }\r\n      },\r\n      \"srd-copilot-delete\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e763\"\r\n        }\r\n      },\r\n      \"srd-copilot-refresh\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7f7\"\r\n        }\r\n      },\r\n      \"srd-copilot-logo\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7b3\"\r\n        }\r\n      },\r\n      \"srd-copilot-cancel\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e65f\"\r\n        }\r\n      }\r\n    },\r\n    \"iconFonts\": [\r\n      {\r\n        \"id\": \"srd-copilot-font\",\r\n        \"src\": [\r\n          {\r\n            \"path\": \"assets/iconfont.woff\",\r\n            \"format\": \"woff\"\r\n          }\r\n        ]\r\n      }\r\n    ],\r\n    \"configuration\": [\r\n      {\r\n        \"title\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"title\": \"基础配置\",\r\n        \"properties\": {\r\n          \"通用.服务器扫描地址\": {\r\n            \"type\": \"string\",\r\n            \"order\": 0,\r\n            \"description\": \"协议://IP地址:端口/项目名,如 http://***********:3000/oscap\",\r\n            \"pattern\": \"^https?://.+\"\r\n          },\r\n          \"通用.登录状态\": {\r\n            \"type\": \"string\",\r\n            \"order\": 1,\r\n            \"default\": \"未登录\",\r\n            \"readOnly\": true,\r\n            \"markdownDescription\": \"[**点此登录**](command:secidea.login)  [**点此登出**](command:secidea.logout)  \\n  请先配置正确服务器扫描地址，登录成功后，下方会显示用户信息\"\r\n          },\r\n          \"通用.版本信息\": {\r\n            \"type\": \"object\",\r\n            \"order\": 2,\r\n            \"markdownDescription\": \"插件版本号：3.5.0-c10 [检查更新](command:srd-copilot.checkUpdate)\",\r\n            \"default\": {\r\n              \"新版本插件安装包存放目录\": \"\",\r\n              \"是否自动下载新版本\": false,\r\n              \"是否开启更新提醒\": false,\r\n              \"更新提醒间隔小时\": 1\r\n            },\r\n            \"properties\": {\r\n              \"新版本插件安装包存放目录\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"是否自动下载新版本\": {\r\n                \"type\": \"boolean\"\r\n              },\r\n              \"是否开启更新提醒\": {\r\n                \"type\": \"boolean\"\r\n              },\r\n              \"更新提醒间隔小时\": {\r\n                \"type\": \"number\"\r\n              }\r\n            },\r\n            \"additionalProperties\": false\r\n          }\r\n        }\r\n      },\r\n      {\r\n        \"title\": \"代码助手配置\",\r\n        \"properties\": {\r\n          \"代码助手.是否展示函数级别快捷键\": {\r\n            \"type\": \"boolean\",\r\n            \"default\": true,\r\n            \"description\": \"是否展示函数级别快捷键\"\r\n          },\r\n          \"代码助手.禁用补全语言\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"禁用补全的语言设置\",\r\n            \"items\": {\r\n              \"type\": \"object\",\r\n              \"properties\": {\r\n                \"语言\": {\r\n                  \"type\": \"string\",\r\n                  \"description\": \"语言\"\r\n                },\r\n                \"尾缀名\": {\r\n                  \"type\": \"string\",\r\n                  \"description\": \"尾缀名\"\r\n                },\r\n                \"是否禁用\": {\r\n                  \"type\": \"boolean\",\r\n                  \"description\": \"是否禁用\"\r\n                }\r\n              },\r\n              \"required\": [\r\n                \"语言\",\r\n                \"尾缀名\",\r\n                \"是否禁用\"\r\n              ]\r\n            },\r\n            \"default\": [\r\n              {\r\n                \"语言\": \"ABAP\",\r\n                \"尾缀名\": \"abap\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"ActionScript\",\r\n                \"尾缀名\": \"as\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Ada\",\r\n                \"尾缀名\": \"ada\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AsciiDoc\",\r\n                \"尾缀名\": \"adoc\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Makefile\",\r\n                \"尾缀名\": \"am\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AppleScript\",\r\n                \"尾缀名\": \"applescript\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Arc\",\r\n                \"尾缀名\": \"arc\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"ASP\",\r\n                \"尾缀名\": \"asp\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Assembly\",\r\n                \"尾缀名\": \"asm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AutoHotkey\",\r\n                \"尾缀名\": \"ahk\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AutoIt\",\r\n                \"尾缀名\": \"au3\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Awk\",\r\n                \"尾缀名\": \"as\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Batch\",\r\n                \"尾缀名\": \"bat\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Bazel\",\r\n                \"尾缀名\": \"bzl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"BibTeX\",\r\n                \"尾缀名\": \"bib\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Bison\",\r\n                \"尾缀名\": \"bison\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"BitBake\",\r\n                \"尾缀名\": \"bb\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Blade\",\r\n                \"尾缀名\": \"blade.php\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Bash\",\r\n                \"尾缀名\": \"sh\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"C\",\r\n                \"尾缀名\": \"c\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"C#\",\r\n                \"尾缀名\": \"cs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"C++\",\r\n                \"尾缀名\": \"cpp\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CMake\",\r\n                \"尾缀名\": \"cmake\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"COBOL\",\r\n                \"尾缀名\": \"cob\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CoffeeScript\",\r\n                \"尾缀名\": \"coffee\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"ColdFusion\",\r\n                \"尾缀名\": \"cfm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Clojure\",\r\n                \"尾缀名\": \"clj\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CSS\",\r\n                \"尾缀名\": \"css\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CSV\",\r\n                \"尾缀名\": \"csv\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CUDA\",\r\n                \"尾缀名\": \"cu\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"D\",\r\n                \"尾缀名\": \"d\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Dart\",\r\n                \"尾缀名\": \"dart\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Delphi\",\r\n                \"尾缀名\": \"dpr\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Pascal\",\r\n                \"尾缀名\": \"pas\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Diff\",\r\n                \"尾缀名\": \"diff\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Patch\",\r\n                \"尾缀名\": \"patch\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Dockerfile\",\r\n                \"尾缀名\": \"dockerfile\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"DTD\",\r\n                \"尾缀名\": \"dtd\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Erlang\",\r\n                \"尾缀名\": \"erl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Elixir\",\r\n                \"尾缀名\": \"ex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Elixir Script\",\r\n                \"尾缀名\": \"exs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Elm\",\r\n                \"尾缀名\": \"elm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"EEx\",\r\n                \"尾缀名\": \"eex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"LiveEEx\",\r\n                \"尾缀名\": \"leex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"F#\",\r\n                \"尾缀名\": \"fs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Fortran\",\r\n                \"尾缀名\": \"f\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Fortran90\",\r\n                \"尾缀名\": \"f90\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Fish\",\r\n                \"尾缀名\": \"fish\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Forth\",\r\n                \"尾缀名\": \"fth\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"GLSL\",\r\n                \"尾缀名\": \"glsl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Go\",\r\n                \"尾缀名\": \"go\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"GraphQL\",\r\n                \"尾缀名\": \"gql\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Groovy\",\r\n                \"尾缀名\": \"groovy\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Gradle\",\r\n                \"尾缀名\": \"gradle\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Haml\",\r\n                \"尾缀名\": \"haml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Haskell\",\r\n                \"尾缀名\": \"hs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HCL\",\r\n                \"尾缀名\": \"hcl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HLSL\",\r\n                \"尾缀名\": \"hlsl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HTML\",\r\n                \"尾缀名\": \"html\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HTML\",\r\n                \"尾缀名\": \"htm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HTTP\",\r\n                \"尾缀名\": \"http\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Haxe\",\r\n                \"尾缀名\": \"hx\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"INI\",\r\n                \"尾缀名\": \"ini\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Java\",\r\n                \"尾缀名\": \"java\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"JavaScript\",\r\n                \"尾缀名\": \"js\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"JSX\",\r\n                \"尾缀名\": \"jsx\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"JSON\",\r\n                \"尾缀名\": \"json\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Julia\",\r\n                \"尾缀名\": \"jl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Kotlin\",\r\n                \"尾缀名\": \"kt\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Kotlin Script\",\r\n                \"尾缀名\": \"kts\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"LaTeX\",\r\n                \"尾缀名\": \"tex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Less\",\r\n                \"尾缀名\": \"less\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Lisp\",\r\n                \"尾缀名\": \"lisp\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Lua\",\r\n                \"尾缀名\": \"lua\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"MATLAB/Objective-C\",\r\n                \"尾缀名\": \"m\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Objective-C++\",\r\n                \"尾缀名\": \"mm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Markdown\",\r\n                \"尾缀名\": \"md\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Makefile\",\r\n                \"尾缀名\": \"makefile\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Nix\",\r\n                \"尾缀名\": \"nix\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"OCaml\",\r\n                \"尾缀名\": \"ml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Perl\",\r\n                \"尾缀名\": \"pl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"PHP\",\r\n                \"尾缀名\": \"php\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"PowerShell\",\r\n                \"尾缀名\": \"ps1\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Protocol Buffers\",\r\n                \"尾缀名\": \"proto\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Python\",\r\n                \"尾缀名\": \"py\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"R\",\r\n                \"尾缀名\": \"r\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Ruby\",\r\n                \"尾缀名\": \"rb\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Rust\",\r\n                \"尾缀名\": \"rs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Sass\",\r\n                \"尾缀名\": \"sass\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Scala\",\r\n                \"尾缀名\": \"scala\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"SCSS\",\r\n                \"尾缀名\": \"scss\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"SQL\",\r\n                \"尾缀名\": \"sql\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Stylus\",\r\n                \"尾缀名\": \"styl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"SVG\",\r\n                \"尾缀名\": \"svg\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Swift\",\r\n                \"尾缀名\": \"swift\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Tcl\",\r\n                \"尾缀名\": \"tcl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Terraform\",\r\n                \"尾缀名\": \"tf\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"TypeScript\",\r\n                \"尾缀名\": \"ts\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"TSX\",\r\n                \"尾缀名\": \"tsx\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Twig\",\r\n                \"尾缀名\": \"twig\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Text\",\r\n                \"尾缀名\": \"txt\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"VB.NET\",\r\n                \"尾缀名\": \"vb\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Vue\",\r\n                \"尾缀名\": \"vue\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"XML\",\r\n                \"尾缀名\": \"xml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"XSLT\",\r\n                \"尾缀名\": \"xsl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"YAML\",\r\n                \"尾缀名\": \"yaml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"YAML\",\r\n                \"尾缀名\": \"yml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Zig\",\r\n                \"尾缀名\": \"zig\",\r\n                \"是否禁用\": false\r\n              }\r\n            ],\r\n            \"markdownDescription\": \"配置禁用代码补全功能的语言类型\",\r\n            \"order\": 2\r\n          },\r\n          \"代码助手.补全模式\": {\r\n            \"type\": [\r\n              \"string\"\r\n            ],\r\n            \"enum\": [\r\n              \"精准优先\",\r\n              \"平衡模式\",\r\n              \"速度优先\"\r\n            ],\r\n            \"default\": \"精准优先\",\r\n            \"markdownDescription\": \"选择使用代码助手进行补全的模式\",\r\n            \"order\": 1\r\n          },\r\n          \"代码助手.快捷键配置\": {\r\n            \"type\": [\r\n              \"string\"\r\n            ],\r\n            \"enum\": [\r\n              \"默认\",\r\n              \"自定义\"\r\n            ],\r\n            \"default\": \"默认\",\r\n            \"enumDescriptions\": [\r\n              \"使用`Ctrl + \\\\`键显示补全信息，使用`Tab`键接受完整补全 \\n使用`Ctrl + Alt + /`键接受下一行\",\r\n              \"自定义快捷键\"\r\n            ],\r\n            \"markdownDescription\": \"配置代码助手补全对应的快捷键\",\r\n            \"order\": 3\r\n          },\r\n          \"代码助手.自定义指令\": {\r\n            \"type\": \"string\",\r\n            \"maxLength\": 1500,\r\n            \"description\": \"需要代码助手了解哪些信息，或如何进行回答。例如：我是一名软件开发者，当我问安全问题时，如果回复内容包含代码，请解释这些代码；但如果仅需要提供功能代码时，只提供代码。\",\r\n            \"order\": 4\r\n          }\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  \"scripts\": {\r\n    \"vsce:package\": \"node scripts/switch-assets.js && vsce package\",\r\n    \"vsce:package:prod\": \"cross-env NODE_ENV=production node scripts/switch-assets.js && vsce package\",\r\n    \"vscode:prepublish\": \"yarn run clean:dist && yarn run package\",\r\n    \"compile\": \"yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run build-codechat\",\r\n    \"package\": \"node scripts/switch-assets.js && webpack --mode production --devtool hidden-source-map && yarn run build-codechat\",\r\n    \"watch\": \"yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run watch-codechat\",\r\n    \"watch-vscode\": \"yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && webpack --watch\",\r\n    \"watch-vscode-prod\": \"yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && cross-env NODE_ENV=production webpack --watch\",\r\n    \"watch-codechat\": \"cd webview/codechat && yarn run build-watch\",\r\n    \"build-codechat\": \"cd webview/codechat && yarn run build\",\r\n    \"clean:dist\": \"rimraf dist\",\r\n    \"compile-tests\": \"tsc -p . --outDir out\",\r\n    \"watch-tests\": \"tsc -p . -w --outDir out\",\r\n    \"pretest\": \"yarn run compile-tests && yarn run compile && yarn run lint\",\r\n    \"lint\": \"eslint ./src --ext ts --fix\",\r\n    \"test\": \"node ./out/test/runTest.js\"\r\n  },\r\n  \"devDependencies\": {\r\n    \"@types/fs-extra\": \"^11.0.4\",\r\n    \"@types/glob\": \"^8.1.0\",\r\n    \"@types/mocha\": \"^10.0.1\",\r\n    \"@types/node\": \"20.2.5\",\r\n    \"@types/proper-lockfile\": \"^4.1.4\",\r\n    \"@types/tmp\": \"^0.2.3\",\r\n    \"@types/vscode\": \"^1.68.1\",\r\n    \"@types/ws\": \"^8.5.5\",\r\n    \"@typescript-eslint/eslint-plugin\": \"^5.59.8\",\r\n    \"@typescript-eslint/parser\": \"^5.59.8\",\r\n    \"@vscode/test-electron\": \"^2.3.2\",\r\n    \"cross-env\": \"^7.0.3\",\r\n    \"dotenv-webpack\": \"^8.0.1\",\r\n    \"eslint\": \"^8.41.0\",\r\n    \"eslint-config-prettier\": \"^8.6.0\",\r\n    \"eslint-plugin-prettier\": \"^4.2.1\",\r\n    \"glob\": \"^8.1.0\",\r\n    \"mocha\": \"^10.2.0\",\r\n    \"prettier\": \"^2.8.3\",\r\n    \"rimraf\": \"^5.0.5\",\r\n    \"ts-loader\": \"^9.4.3\",\r\n    \"typescript\": \"^5.1.3\",\r\n    \"vsce\": \"^2.15.0\",\r\n    \"webpack\": \"^5.85.0\",\r\n    \"webpack-cli\": \"^5.1.1\"\r\n  },\r\n  \"dependencies\": {\r\n    \"@ai-zen/node-fetch-event-source\": \"2.1.0\",\r\n    \"@types/archiver\": \"^6.0.2\",\r\n    \"@types/diff\": \"^7.0.2\",\r\n    \"@types/markdown-it-attrs\": \"^4.1.3\",\r\n    \"abort-controller\": \"^3.0.0\",\r\n    \"archiver\": \"^7.0.1\",\r\n    \"axios\": \"^1.8.4\",\r\n    \"diff\": \"^7.0.0\",\r\n    \"dotenv\": \"^8.6.0\",\r\n    \"form-data\": \"^4.0.0\",\r\n    \"fs-extra\": \"^11.3.0\",\r\n    \"fuse.js\": \"6.4.6\",\r\n    \"http-proxy-agent\": \"^7.0.0\",\r\n    \"macaddress\": \"^0.5.3\",\r\n    \"markdown-it-attrs\": \"^4.1.6\",\r\n    \"node-fetch\": \"^3.3.2\",\r\n    \"node-machine-id\": \"^1.1.12\",\r\n    \"proper-lockfile\": \"^4.1.2\",\r\n    \"rxjs\": \"^7.8.2\",\r\n    \"uuid\": \"^11.1.0\",\r\n    \"web-tree-sitter\": \"^0.23.0\",\r\n    \"ws\": \"^8.14.0\",\r\n    \"zip-lib\": \"^1.0.5\"\r\n  },\r\n  \"optionalDependencies\": {\r\n    \"bufferutil\": \"^4.0.7\",\r\n    \"utf-8-validate\": \"^6.0.3\"\r\n  },\r\n  \"workspaces\": [\r\n    \"webview/*\"\r\n  ],\r\n  \"__metadata\": {\r\n    \"publisherDisplayName\": \"海云安\"\r\n  }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/package.json b/package.json
--- a/package.json	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/package.json	(date 1750821003368)
@@ -87,6 +87,11 @@
         "category": "海云安代码助手"
       },
       {
+        "command": "srd-copilot.showStatusMenu",
+        "title": "显示状态菜单",
+        "category": "海云安代码助手"
+      },
+      {
         "command": "srd-copilot.codeSelection.explain",
         "title": "解释代码",
         "category": "海云安代码助手"
@@ -1031,10 +1036,10 @@
             ],
             "default": "默认",
             "enumDescriptions": [
-              "使用`Ctrl + \\`键显示补全信息，使用`Tab`键接受完整补全 \n使用`Ctrl + Alt + /`键接受下一行",
+              "`Ctrl + Enter` 手动发起补全请求\n`Tab` 选择当前补全建议\n`Alt + [` 或 `Alt + ]` 查看上一条/下一条补全建议\n`Ctrl + Alt + Shift + O` 启用/禁用代码补全",
               "自定义快捷键"
             ],
-            "markdownDescription": "配置代码助手补全对应的快捷键",
+            "markdownDescription": "配置代码助手对应的补全快捷键",
             "order": 3
           },
           "代码助手.自定义指令": {
Index: src/agent/process/TabbyAgentProcess.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { ChildProcess } from \"child_process\";\r\nimport { AgentProcess } from \"./AgentProcess\";\r\nimport { spawn } from 'child_process';\r\nimport { AgentConfig, TABBY_AGENT } from \"../types\";\r\nimport { SystemUtils } from '../utils/system';\r\nimport { Logger } from \"../../utils/logger\";\r\nimport { getEnvTag } from \"../../utils/common\";\r\nimport * as path from 'path';\r\nimport { generateIgnoreList } from \"../../utils/common\";\r\n\r\nexport class TabbyAgentProcess extends AgentProcess {\r\n  public constructor() {\r\n    super()\r\n  }\r\n\r\n  public buildProcess(agentPath: string, port: number, config: AgentConfig): ChildProcess {\r\n    const baseBrand = process.env.ISSEC !== 'false' ? 'oscap' : 'codefree';\r\n    const nodePath = SystemUtils.getAgentPath(baseBrand, 'node');\r\n    const basePath = SystemUtils.getAgentBasePath(baseBrand)\r\n    SystemUtils.setPermission(nodePath);\r\n    SystemUtils.removeQuarantine(basePath);\r\n\r\n    const childProcess = spawn(nodePath, [agentPath], {\r\n      stdio: ['pipe', 'pipe', 'pipe'],\r\n      env: {\r\n        ...process.env,\r\n        LANG: 'en_US.UTF-8',\r\n        LC_ALL: 'en_US.UTF-8',\r\n        NODE_SKIP_PLATFORM_CHECK: '1',\r\n        \"apiKey\": config.apiKey,\r\n        \"invokerId\": config.invokerId,\r\n        \"pluginType\": config.pluginType,\r\n        \"pluginVersion\": config.pluginVersion,\r\n        \"clientType\": config.clientType,\r\n        \"clientVersion\": config.clientVersion,\r\n        \"serverType\": config.serverType,\r\n        \"serverBaseUrl\": config.serverBaseUrl,\r\n        \"cplSubservice\": config.cplSubservice,\r\n        \"env\": getEnvTag(),\r\n        // todo:ignore list\r\n        'ignoreList': generateIgnoreList(),\r\n      },\r\n      detached: false,\r\n      cwd: path.dirname(agentPath),\r\n    });\r\n\r\n    this.process = childProcess;\r\n\r\n    return childProcess\r\n  }\r\n\r\n\r\n  public getAgentName(): string {\r\n    return TABBY_AGENT\r\n  }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/agent/process/TabbyAgentProcess.ts b/src/agent/process/TabbyAgentProcess.ts
--- a/src/agent/process/TabbyAgentProcess.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/agent/process/TabbyAgentProcess.ts	(date 1750815759708)
@@ -34,7 +34,7 @@
         "clientType": config.clientType,
         "clientVersion": config.clientVersion,
         "serverType": config.serverType,
-        "serverBaseUrl": config.serverBaseUrl,
+        "serverBaseUrl": config.serverBaseUrl?.endsWith('/') ? config.serverBaseUrl.slice(0, -1) : config.serverBaseUrl,
         "cplSubservice": config.cplSubservice,
         "env": getEnvTag(),
         // todo:ignore list
Index: package-sec.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>{\r\n  \"name\": \"Secidea\",\r\n  \"displayName\": \"海云安代码助手\",\r\n  \"publisher\": \"Secidea\",\r\n  \"description\": \"海云安代码助手插件是一款高效、可靠的缺陷扫描展示工具，与海云安源代码检测和开源组件检测平台无缝对接\",\r\n  \"version\": \"3.5.0-c10\",\r\n  \"private\": true,\r\n  \"engines\": {\r\n    \"vscode\": \"^1.68.1\"\r\n  },\r\n  \"icon\": \"assets/logo.png\",\r\n  \"license\": \"SEE LICENSE IN LICENSE\",\r\n  \"categories\": [\r\n    \"Programming Languages\",\r\n    \"Machine Learning\",\r\n    \"Education\",\r\n    \"Snippets\"\r\n  ],\r\n  \"keywords\": [\r\n    \"ai\",\r\n    \"openai\",\r\n    \"codex\",\r\n    \"pilot\",\r\n    \"snippets\",\r\n    \"documentation\",\r\n    \"autocomplete\",\r\n    \"intellisense\",\r\n    \"refactor\",\r\n    \"javascript\",\r\n    \"python\",\r\n    \"typescript\",\r\n    \"php\",\r\n    \"go\",\r\n    \"golang\",\r\n    \"ruby\",\r\n    \"c++\",\r\n    \"c#\",\r\n    \"java\",\r\n    \"kotlin\",\r\n    \"co-pilot\"\r\n  ],\r\n  \"homepage\": \"https://secidea.com/Secidea/secidea\",\r\n  \"repository\": {\r\n    \"type\": \"git\",\r\n    \"url\": \"https://secidea.com/Secidea/secidea\"\r\n  },\r\n  \"activationEvents\": [\r\n    \"onStartupFinished\"\r\n  ],\r\n  \"main\": \"./dist/extension.js\",\r\n  \"enabledApiProposals\": [\r\n    \"interactive\",\r\n    \"interactiveUserActions\"\r\n  ],\r\n  \"capabilities\": {\r\n    \"virtualWorkspaces\": true,\r\n    \"untrustedWorkspaces\": {\r\n      \"supported\": true\r\n    }\r\n  },\r\n  \"contributes\": {\r\n    \"commands\": [\r\n      {\r\n        \"command\": \"srd-copilot.settings\",\r\n        \"title\": \"设置\",\r\n        \"category\": \"海云安代码助手\",\r\n        \"icon\": \"$(gear)\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.startChat\",\r\n        \"title\": \"开始聊天\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.helpDocument\",\r\n        \"title\": \"帮助文档\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.login\",\r\n        \"title\": \"CodeFree登录\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeComplete.enableToggle\",\r\n        \"title\": \"代码补全启用/禁用\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.explain\",\r\n        \"title\": \"解释代码\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.unitTest\",\r\n        \"title\": \"生成单元测试\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.comment\",\r\n        \"title\": \"生成代码注释\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeSelection.optimization\",\r\n        \"title\": \"生成代码优化建议\",\r\n        \"category\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.openQuestion\",\r\n        \"title\": \"帮助\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/question-light-draw.svg\",\r\n          \"dark\": \"assets/toolbar/question-dark-draw.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.openFeedback\",\r\n        \"title\": \"反馈\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/conversation-light-draw.svg\",\r\n          \"dark\": \"assets/toolbar/conversation-dark-draw.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.closeSidebar\",\r\n        \"title\": \"关闭\",\r\n        \"icon\": {\r\n          \"light\": \"assets/toolbar/close-draw-light.svg\",\r\n          \"dark\": \"assets/toolbar/close-draw-dark.svg\"\r\n        }\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeDiff.acceptAllChanges\",\r\n        \"title\": \"Accept All Changes\",\r\n        \"icon\": \"$(check-all)\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeDiff.revertAllChanges\",\r\n        \"title\": \"Revert All Changes\",\r\n        \"icon\": \"$(discard)\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeLensAction\",\r\n        \"title\": \"代码透镜\"\r\n      }\r\n    ],\r\n    \"keybindings\": [\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.trigger\",\r\n        \"key\": \"ctrl+enter\",\r\n        \"mac\": \"cmd+enter\",\r\n        \"when\": \"editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible\"\r\n      },\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.showPrevious\",\r\n        \"key\": \"alt+[\",\r\n        \"mac\": \"alt+[\",\r\n        \"when\": \"inlineSuggestionsVisible\"\r\n      },\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.showNext\",\r\n        \"key\": \"alt+]\",\r\n        \"mac\": \"alt+]\",\r\n        \"when\": \"inlineSuggestionsVisible\"\r\n      },\r\n      {\r\n        \"command\": \"editor.action.inlineSuggest.commit\",\r\n        \"key\": \"tab\",\r\n        \"mac\": \"tab\",\r\n        \"when\": \"inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.codeComplete.enableToggle\",\r\n        \"key\": \"ctrl+alt+shift+o\",\r\n        \"mac\": \"cmd+alt+shift+o\",\r\n        \"when\": \"srd-copilot.activated\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.chat.focus\",\r\n        \"key\": \"alt+shift+k\",\r\n        \"mac\": \"alt+shift+k\"\r\n      },\r\n      {\r\n        \"command\": \"srd-copilot.startChat\",\r\n        \"key\": \"alt+shift+k\",\r\n        \"mac\": \"alt+shift+k\"\r\n      }\r\n    ],\r\n    \"menus\": {\r\n      \"view/title\": [\r\n        {\r\n          \"command\": \"srd-copilot.openQuestion\",\r\n          \"group\": \"navigation@0\",\r\n          \"when\": \"view == srd-copilot.chat\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.openFeedback\",\r\n          \"group\": \"navigation@1\",\r\n          \"when\": \"view == srd-copilot.chat\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.closeSidebar\",\r\n          \"group\": \"navigation@2\",\r\n          \"when\": \"view == srd-copilot.chat\"\r\n        }\r\n      ],\r\n      \"editor/context\": [\r\n        {\r\n          \"submenu\": \"srd-copilot/editor/context/menuItems\",\r\n          \"group\": \"navigation\"\r\n        }\r\n      ],\r\n      \"srd-copilot/editor/context/menuItems\": [\r\n        {\r\n          \"command\": \"srd-copilot.helpDocument\",\r\n          \"group\": \"srd-copilot-menu-group@1\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.login\",\r\n          \"group\": \"srd-copilot-menu-group@2\",\r\n          \"when\": \"!srd-copilot.activated\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.startChat\",\r\n          \"group\": \"srd-copilot-menu-group@3\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeComplete.enableToggle\",\r\n          \"group\": \"srd-copilot-menu-group@4\",\r\n          \"when\": \"srd-copilot.activated\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.explain\",\r\n          \"group\": \"srd-copilot-menu-group@5\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.unitTest\",\r\n          \"group\": \"srd-copilot-menu-group@6\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.comment\",\r\n          \"group\": \"srd-copilot-menu-group@7\"\r\n        },\r\n        {\r\n          \"command\": \"srd-copilot.codeSelection.optimization\",\r\n          \"group\": \"srd-copilot-menu-group@8\"\r\n        }\r\n      ],\r\n      \"commandPalette\": [],\r\n      \"editor/title\": [\r\n        {\r\n          \"when\": \"isInDiffEditor\",\r\n          \"command\": \"srd-copilot.codeDiff.acceptAllChanges\",\r\n          \"group\": \"navigation@1\"\r\n        },\r\n        {\r\n          \"when\": \"isInDiffEditor\",\r\n          \"command\": \"srd-copilot.codeDiff.revertAllChanges\",\r\n          \"group\": \"navigation@2\"\r\n        }\r\n      ]\r\n    },\r\n    \"submenus\": [\r\n      {\r\n        \"id\": \"srd-copilot/editor/context/menuItems\",\r\n        \"label\": \"海云安代码助手\"\r\n      }\r\n    ],\r\n    \"viewsContainers\": {\r\n      \"activitybar\": [\r\n        {\r\n          \"id\": \"srd-chat\",\r\n          \"title\": \"海云安代码助手\",\r\n          \"icon\": \"assets/chat.svg\"\r\n        }\r\n      ]\r\n    },\r\n    \"views\": {\r\n      \"srd-chat\": [\r\n        {\r\n          \"id\": \"srd-copilot.chat\",\r\n          \"type\": \"webview\",\r\n          \"name\": \"海云安代码助手\"\r\n        }\r\n      ]\r\n    },\r\n    \"icons\": {\r\n      \"srd-copilot-unlogin\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e791\"\r\n        }\r\n      },\r\n      \"srd-copilot-error-info\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7a0\"\r\n        }\r\n      },\r\n      \"srd-copilot-unconnect\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e78d\"\r\n        }\r\n      },\r\n      \"srd-copilot-code-enabled\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e78e\"\r\n        }\r\n      },\r\n      \"srd-copilot-code-disabled\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e78f\"\r\n        }\r\n      },\r\n      \"srd-copilot-select\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7b4\"\r\n        }\r\n      },\r\n      \"srd-copilot-delete\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e763\"\r\n        }\r\n      },\r\n      \"srd-copilot-refresh\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7f7\"\r\n        }\r\n      },\r\n      \"srd-copilot-logo\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e7b3\"\r\n        }\r\n      },\r\n      \"srd-copilot-cancel\": {\r\n        \"description\": \"SRD Copilot icon\",\r\n        \"default\": {\r\n          \"fontPath\": \"assets/iconfont.woff\",\r\n          \"fontCharacter\": \"\\\\e65f\"\r\n        }\r\n      }\r\n    },\r\n    \"iconFonts\": [\r\n      {\r\n        \"id\": \"srd-copilot-font\",\r\n        \"src\": [\r\n          {\r\n            \"path\": \"assets/iconfont.woff\",\r\n            \"format\": \"woff\"\r\n          }\r\n        ]\r\n      }\r\n    ],\r\n    \"configuration\": [\r\n      {\r\n        \"title\": \"海云安代码助手\"\r\n      },\r\n      {\r\n        \"title\": \"基础配置\",\r\n        \"properties\": {\r\n          \"通用.服务器扫描地址\": {\r\n            \"type\": \"string\",\r\n            \"order\": 0,\r\n            \"description\": \"协议://IP地址:端口/项目名,如 http://***********:3000/oscap\",\r\n            \"pattern\": \"^https?://.+\"\r\n          },\r\n          \"通用.登录状态\": {\r\n            \"type\": \"string\",\r\n            \"order\": 1,\r\n            \"default\": \"未登录\",\r\n            \"readOnly\": true,\r\n            \"markdownDescription\": \"[**点此登录**](command:secidea.login)  [**点此登出**](command:secidea.logout)  \\n  请先配置正确服务器扫描地址，登录成功后，下方会显示用户信息\"\r\n          },\r\n          \"通用.版本信息\": {\r\n            \"type\": \"object\",\r\n            \"order\": 2,\r\n            \"markdownDescription\": \"插件版本号：3.5.0-c10 [检查更新](command:srd-copilot.checkUpdate)\",\r\n            \"default\": {\r\n              \"新版本插件安装包存放目录\": \"\",\r\n              \"是否自动下载新版本\": false,\r\n              \"是否开启更新提醒\": false,\r\n              \"更新提醒间隔小时\": 1\r\n            },\r\n            \"properties\": {\r\n              \"新版本插件安装包存放目录\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"是否自动下载新版本\": {\r\n                \"type\": \"boolean\"\r\n              },\r\n              \"是否开启更新提醒\": {\r\n                \"type\": \"boolean\"\r\n              },\r\n              \"更新提醒间隔小时\": {\r\n                \"type\": \"number\"\r\n              }\r\n            },\r\n            \"additionalProperties\": false\r\n          }\r\n        }\r\n      },\r\n      {\r\n        \"title\": \"代码助手配置\",\r\n        \"properties\": {\r\n          \"代码助手.是否展示函数级别快捷键\": {\r\n            \"type\": \"boolean\",\r\n            \"default\": true,\r\n            \"description\": \"是否展示函数级别快捷键\"\r\n          },\r\n          \"代码助手.禁用补全语言\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"禁用补全的语言设置\",\r\n            \"items\": {\r\n              \"type\": \"object\",\r\n              \"properties\": {\r\n                \"语言\": {\r\n                  \"type\": \"string\",\r\n                  \"description\": \"语言\"\r\n                },\r\n                \"尾缀名\": {\r\n                  \"type\": \"string\",\r\n                  \"description\": \"尾缀名\"\r\n                },\r\n                \"是否禁用\": {\r\n                  \"type\": \"boolean\",\r\n                  \"description\": \"是否禁用\"\r\n                }\r\n              },\r\n              \"required\": [\r\n                \"语言\",\r\n                \"尾缀名\",\r\n                \"是否禁用\"\r\n              ]\r\n            },\r\n            \"default\": [\r\n              {\r\n                \"语言\": \"ABAP\",\r\n                \"尾缀名\": \"abap\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"ActionScript\",\r\n                \"尾缀名\": \"as\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Ada\",\r\n                \"尾缀名\": \"ada\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AsciiDoc\",\r\n                \"尾缀名\": \"adoc\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Makefile\",\r\n                \"尾缀名\": \"am\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AppleScript\",\r\n                \"尾缀名\": \"applescript\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Arc\",\r\n                \"尾缀名\": \"arc\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"ASP\",\r\n                \"尾缀名\": \"asp\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Assembly\",\r\n                \"尾缀名\": \"asm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AutoHotkey\",\r\n                \"尾缀名\": \"ahk\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"AutoIt\",\r\n                \"尾缀名\": \"au3\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Awk\",\r\n                \"尾缀名\": \"as\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Batch\",\r\n                \"尾缀名\": \"bat\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Bazel\",\r\n                \"尾缀名\": \"bzl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"BibTeX\",\r\n                \"尾缀名\": \"bib\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Bison\",\r\n                \"尾缀名\": \"bison\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"BitBake\",\r\n                \"尾缀名\": \"bb\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Blade\",\r\n                \"尾缀名\": \"blade.php\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Bash\",\r\n                \"尾缀名\": \"sh\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"C\",\r\n                \"尾缀名\": \"c\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"C#\",\r\n                \"尾缀名\": \"cs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"C++\",\r\n                \"尾缀名\": \"cpp\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CMake\",\r\n                \"尾缀名\": \"cmake\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"COBOL\",\r\n                \"尾缀名\": \"cob\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CoffeeScript\",\r\n                \"尾缀名\": \"coffee\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"ColdFusion\",\r\n                \"尾缀名\": \"cfm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Clojure\",\r\n                \"尾缀名\": \"clj\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CSS\",\r\n                \"尾缀名\": \"css\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CSV\",\r\n                \"尾缀名\": \"csv\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"CUDA\",\r\n                \"尾缀名\": \"cu\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"D\",\r\n                \"尾缀名\": \"d\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Dart\",\r\n                \"尾缀名\": \"dart\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Delphi\",\r\n                \"尾缀名\": \"dpr\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Pascal\",\r\n                \"尾缀名\": \"pas\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Diff\",\r\n                \"尾缀名\": \"diff\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Patch\",\r\n                \"尾缀名\": \"patch\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Dockerfile\",\r\n                \"尾缀名\": \"dockerfile\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"DTD\",\r\n                \"尾缀名\": \"dtd\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Erlang\",\r\n                \"尾缀名\": \"erl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Elixir\",\r\n                \"尾缀名\": \"ex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Elixir Script\",\r\n                \"尾缀名\": \"exs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Elm\",\r\n                \"尾缀名\": \"elm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"EEx\",\r\n                \"尾缀名\": \"eex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"LiveEEx\",\r\n                \"尾缀名\": \"leex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"F#\",\r\n                \"尾缀名\": \"fs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Fortran\",\r\n                \"尾缀名\": \"f\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Fortran90\",\r\n                \"尾缀名\": \"f90\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Fish\",\r\n                \"尾缀名\": \"fish\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Forth\",\r\n                \"尾缀名\": \"fth\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"GLSL\",\r\n                \"尾缀名\": \"glsl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Go\",\r\n                \"尾缀名\": \"go\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"GraphQL\",\r\n                \"尾缀名\": \"gql\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Groovy\",\r\n                \"尾缀名\": \"groovy\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Gradle\",\r\n                \"尾缀名\": \"gradle\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Haml\",\r\n                \"尾缀名\": \"haml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Haskell\",\r\n                \"尾缀名\": \"hs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HCL\",\r\n                \"尾缀名\": \"hcl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HLSL\",\r\n                \"尾缀名\": \"hlsl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HTML\",\r\n                \"尾缀名\": \"html\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HTML\",\r\n                \"尾缀名\": \"htm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"HTTP\",\r\n                \"尾缀名\": \"http\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Haxe\",\r\n                \"尾缀名\": \"hx\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"INI\",\r\n                \"尾缀名\": \"ini\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Java\",\r\n                \"尾缀名\": \"java\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"JavaScript\",\r\n                \"尾缀名\": \"js\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"JSX\",\r\n                \"尾缀名\": \"jsx\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"JSON\",\r\n                \"尾缀名\": \"json\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Julia\",\r\n                \"尾缀名\": \"jl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Kotlin\",\r\n                \"尾缀名\": \"kt\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Kotlin Script\",\r\n                \"尾缀名\": \"kts\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"LaTeX\",\r\n                \"尾缀名\": \"tex\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Less\",\r\n                \"尾缀名\": \"less\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Lisp\",\r\n                \"尾缀名\": \"lisp\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Lua\",\r\n                \"尾缀名\": \"lua\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"MATLAB/Objective-C\",\r\n                \"尾缀名\": \"m\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Objective-C++\",\r\n                \"尾缀名\": \"mm\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Markdown\",\r\n                \"尾缀名\": \"md\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Makefile\",\r\n                \"尾缀名\": \"makefile\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Nix\",\r\n                \"尾缀名\": \"nix\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"OCaml\",\r\n                \"尾缀名\": \"ml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Perl\",\r\n                \"尾缀名\": \"pl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"PHP\",\r\n                \"尾缀名\": \"php\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"PowerShell\",\r\n                \"尾缀名\": \"ps1\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Protocol Buffers\",\r\n                \"尾缀名\": \"proto\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Python\",\r\n                \"尾缀名\": \"py\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"R\",\r\n                \"尾缀名\": \"r\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Ruby\",\r\n                \"尾缀名\": \"rb\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Rust\",\r\n                \"尾缀名\": \"rs\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Sass\",\r\n                \"尾缀名\": \"sass\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Scala\",\r\n                \"尾缀名\": \"scala\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"SCSS\",\r\n                \"尾缀名\": \"scss\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"SQL\",\r\n                \"尾缀名\": \"sql\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Stylus\",\r\n                \"尾缀名\": \"styl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"SVG\",\r\n                \"尾缀名\": \"svg\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Swift\",\r\n                \"尾缀名\": \"swift\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Tcl\",\r\n                \"尾缀名\": \"tcl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Terraform\",\r\n                \"尾缀名\": \"tf\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"TypeScript\",\r\n                \"尾缀名\": \"ts\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"TSX\",\r\n                \"尾缀名\": \"tsx\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Twig\",\r\n                \"尾缀名\": \"twig\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Text\",\r\n                \"尾缀名\": \"txt\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"VB.NET\",\r\n                \"尾缀名\": \"vb\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Vue\",\r\n                \"尾缀名\": \"vue\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"XML\",\r\n                \"尾缀名\": \"xml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"XSLT\",\r\n                \"尾缀名\": \"xsl\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"YAML\",\r\n                \"尾缀名\": \"yaml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"YAML\",\r\n                \"尾缀名\": \"yml\",\r\n                \"是否禁用\": false\r\n              },\r\n              {\r\n                \"语言\": \"Zig\",\r\n                \"尾缀名\": \"zig\",\r\n                \"是否禁用\": false\r\n              }\r\n            ],\r\n            \"markdownDescription\": \"配置禁用代码补全功能的语言类型\",\r\n            \"order\": 2\r\n          },\r\n          \"代码助手.补全模式\": {\r\n            \"type\": [\r\n              \"string\"\r\n            ],\r\n            \"enum\": [\r\n              \"精准优先\",\r\n              \"平衡模式\",\r\n              \"速度优先\"\r\n            ],\r\n            \"default\": \"精准优先\",\r\n            \"markdownDescription\": \"选择使用代码助手进行补全的模式\",\r\n            \"order\": 1\r\n          },\r\n          \"代码助手.快捷键配置\": {\r\n            \"type\": [\r\n              \"string\"\r\n            ],\r\n            \"enum\": [\r\n              \"默认\",\r\n              \"自定义\"\r\n            ],\r\n            \"default\": \"默认\",\r\n            \"enumDescriptions\": [\r\n              \"使用`Ctrl + \\\\`键显示补全信息，使用`Tab`键接受完整补全 \\n使用`Ctrl + Alt + /`键接受下一行\",\r\n              \"自定义快捷键\"\r\n            ],\r\n            \"markdownDescription\": \"配置代码助手补全对应的快捷键\",\r\n            \"order\": 3\r\n          },\r\n          \"代码助手.自定义指令\": {\r\n            \"type\": \"string\",\r\n            \"maxLength\": 1500,\r\n            \"description\": \"需要代码助手了解哪些信息，或如何进行回答。例如：我是一名软件开发者，当我问安全问题时，如果回复内容包含代码，请解释这些代码；但如果仅需要提供功能代码时，只提供代码。\",\r\n            \"order\": 4\r\n          }\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  \"scripts\": {\r\n    \"vsce:package\": \"node scripts/switch-assets.js && vsce package\",\r\n    \"vsce:package:prod\": \"cross-env NODE_ENV=production node scripts/switch-assets.js && vsce package\",\r\n    \"vscode:prepublish\": \"yarn run clean:dist && yarn run package\",\r\n    \"compile\": \"yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run build-codechat\",\r\n    \"package\": \"node scripts/switch-assets.js && webpack --mode production --devtool hidden-source-map && yarn run build-codechat\",\r\n    \"watch\": \"yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run watch-codechat\",\r\n    \"watch-vscode\": \"yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && webpack --watch\",\r\n    \"watch-vscode-prod\": \"yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && cross-env NODE_ENV=production webpack --watch\",\r\n    \"watch-codechat\": \"cd webview/codechat && yarn run build-watch\",\r\n    \"build-codechat\": \"cd webview/codechat && yarn run build\",\r\n    \"clean:dist\": \"rimraf dist\",\r\n    \"compile-tests\": \"tsc -p . --outDir out\",\r\n    \"watch-tests\": \"tsc -p . -w --outDir out\",\r\n    \"pretest\": \"yarn run compile-tests && yarn run compile && yarn run lint\",\r\n    \"lint\": \"eslint ./src --ext ts --fix\",\r\n    \"test\": \"node ./out/test/runTest.js\"\r\n  },\r\n  \"devDependencies\": {\r\n    \"@types/fs-extra\": \"^11.0.4\",\r\n    \"@types/glob\": \"^8.1.0\",\r\n    \"@types/mocha\": \"^10.0.1\",\r\n    \"@types/node\": \"20.2.5\",\r\n    \"@types/proper-lockfile\": \"^4.1.4\",\r\n    \"@types/tmp\": \"^0.2.3\",\r\n    \"@types/vscode\": \"^1.68.1\",\r\n    \"@types/ws\": \"^8.5.5\",\r\n    \"@typescript-eslint/eslint-plugin\": \"^5.59.8\",\r\n    \"@typescript-eslint/parser\": \"^5.59.8\",\r\n    \"@vscode/test-electron\": \"^2.3.2\",\r\n    \"cross-env\": \"^7.0.3\",\r\n    \"dotenv-webpack\": \"^8.0.1\",\r\n    \"eslint\": \"^8.41.0\",\r\n    \"eslint-config-prettier\": \"^8.6.0\",\r\n    \"eslint-plugin-prettier\": \"^4.2.1\",\r\n    \"glob\": \"^8.1.0\",\r\n    \"mocha\": \"^10.2.0\",\r\n    \"prettier\": \"^2.8.3\",\r\n    \"rimraf\": \"^5.0.5\",\r\n    \"ts-loader\": \"^9.4.3\",\r\n    \"typescript\": \"^5.1.3\",\r\n    \"vsce\": \"^2.15.0\",\r\n    \"webpack\": \"^5.85.0\",\r\n    \"webpack-cli\": \"^5.1.1\"\r\n  },\r\n  \"dependencies\": {\r\n    \"@ai-zen/node-fetch-event-source\": \"2.1.0\",\r\n    \"@types/archiver\": \"^6.0.2\",\r\n    \"@types/diff\": \"^7.0.2\",\r\n    \"@types/markdown-it-attrs\": \"^4.1.3\",\r\n    \"abort-controller\": \"^3.0.0\",\r\n    \"archiver\": \"^7.0.1\",\r\n    \"axios\": \"^1.8.4\",\r\n    \"diff\": \"^7.0.0\",\r\n    \"dotenv\": \"^8.6.0\",\r\n    \"form-data\": \"^4.0.0\",\r\n    \"fs-extra\": \"^11.3.0\",\r\n    \"fuse.js\": \"6.4.6\",\r\n    \"http-proxy-agent\": \"^7.0.0\",\r\n    \"macaddress\": \"^0.5.3\",\r\n    \"markdown-it-attrs\": \"^4.1.6\",\r\n    \"node-fetch\": \"^3.3.2\",\r\n    \"node-machine-id\": \"^1.1.12\",\r\n    \"proper-lockfile\": \"^4.1.2\",\r\n    \"rxjs\": \"^7.8.2\",\r\n    \"uuid\": \"^11.1.0\",\r\n    \"web-tree-sitter\": \"^0.23.0\",\r\n    \"ws\": \"^8.14.0\",\r\n    \"zip-lib\": \"^1.0.5\"\r\n  },\r\n  \"optionalDependencies\": {\r\n    \"bufferutil\": \"^4.0.7\",\r\n    \"utf-8-validate\": \"^6.0.3\"\r\n  },\r\n  \"workspaces\": [\r\n    \"webview/*\"\r\n  ],\r\n  \"__metadata\": {\r\n    \"publisherDisplayName\": \"海云安\"\r\n  }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/package-sec.json b/package-sec.json
--- a/package-sec.json	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/package-sec.json	(date 1750820687688)
@@ -1017,10 +1017,10 @@
             ],
             "default": "默认",
             "enumDescriptions": [
-              "使用`Ctrl + \\`键显示补全信息，使用`Tab`键接受完整补全 \n使用`Ctrl + Alt + /`键接受下一行",
+              "`Ctrl + Enter` 手动发起补全请求\n`Tab` 选择当前补全建议\n`Alt + [` 或 `Alt + ]` 查看上一条/下一条补全建议\n`Ctrl + Alt + Shift + O` 启用/禁用代码补全",
               "自定义快捷键"
             ],
-            "markdownDescription": "配置代码助手补全对应的快捷键",
+            "markdownDescription": "配置代码助手对应的补全快捷键",
             "order": 3
           },
           "代码助手.自定义指令": {
Index: src/codecomplete/codeCompleteEngin.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { QuestionType } from '../common/constants';\r\nimport { AskQuestionParams } from '../service/types/questionTask';\r\nimport { Logger } from '../utils/logger';\r\nimport { AutocompleteParams, HistoryAnswer, AutocompleteResult } from './types';\r\nimport { IStatusBarHandler } from '../statusbar/types';\r\nimport { getEndOfLine } from '../utils/common';\r\nimport { END_OF_LINE } from '../common/config';\r\nimport { CodeCompletionStrategy } from './completionStrategy/codeCompletionStrategy';\r\nimport { DefaultCodeCompletionStrategy } from './completionStrategy/defaultCodeCompletionStrategy';\r\nimport { AgentCodeCompletionStrategy } from './completionStrategy/agentCompletionStrategy';\r\n\r\nexport class CodeCompleteEngin {\r\n  private strategies: Map<string, CodeCompletionStrategy>;\r\n\r\n  private statusHandler: IStatusBarHandler | null = null;\r\n\r\n  // 上次位置标识\r\n  private lastReqParamKey = '';\r\n\r\n  // 当前位置标识\r\n  private curReqParamKey = '';\r\n\r\n  // 位置标识与回答列表映射\r\n  private reqParamKeyAnswerMap: Map<string, HistoryAnswer[]> = new Map<string, HistoryAnswer[]>();\r\n\r\n  constructor() {\r\n    this.strategies = new Map<string, CodeCompletionStrategy>();\r\n    this.strategies.set('default', new DefaultCodeCompletionStrategy());\r\n    this.strategies.set('agent', new AgentCodeCompletionStrategy());\r\n  }\r\n\r\n  /**\r\n   * 设置enginHandler，目前只通知事件到statusbar\r\n   * @param handler\r\n   */\r\n  public setStatusBarHandler(handler: IStatusBarHandler | null) {\r\n    this.statusHandler = handler;\r\n  }\r\n\r\n  /**\r\n   * 发起代码补全请求\r\n   * @param request\r\n   * @returns\r\n   */\r\n  public async askCompletion(request: AutocompleteParams) {\r\n    this.curReqParamKey = this.getReqParamKey(request);\r\n\r\n    var result;\r\n    if (this.strategies.get('agent')?.isEnabled()) {\r\n      Logger.info(\"[CodeCompleteEngin]: ask from agent\")\r\n      result = await this.strategies.get('agent')?.getCodeCompletion(this, request)\r\n    } else {\r\n      Logger.info(\"[CodeCompleteEngin]: ask from default\")\r\n      result = await this.strategies.get('default')?.getCodeCompletion(this, request)\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * 清除同一光标位置历史回答\r\n   * @param reqParamKey\r\n   */\r\n  public clearReqParamKeyAnswerCache(reqParamKey?: string) {\r\n    if (reqParamKey) {\r\n      this.reqParamKeyAnswerMap.delete(reqParamKey);\r\n    } else {\r\n      this.reqParamKeyAnswerMap.clear();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 根据不同场景获取不同的stopWords\r\n   * @param questionType 请求类型\r\n   * @param request 请求参数\r\n   */\r\n  public getStopWords(\r\n    questionType: QuestionType,\r\n    request: AutocompleteParams\r\n  ): string[] | undefined {\r\n    let stopWords, words;\r\n    const { isMidline, endOfLine } = this.checkIsMidlinePosition(request.before, request.after);\r\n\r\n    switch (questionType) {\r\n      // 自动补全：当前行的中间触发，取当前位置后面实质字符; 其它情况取'\\n'\r\n      case QuestionType.CODE_GEN:\r\n        words = isMidline ? request.after.split(endOfLine)[0] : END_OF_LINE;\r\n        break;\r\n      // 手动补全：\r\n      // 1) 当前行的中间触发，取当前位置后面实质字符;\r\n      // 2) 行尾触发寻找当前位置所在行以下的第一个非空行;如果都是空行，取'\\n'\r\n      case QuestionType.CODE_GEN_MANUAL:\r\n        if (isMidline) {\r\n          words = request.after.split(endOfLine)[0];\r\n        } else {\r\n          const lineArr = request.after.split(endOfLine);\r\n          const idx = lineArr.findIndex(line => !!line);\r\n\r\n          if (idx > -1 && endOfLine) {\r\n            words = lineArr.slice(0, idx + 1).join(endOfLine);\r\n          } else {\r\n            words = END_OF_LINE;\r\n          }\r\n        }\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    if (words) {\r\n      if (Array.isArray(words)) {\r\n        stopWords = words;\r\n      } else { \r\n        stopWords = [words];\r\n      }\r\n    }\r\n\r\n    return stopWords;\r\n  }\r\n\r\n  /**\r\n   * 构建代码补全返回数据结构\r\n   * @param answer\r\n   * @returns\r\n   */\r\n  public buildCodeCompleteResult(\r\n    answer = '',\r\n    isCurReqId?: boolean,\r\n    curReqParams?: AskQuestionParams\r\n  ): AutocompleteResult | undefined {\r\n    let newAnswers: string[] = [],\r\n      historyAnswers: HistoryAnswer[] = [];\r\n\r\n    const { isSamePosition, curIsAuto, curKey, lastKey } = this.checkIsSamePosition();\r\n\r\n    // 光标位置不变，先获取历史回答\r\n    if (isSamePosition && lastKey) {\r\n      historyAnswers = this.reqParamKeyAnswerMap.get(lastKey) || [];\r\n\r\n      // 当前是自动触发，需要去除手动补全的回答\r\n      if (curIsAuto) {\r\n        historyAnswers = historyAnswers.filter(ans => !!ans.isAuto);\r\n      }\r\n    } else {\r\n      // 光标位置改变，可以清除光标位置历史回答\r\n      this.clearReqParamKeyAnswerCache();\r\n    }\r\n\r\n    // 当前回答有效，分情况返回答案\r\n    if (answer) {\r\n      // 自动补全时，只保留一行回答\r\n      const newAnswer = curIsAuto ? this.filterAutoAnswer(answer) : answer;\r\n      const newHistoryAnswer = { answer: newAnswer, isAuto: curIsAuto };\r\n      const answerHasExisted = !!historyAnswers.find(ans => ans.answer === answer);\r\n\r\n      // 1) 光标位置不变且是当前问题，当前回答和历史一起返回\r\n      if (isSamePosition && isCurReqId) {\r\n        newAnswers = historyAnswers.map(ans => ans.answer);\r\n\r\n        if (!answerHasExisted) {\r\n          newAnswers = [newAnswer].concat(newAnswers);\r\n          historyAnswers.unshift(newHistoryAnswer);\r\n          this.reqParamKeyAnswerMap.set(curKey, historyAnswers);\r\n        }\r\n      } else if (isSamePosition && !isCurReqId && !answerHasExisted) {\r\n        // 2) 光标位置不变但不是当前问题，记录到历史回答，不返回\r\n        historyAnswers.unshift(newHistoryAnswer);\r\n        this.reqParamKeyAnswerMap.set(curKey, historyAnswers);\r\n      } else if (!isSamePosition && isCurReqId) {\r\n        // 3) 光标位置变化且是当前问题，记录到新位置历史回答，当前回答返回\r\n        newAnswers.unshift(newAnswer);\r\n        this.reqParamKeyAnswerMap.set(curKey, [newHistoryAnswer]);\r\n      } else {\r\n        // 4) 其它，忽略不处理\r\n      }\r\n    }\r\n\r\n    if (isCurReqId) {\r\n      this.lastReqParamKey = this.curReqParamKey;\r\n    }\r\n\r\n    if (newAnswers.length > 0) {\r\n      const oldPrefix = isCurReqId && curReqParams ? curReqParams.prefix || '' : '';\r\n\r\n      return {\r\n        old_prefix: oldPrefix || '',\r\n        results: newAnswers.map(ans => ({\r\n          new_prefix: oldPrefix + ans,\r\n          old_suffix: '',\r\n          new_suffix: '',\r\n          answer: ans,\r\n        })),\r\n      };\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  /**\r\n   * 获取当前问题光标位置标识\r\n   * @param request\r\n   * @returns\r\n   */\r\n  private getReqParamKey(request: AutocompleteParams) {\r\n    return `${request.filename}__${request.line}__${request.character}??${request.isAuto}`;\r\n  }\r\n\r\n  /**\r\n   * 分隔光标位置标识，返回位置信息和是否自动补全\r\n   * @param keyStr\r\n   * @returns\r\n   */\r\n  private splitReqParamKey(keyStr: string) {\r\n    if (!keyStr) {\r\n      return {};\r\n    }\r\n\r\n    const splitArr = keyStr.split('??');\r\n\r\n    return {\r\n      key: splitArr[0],\r\n      isAuto: splitArr[1] === 'true',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 检查光标位置是否没变\r\n   * @returns\r\n   */\r\n  private checkIsSamePosition() {\r\n    const { key: curKey, isAuto: curIsAuto } = this.splitReqParamKey(this.curReqParamKey);\r\n    const { key: lastKey, isAuto: lastIsAuto } = this.splitReqParamKey(this.lastReqParamKey);\r\n\r\n    return {\r\n      isSamePosition: lastKey && curKey === lastKey,\r\n      curKey: curKey as string,\r\n      curIsAuto: !!curIsAuto,\r\n      lastKey,\r\n      lastIsAuto,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 自动补全应去掉\\n或\\r\\n后面的值，且不包含\\n以及\\r\\n\r\n   * @param answer\r\n   */\r\n  private filterAutoAnswer(answer: string): string {\r\n    const endOfLine = getEndOfLine(answer);\r\n\r\n    if (endOfLine) {\r\n      return answer.split(endOfLine)?.[0] || '';\r\n    }\r\n\r\n    return answer;\r\n  }\r\n\r\n  /**\r\n   * 发起补全的位置是否在行中以及文本的换行符\r\n   * @param prefix\r\n   * @param suffix\r\n   * @returns\r\n   */\r\n  private checkIsMidlinePosition(\r\n    prefix: string,\r\n    suffix: string\r\n  ): { isMidline: boolean; endOfLine: string } {\r\n    const endOfLine = getEndOfLine(prefix + suffix);\r\n\r\n    return {\r\n      isMidline: suffix.indexOf(endOfLine) > 0,\r\n      endOfLine,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 正则表达式匹配整个字符串，确保它只包含换行符(\\n)和空格\r\n   * @param answer\r\n   * @returns\r\n   */\r\n  private isOnlyNewlinesOrSpaces(answer: string): boolean {\r\n    const regex = /^[\\n ]*$/;\r\n    return regex.test(answer);\r\n  }\r\n\r\n  /**\r\n   * 定制化处理代码补全结果\r\n   * @param answer\r\n   * @returns\r\n   */\r\n  public handleCompleteAnswer(answer: string) {\r\n    Logger.debug(\r\n      `[CodeCompleteEngin] handleCompleteAnswer, answer:${answer}, length:${answer.length}`\r\n    );\r\n    if (this.isOnlyNewlinesOrSpaces(answer)) {\r\n      Logger.debug(\r\n        `[CodeCompleteEngin] handleCompleteAnswer, isOnlyNewlinesOrSpaces:${this.isOnlyNewlinesOrSpaces(\r\n          answer\r\n        )}`\r\n      );\r\n      return '';\r\n    }\r\n    return answer;\r\n  }\r\n\r\n  public getStatusBarHandler(): IStatusBarHandler | null {\r\n    return this.statusHandler;\r\n  }\r\n}\r\n\r\nexport const CodeCompleteEnginInst = new CodeCompleteEngin();\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/codecomplete/codeCompleteEngin.ts b/src/codecomplete/codeCompleteEngin.ts
--- a/src/codecomplete/codeCompleteEngin.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/codecomplete/codeCompleteEngin.ts	(date 1750815032108)
@@ -46,13 +46,13 @@
     this.curReqParamKey = this.getReqParamKey(request);
 
     var result;
-    if (this.strategies.get('agent')?.isEnabled()) {
-      Logger.info("[CodeCompleteEngin]: ask from agent")
+    // if (this.strategies.get('agent')?.isEnabled()) {
+    //   Logger.info("[CodeCompleteEngin]: ask from agent")
       result = await this.strategies.get('agent')?.getCodeCompletion(this, request)
-    } else {
-      Logger.info("[CodeCompleteEngin]: ask from default")
-      result = await this.strategies.get('default')?.getCodeCompletion(this, request)
-    }
+    // } else {
+      // Logger.info("[CodeCompleteEngin]: ask from default")
+    //   result = await this.strategies.get('default')?.getCodeCompletion(this, request)
+    // }
 
     return result;
   }
Index: .vscode/launch.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// A launch configuration that compiles the extension and then opens it inside a new window\r\n// Use IntelliSense to learn about possible attributes.\r\n// Hover to view descriptions of existing attributes.\r\n// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387\r\n{\r\n\t\"version\": \"0.2.0\",\r\n\t\"configurations\": [\r\n\t\t{\r\n\t\t\t\"name\": \"Run Extension\",\r\n\t\t\t\"type\": \"extensionHost\",\r\n\t\t\t\"request\": \"launch\",\r\n\t\t\t\"args\": [\r\n\t\t\t\t\"--extensionDevelopmentPath=${workspaceFolder}\",\r\n\t\t\t\t\"--enable-proposed-api Secidea.Secidea\",\r\n\t\t\t],\r\n\t\t\t\"outFiles\": [\r\n\t\t\t\t\"${workspaceFolder}/dist/**/*.js\"\r\n\t\t\t],\r\n\t\t\t// \"preLaunchTask\": \"tasks: watch\",\r\n\t\t\t\"preLaunchTask\": \"${defaultBuildTask}\",\r\n\t\t\t\"trace\": true\r\n\t\t},\r\n\t\t{\r\n\t\t\t\"name\": \"Extension Tests\",\r\n\t\t\t\"type\": \"extensionHost\",\r\n\t\t\t\"request\": \"launch\",\r\n\t\t\t\"args\": [\r\n\t\t\t\t\"--extensionDevelopmentPath=${workspaceFolder}\",\r\n\t\t\t\t\"--extensionTestsPath=${workspaceFolder}/out/test/suite/index\"\r\n\t\t\t],\r\n\t\t\t\"outFiles\": [\r\n\t\t\t\t\"${workspaceFolder}/out/**/*.js\",\r\n\t\t\t\t\"${workspaceFolder}/dist/**/*.js\"\r\n\t\t\t],\r\n\t\t\t// \"preLaunchTask\": \"tasks: watch\"\r\n\t\t\t\"preLaunchTask\": \"tasks: watch-tests\"\r\n\t\t}\r\n\t]\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.vscode/launch.json b/.vscode/launch.json
--- a/.vscode/launch.json	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/.vscode/launch.json	(date 1750647643758)
@@ -16,7 +16,6 @@
 			"outFiles": [
 				"${workspaceFolder}/dist/**/*.js"
 			],
-			// "preLaunchTask": "tasks: watch",
 			"preLaunchTask": "${defaultBuildTask}",
 			"trace": true
 		},
@@ -32,7 +31,6 @@
 				"${workspaceFolder}/out/**/*.js",
 				"${workspaceFolder}/dist/**/*.js"
 			],
-			// "preLaunchTask": "tasks: watch"
 			"preLaunchTask": "tasks: watch-tests"
 		}
 	]
Index: src/settings/utils.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { LanguageConfig } from './type';\r\nimport { AgentManager } from '../agent/agentmanager';\r\nimport { Logger } from '../utils/logger';\r\n\r\nexport const languageDisableMap: Map<string, boolean> = new Map();\r\n\r\nexport function isExtensionDisabled(extension: string): boolean {\r\n  return languageDisableMap.get(extension) ?? false;\r\n}\r\nexport function generateWeakUniqueNumber(): number {\r\n  // 获取当前时间的毫秒时间戳\r\n  const timestamp = Date.now();\r\n  // 生成一个0到1000之间的随机数（不含1000）\r\n  const randomPart = Math.floor(Math.random() * 1000);\r\n  // 将时间戳和随机数组合起来得到一个相对唯一的数字\r\n  const uniqueNumber = timestamp * 1000 + randomPart; // 乘以1000是为了与时间戳区分，避免因随机数太小导致的直接相加可能产生的重复\r\n  return uniqueNumber;\r\n}\r\n\r\nexport async function updateClientPropertiesEndpoint(endpoint: string) {\r\n  const updateConfigRequest = [\r\n    generateWeakUniqueNumber(),\r\n    {\r\n      func: 'updateConfig',\r\n      args: ['server.endpoint', endpoint],\r\n    },\r\n  ];\r\n\r\n  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');\r\n  if (tabbyClient) {\r\n    tabbyClient.request('updateConfig', ['server.endpoint', endpoint], null, (data: any) => {\r\n      Logger.debug(`updateClientPropertiesEndpoint response: ${JSON.stringify(data)}`);\r\n    });\r\n  } else {\r\n    Logger.warn('updateClientPropertiesEndpoint: tabbyClient not available');\r\n  }\r\n\r\n  console.debug('update endpoint');\r\n}\r\nexport async function clearClientPropertiesEndpoint() {\r\n  const clearConfigRequest = [\r\n    generateWeakUniqueNumber(),\r\n    {\r\n      func: 'clearConfig',\r\n      args: ['server.endpoint'],\r\n    },\r\n  ];\r\n\r\n  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');\r\n  if (tabbyClient) {\r\n    tabbyClient.request('clearConfig', ['server.endpoint'], null, (data: any) => {\r\n      Logger.debug(`clearClientPropertiesEndpoint response: ${JSON.stringify(data)}`);\r\n    });\r\n  } else {\r\n    Logger.warn('clearClientPropertiesEndpoint: tabbyClient not available');\r\n  }\r\n\r\n  console.debug('clear endpoint');\r\n}\r\n\r\nexport async function updateClientPropertiesTriggerMode(mode: string) {\r\n  const id = generateWeakUniqueNumber();\r\n  const request = [\r\n    id,\r\n    {\r\n      func: 'updateClientProperties',\r\n      args: ['user', 'vscode.triggerMode', mode],\r\n    },\r\n  ];\r\n\r\n  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');\r\n  if (tabbyClient) {\r\n    tabbyClient.request('updateClientProperties', ['user', 'vscode.triggerMode', mode], null, (data: any) => {\r\n      Logger.debug(`updateClientPropertiesTriggerMode response: ${JSON.stringify(data)}`);\r\n    });\r\n  } else {\r\n    Logger.warn('updateClientPropertiesTriggerMode: tabbyClient not available');\r\n  }\r\n\r\n  console.debug('update triggermode');\r\n}\r\nexport async function updateClientPropertiesKeybinding(binding: string) {\r\n  const id = generateWeakUniqueNumber();\r\n  const request = [\r\n    id,\r\n    {\r\n      func: 'updateClientProperties',\r\n      args: ['user', 'vscode.keybindings', binding],\r\n    },\r\n  ];\r\n\r\n  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');\r\n  if (tabbyClient) {\r\n    tabbyClient.request('updateClientProperties', ['user', 'vscode.keybindings', binding], null, (data: any) => {\r\n      Logger.debug(`updateClientPropertiesKeybinding response: ${JSON.stringify(data)}`);\r\n    });\r\n  } else {\r\n    Logger.warn('updateClientPropertiesKeybinding: tabbyClient not available');\r\n  }\r\n\r\n  console.debug('update keybinding');\r\n}\r\nexport async function handleDisableLanguageChange(newConfig: LanguageConfig[]): Promise<void> {\r\n  languageDisableMap.clear();\r\n\r\n  if (Array.isArray(newConfig)) {\r\n    newConfig.forEach(item => {\r\n      languageDisableMap.set(item.尾缀名, item.是否禁用);\r\n    });\r\n  }\r\n  console.debug(\r\n    'update languageDisableMap: ' + JSON.stringify(Array.from(languageDisableMap.entries()))\r\n  );\r\n}\r\nexport async function updateClientPropertiesCplMode(mode: string) {\r\n  const updateCplModeRequest = [\r\n    generateWeakUniqueNumber(),\r\n    {\r\n      func: 'updateConfig',\r\n      args: ['server.cplMode', mode],\r\n    },\r\n  ];\r\n\r\n  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');\r\n  if (tabbyClient) {\r\n    tabbyClient.request('updateConfig', ['server.cplMode', mode], null, (data: any) => {\r\n      Logger.debug(`updateClientPropertiesCplMode response: ${JSON.stringify(data)}`);\r\n    });\r\n  } else {\r\n    Logger.warn('updateClientPropertiesCplMode: tabbyClient not available');\r\n  }\r\n\r\n  console.debug('update cplMode');\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/settings/utils.ts b/src/settings/utils.ts
--- a/src/settings/utils.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/settings/utils.ts	(date 1750820453771)
@@ -79,27 +79,7 @@
 
   console.debug('update triggermode');
 }
-export async function updateClientPropertiesKeybinding(binding: string) {
-  const id = generateWeakUniqueNumber();
-  const request = [
-    id,
-    {
-      func: 'updateClientProperties',
-      args: ['user', 'vscode.keybindings', binding],
-    },
-  ];
 
-  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
-  if (tabbyClient) {
-    tabbyClient.request('updateClientProperties', ['user', 'vscode.keybindings', binding], null, (data: any) => {
-      Logger.debug(`updateClientPropertiesKeybinding response: ${JSON.stringify(data)}`);
-    });
-  } else {
-    Logger.warn('updateClientPropertiesKeybinding: tabbyClient not available');
-  }
-
-  console.debug('update keybinding');
-}
 export async function handleDisableLanguageChange(newConfig: LanguageConfig[]): Promise<void> {
   languageDisableMap.clear();
 
Index: src/agent/agentmanager.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import * as path from 'path';\r\nimport { SystemUtils } from './utils/system';\r\nimport { AgentDownloader } from './services/downloader';\r\nimport { VersionChecker } from './services/versionChecker';\r\nimport { ProcessManager } from './services/processManager';\r\nimport { AgentConfig, CORE_AGENT, TABBY_AGENT } from './types';\r\nimport { AgentCommClient } from './commclient/agentcommclient';\r\nimport { IAgentMessageReceiver } from './commclient/MessageReceiver';\r\nimport { TabbyAgentProcess } from './process/TabbyAgentProcess';\r\nimport { CoreAgentProcess } from './process/CoreAgentProcess';\r\nimport { Logger } from '../utils/logger';\r\nimport { TabbyAgentClient } from './commclient/tabbyAgentClient';\r\nimport { CoreAgentClient } from './commclient/coreAgentClient';\r\nimport { CoreAgentMessageReceiver } from './commclient/coreAgentReceiver';\r\nimport { TabbyAgentMessageReceiver } from './commclient/tabbyAgentReceiver';\r\nimport { FileLockUtil } from './utils/lockUtil';\r\nimport { IStatusBarHandler } from '../statusbar/types';\r\nimport { AgentStatus } from '../common/constants';\r\nimport * as vscode from 'vscode';\r\nimport { AgentProcess } from './process/AgentProcess';\r\n\r\nexport class AgentManager {\r\n  private config: Map<string, AgentConfig> = new Map<string, AgentConfig>();\r\n\r\n  private downloader!: AgentDownloader;\r\n\r\n  private versionChecker!: VersionChecker;\r\n\r\n  private processManager!: ProcessManager;\r\n\r\n  private pluginName!: string;\r\n\r\n  private agentCommClients: Map<string, AgentCommClient> = new Map();\r\n\r\n  private messageReceivers!: Map<string, IAgentMessageReceiver>;\r\n\r\n  private statusBarHandler: IStatusBarHandler | null = null;\r\n  private agentProcessMap: Map<string, AgentProcess> = new Map();\r\n  \r\n\r\n  // 单例模式\r\n  private static instance: AgentManager | null;\r\n\r\n  public constructor() { }\r\n\r\n  public static getInstance(): AgentManager {\r\n    if (AgentManager.instance == null) {\r\n      AgentManager.instance = new AgentManager();\r\n    }\r\n    return AgentManager.instance;\r\n  }\r\n\r\n  public init(\r\n    config: Map<string, AgentConfig>,\r\n    pluginName: string,\r\n    messageReceivers: Map<string, IAgentMessageReceiver>\r\n  ) {\r\n    this.config = config;\r\n    this.pluginName = pluginName;\r\n    this.messageReceivers = messageReceivers;\r\n    this.downloader = new AgentDownloader();\r\n    this.versionChecker = new VersionChecker();\r\n    this.processManager = new ProcessManager(config, this); // Pass AgentManager instance\r\n  }\r\n\r\n  public async initialize(): Promise<void> {\r\n    //启动需要的agent进程\r\n    const agentProcessMap = new Map([\r\n      ['core', new CoreAgentProcess()],\r\n      ['tabby', new TabbyAgentProcess()]\r\n    ]);\r\n    this.agentProcessMap = agentProcessMap;\r\n    for (const [agentName, agent] of agentProcessMap) {\r\n      const agentPath = SystemUtils.getAgentPath(this.pluginName, agent.getAgentName());\r\n\r\n      try {\r\n        // 启动agent进程\r\n        const process = await this.processManager.startAgent(agent, agentPath, 0);\r\n\r\n        // 创建并存储AgentCommClient实例\r\n        const messageReceiver = this.messageReceivers.get(agent.getAgentName());\r\n        if (!messageReceiver) {\r\n          throw new Error(`No message receiver found for agent: ${agent.getAgentName()}`);\r\n        }\r\n        if (agentName === TABBY_AGENT) {\r\n          const commClient = new TabbyAgentClient(process, messageReceiver);\r\n          this.agentCommClients.set(agentName, commClient);\r\n          continue;\r\n        } \r\n        const commClient = new AgentCommClient(process, messageReceiver);\r\n        this.agentCommClients.set(agent.getAgentName(), commClient);\r\n      } catch (error) {\r\n        // 处理启动失败情况\r\n        Logger.error(`[AgentManager] 启动${agentName}进程失败: ${error}`);\r\n        \r\n        // 通知状态栏启动失败\r\n        if (this.statusBarHandler) {\r\n          let errorMessage = '服务启动失败';\r\n          if (error instanceof Error) {\r\n            errorMessage = error.message;\r\n          } else if (typeof error === 'string') {\r\n            errorMessage = error;\r\n          } else if (error && typeof error === 'object' && 'message' in error) {\r\n            errorMessage = String(error.message);\r\n          }\r\n          this.statusBarHandler.onAgentStatusChange(AgentStatus.STARTUP_FAILED, errorMessage);\r\n        }\r\n        \r\n        // 继续抛出异常或者根据需要决定是否继续\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  public onAgentRestarted(agentName: string, newProcess: any): void {\r\n    // Handle agent restart notification from ProcessManager\r\n    const existingClient = this.agentCommClients.get(agentName);\r\n    if (existingClient) {\r\n      existingClient.shutdown();\r\n      this.agentCommClients.delete(agentName);\r\n    }\r\n\r\n    const messageReceiver = this.messageReceivers.get(agentName);\r\n    if (messageReceiver) {\r\n      if (agentName === TABBY_AGENT) {\r\n        const newCommClient = new TabbyAgentClient(newProcess, messageReceiver);\r\n        this.agentCommClients.set(agentName, newCommClient);\r\n      } else {\r\n        const newCommClient = new CoreAgentClient(newProcess, messageReceiver);\r\n        this.agentCommClients.set(agentName, newCommClient);\r\n      }\r\n    }\r\n  }\r\n\r\n  public shutdown(): void {\r\n    // Shutdown all comm clients\r\n    for (const client of this.agentCommClients.values()) {\r\n      client.shutdown();\r\n    }\r\n    this.agentCommClients.clear();\r\n\r\n    this.processManager.stopAll();\r\n  }\r\n\r\n  /**\r\n   * 根据agent名称获取对应的AgentCommClient\r\n   * @param agentName agent名称\r\n   * @returns 对应的AgentCommClient实例，如果不存在则返回null\r\n   */\r\n  public getAgentCommClient(agentName: string): AgentCommClient | null {\r\n    const client = this.agentCommClients.get(agentName);\r\n    if (!client) {\r\n      return null;\r\n    }\r\n    return client;\r\n  }\r\n\r\n  /**\r\n   * 注册一个状态栏处理程序以接收代理状态更新\r\n   * @param handler 状态栏处理程序\r\n   */\r\n  public registerStatusBarHandler(handler: IStatusBarHandler): void {\r\n    this.statusBarHandler = handler;\r\n  }\r\n\r\n  /**\r\n   * 检查并下载Agent\r\n   * @returns 下载成功返回true，任何一个Agent下载失败则返回false\r\n   */\r\n  public async checkAndDownloadAgents(): Promise<boolean> {\r\n    try {\r\n      // 获取配置的Agent版本信息\r\n      const agentVersions = this.versionChecker.getConfiguredAgentVersions();\r\n\r\n      if (agentVersions.length === 0) {\r\n        Logger.warn('[AgentManager] 未找到配置的Agent版本信息，跳过下载');\r\n        return false; // 没有从远端拿到下载的Agent版本，视为失败\r\n      }\r\n\r\n      // 获取配置文件路径，这里现在jetbrain和vscode的配置文件路径是一样的\r\n      const configPath = SystemUtils.getAgentConfigPath(this.pluginName);\r\n\r\n      // 获取锁文件路径\r\n      const lockFilePath = SystemUtils.getAgentLockFilePath(this.pluginName);\r\n\r\n      let allDownloadsSuccessful = true; // 标记是否所有下载都成功\r\n      \r\n      try {\r\n        // 使用文件锁执行检查和下载过程\r\n        await FileLockUtil.execute(lockFilePath, async () => {\r\n          // 读取本地配置\r\n          const localConfig = await this.versionChecker.readLocalVersions(configPath);\r\n          const osType = SystemUtils.getLocalConfig();\r\n\r\n          // 检查哪些Agent需要更新\r\n          const agentsToUpdate = [];\r\n\r\n          for (const { agentName, version, downloadUrl, md5 } of agentVersions) {\r\n            // 获取本地版本\r\n            const localVersions = localConfig.agent[agentName]?.[osType];\r\n            \r\n            // 检查远端版本是否存在于本地版本列表中\r\n            const hasVersion = localVersions && localVersions.some(v => v.version === version);\r\n            \r\n            // 判断是否需要更新：当远端版本不在本地版本列表中时需要更新\r\n            const needsUpdate = !hasVersion;\r\n            Logger.info(`agent: ${agentName}, 远端版本号: ${version}, 本地是否已有该版本: ${hasVersion}, 需要更新: ${needsUpdate}`);\r\n\r\n            if (needsUpdate && downloadUrl) {\r\n                agentsToUpdate.push({ agentName, version, downloadUrl, md5 });\r\n            }\r\n          }\r\n\r\n          if (agentsToUpdate.length === 0) {\r\n            return;\r\n          }\r\n          \r\n          // 通知状态栏正在同步开始\r\n          if (this.statusBarHandler) {\r\n            this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNCING);\r\n          }\r\n          \r\n          // 创建一个下载任务的数组\r\n          const downloadTasks = agentsToUpdate.map(async ({ agentName, version, downloadUrl, md5 }) => {\r\n            const targetPath = SystemUtils.getAgentDownloadPath(this.pluginName, agentName);\r\n\r\n            try {\r\n              const success = await this.downloader.downloadAgent(\r\n                downloadUrl,\r\n                agentName,\r\n                targetPath,\r\n                md5\r\n              );\r\n\r\n              if (success) {\r\n                return { agentName, version, downloadUrl, md5, success: true };\r\n              } else {\r\n                Logger.error(`[AgentManager] ${agentName} 下载失败`);\r\n                return { agentName, success: false };\r\n              }\r\n            } catch (error) {\r\n              Logger.error(`[AgentManager] 下载 ${agentName} 时出错: ${error}`);\r\n              return { agentName, success: false, error };\r\n            }\r\n          });\r\n\r\n          // 等待所有下载任务完成\r\n          const results = await Promise.all(downloadTasks);\r\n          \r\n          // 检查是否所有下载都成功\r\n          allDownloadsSuccessful = results.every(result => result.success);\r\n          \r\n          if (!allDownloadsSuccessful) {\r\n            Logger.warn('[AgentManager] 部分Agent下载失败，请检查日志');\r\n            \r\n            // 任何下载失败都显示错误弹窗和更新状态栏\r\n            if (this.statusBarHandler) {\r\n              this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_FAILED, '未知错误');\r\n            }\r\n            \r\n            // 显示错误信息，包含失败数量\r\n            vscode.window.showErrorMessage('服务下载失败，请重启');\r\n            return; // 提前返回，但不影响allDownloadsSuccessful的值\r\n          }\r\n          \r\n          // 收集所有成功下载的Agent\r\n          const successfulUpdates = results\r\n            .filter(result => result.success && result.version && result.downloadUrl && result.md5)\r\n            .map(({ agentName, version, downloadUrl, md5 }) => ({ \r\n              agentName, \r\n              version: version as string, \r\n              downloadUrl: downloadUrl as string, \r\n              md5: md5 as string \r\n            }));\r\n\r\n          // 统一更新所有成功下载的Agent版本记录\r\n          if (successfulUpdates.length > 0) {\r\n            await this.versionChecker.updateLocalVersions(configPath, successfulUpdates);\r\n          }\r\n        });\r\n      } finally {\r\n        // 通知状态栏同步完成\r\n        if (this.statusBarHandler && allDownloadsSuccessful) {\r\n          this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_COMPLETED);\r\n        }\r\n      }\r\n      \r\n      return allDownloadsSuccessful;\r\n    } catch (error) {\r\n      Logger.error(`[AgentManager] 检查和下载Agent时出错: ${error}`);\r\n      // 处理错误\r\n      if (this.statusBarHandler) {\r\n        // 类型安全地处理错误消息\r\n        let errorMessage = '未知错误';\r\n        if (error instanceof Error) {\r\n          errorMessage = error.message;\r\n        } else if (typeof error === 'string') {\r\n          errorMessage = error;\r\n        } else if (error && typeof error === 'object' && 'message' in error) {\r\n          errorMessage = String(error.message);\r\n        }\r\n        this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_FAILED, errorMessage);\r\n        vscode.window.showErrorMessage('服务下载失败，请重启');\r\n      }\r\n      return false; // 发生异常也返回失败\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 更新索引进度状态\r\n   * @param progress 索引进度（0-100）\r\n   */\r\n  public updateIndexingProgress(progress: number): void {\r\n    if (this.statusBarHandler) {\r\n      this.statusBarHandler.onAgentStatusChange(AgentStatus.INDEXING, progress);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理索引完成\r\n   */\r\n  public onIndexingComplete(): void {\r\n    if (this.statusBarHandler) {\r\n      this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_COMPLETED);\r\n    }\r\n  }\r\n\r\n  public isAgentRunning(agentName: string) { \r\n    if (this.agentProcessMap.has(agentName)) { \r\n      const agent = this.agentProcessMap.get(agentName);\r\n      return agent!.checkProcessHealth();\r\n    }\r\n    return false;\r\n  }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/agent/agentmanager.ts b/src/agent/agentmanager.ts
--- a/src/agent/agentmanager.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/agent/agentmanager.ts	(date 1750815239796)
@@ -139,7 +139,10 @@
     }
     this.agentCommClients.clear();
 
-    this.processManager.stopAll();
+    // Add null check to prevent TypeError when processManager is undefined
+    if (this.processManager) {
+      this.processManager.stopAll();
+    }
   }
 
   /**
Index: src/common/constants.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// 插件标识\r\nexport const APP_KEY = 'srd-copilot';\r\n\r\n// 插件名称\r\nexport const APP_NAME = process.env.ISSEC !== 'false' ? '海云安代码助手' : '研发云CodeFree';\r\n\r\n// 插件类型\r\nexport const CLIENT_TYPE = 'vscode';\r\n\r\n/**\r\n * 插件运行环境\r\n */\r\nexport enum RuntimeEnv {\r\n  // vscode客户端\r\n  VSCODE = 'vscode',\r\n  // vscode-web版\r\n  BROWSER = 'browser',\r\n  // 云IDE\r\n  CLOUD = 'cloud',\r\n}\r\n\r\n/**\r\n * 定义ViewId, 与package.json注册内容需要一致\r\n */\r\nexport enum SrdViewType {\r\n  CHAT = 'srd-copilot.chat',\r\n}\r\n\r\n/**\r\n * 定义Command指令, 与package.json注册内容需要一致\r\n */\r\nexport enum SrdCommand {\r\n  LOGIN = 'srd-copilot.login',\r\n  SHOW_LOGIN_FAILED = 'srd-copilot.showLoginFailed',\r\n  TOGGLE_CODECOMPLETE = 'srd-copilot.codeComplete.enableToggle',\r\n  COMPLETION_IMPORTS = 'srd-copilot.completionImports',\r\n  HANDLE_IMPORTS = 'srd-copilot.handleImports',\r\n  TAB_OVERRIDE = 'srd-copilot.tab-override',\r\n  UPGRADE_VERSION = 'srd-copilot.upgradeVersion',\r\n  SHOW_SVR_ERROR = 'srd-copilot.showServerError',\r\n  OPEN_HELPDOC = 'srd-copilot.helpDocument',\r\n  SHOW_CODE_NATURAL_INPUT = 'srd-copilot.codeNatural.showInput',\r\n  ACCEPT_CODE_NATURAL_ANSWER = 'srd-copilot.codeNatural.acceptAnswer',\r\n  REJECT_CODE_NATURAL_ANSWER = 'srd-copilot.codeNatural.rejectAnswer',\r\n  REGENERATE_CODE_NATURAL_ANSWER = 'srd-copilot.codeNatural.regenerateAnswer',\r\n  CANCEL_CODE_NATURAL_QUESTION = 'srd-copilot.codeNatural.cancelQuestion',\r\n  CODE_SELECTION_EXPLAIN = 'srd-copilot.codeSelection.explain',\r\n  CODE_SELECTION_UNITTEST = 'srd-copilot.codeSelection.unitTest',\r\n  CODE_SELECTION_COMMENT = 'srd-copilot.codeSelection.comment',\r\n  CODE_SELECTION_OPTIMIZATION = 'srd-copilot.codeSelection.optimization',\r\n  CODE_SECURITY_SCAN = 'srd-copilot.codeSecurity.scan',\r\n  CLOSE_SIDEBAR = 'srd-copilot.closeSidebar',\r\n  OPEN_FEEDBACK = 'srd-copilot.openFeedback',\r\n  OPEN_QUESTION = 'srd-copilot.openQuestion',\r\n  EXCEPTION_FIX = 'srd-copilot.exception.fix',\r\n  CODE_DIFF_ACCEPT_ALL_CHANGES = 'srd-copilot.codeDiff.acceptAllChanges',\r\n  CODE_DIFF_REVERT_ALL_CHANGES = 'srd-copilot.codeDiff.revertAllChanges',\r\n  VIEW_DIFF = 'srd-copilot.viewDiff',\r\n  START_CHAT = 'srd-copilot.startChat',\r\n  SETTINGS = 'srd-copilot.settings',\r\n  CHECK_UPDATE = 'srd-copilot.checkUpdate',\r\n  SECIDEA_LOGIN = 'secidea.login',\r\n  SECIDEA_LOGOUT = 'secidea.logout',\r\n  CODELENS_ACTION = 'srd-copilot.codeLensAction',\r\n}\r\n\r\n/**\r\n * 定义vscode工作台自带命令常量\r\n */\r\nexport enum VscodeWorkbench {\r\n  ACTION_OPEN_SETTINGS = 'workbench.action.openSettings',\r\n}\r\n\r\n/**\r\n * 定义setContext命令设置的key值\r\n */\r\nexport enum SrdContextKey {\r\n  TAB_OVERRIDE = 'srd-copilot.tab-override',\r\n  ACTIVATED = 'srd-copilot.activated',\r\n}\r\n\r\n/**\r\n * 定义SecretStorageKey\r\n */\r\nexport enum SecretStorageKey {\r\n  AUTH_TOKEN = 'srd-copilot.authToken',\r\n  CURRENT_USER = 'srd-copilot.currentUser',\r\n  HAS_LOGINED = 'srd-copilot.hasLogined',\r\n  CODECOMPLETE_AUTO_ENABLED = 'srd-copilot.ifCodeCompleteAutoEnabled',\r\n}\r\n\r\n/**\r\n * 定义全局状态Key\r\n */\r\nexport enum GlobalStateKey {\r\n  CODEAI_CONFIG = 'srd-copilot.codeAIConfig',\r\n  AUTH_TOKEN = 'srd-copilot.authToken',\r\n  CURRENT_USER = 'srd-copilot.currentUser',\r\n  CODECOMPLETE_AUTO_ENABLED = 'srd-copilot.ifCodeCompleteAutoEnabled',\r\n  AGENT_VERSION = 'srd-copilot.agentVersion',\r\n  CLIENT_LATEST_VERSION = 'srd-copilot.clientLatestVersion',\r\n  CLIENT_LATEST_VERSION_CONTENT = 'srd-copilot.clientLatestVersionContent',\r\n  CLIENT_LATEST_VERSION_DOWNLOAD_URL = 'srd-copilot.clientLatestVersionDownloadUrl',\r\n}\r\n\r\nexport enum AnswerMode {\r\n  SYNC = 'sync',\r\n  ASYNC = 'async',\r\n}\r\n\r\nexport enum QuestionType {\r\n  CODE_GEN_MANUAL = 'codegenmanual',\r\n  CODE_GEN = 'codegen',\r\n  CODE_CHAT = 'codechat',\r\n  NATURAL_LANG = 'codenatural',\r\n}\r\n\r\nexport enum ChannelType {\r\n  WS = 1,\r\n  HTTP = 2,\r\n}\r\n\r\nexport enum ChannelStatus {\r\n  CONNECTED = 0,\r\n  DISCONNECTED = 1,\r\n  CONNECTING = 2,\r\n}\r\n\r\nexport enum LoginStatus {\r\n  OK = 0,\r\n  NOT_OK = 1,\r\n}\r\n\r\nexport enum IsAnswerEnd {\r\n  YES = 1,\r\n  NO = 0,\r\n}\r\n\r\nexport enum CodeCompleteStatus {\r\n  START = 0,\r\n  END = 1,\r\n  ERROR = 2,\r\n}\r\n\r\nexport enum PromptRoleType {\r\n  SYSTEM = 'system',\r\n  USER = 'user',\r\n  ASSISTANT = 'assistant',\r\n}\r\n\r\nexport enum QuestionAskType {\r\n  NEW_ASK = 'newAsk',\r\n  RE_ASK = 'reask',\r\n}\r\n\r\nexport enum ChatMessageType {\r\n  EXPLAIN = 1,\r\n  UNITTEST = 2,\r\n  COMMENT = 3,\r\n  MANUAL_GENERATE = 4,\r\n  CHAT_GENERATE = 5,\r\n  OPTIMIZATION = 6,\r\n  EXCEPTION_FIX = 7,\r\n  KB_ASSISTANT = 8,\r\n  QA_RELATED_FILES = 9,\r\n}\r\n\r\nexport const ChatMessageTypeDesc: Record<string, string> = {\r\n  1: '解释代码',\r\n  2: '生成单元测试',\r\n  3: '生成代码注释',\r\n  4: '自然语言编程',\r\n  5: '编程助手问答',\r\n  6: '生成代码优化建议',\r\n  7: '异常报错解释',\r\n  8: '知识库问答',\r\n};\r\n\r\nexport const ActionTypeMap: Record<string, number> = {\r\n  \"解释代码\": ChatMessageType.EXPLAIN,\r\n  \"生成单元测试\": ChatMessageType.UNITTEST,\r\n  \"生成代码注释\": ChatMessageType.COMMENT,\r\n  \"生成代码优化建议\": ChatMessageType.OPTIMIZATION\r\n};\r\n\r\nexport enum SubServiceType {\r\n  CODECHAT = 'codechat',\r\n  ASSISTANT = 'assistant',\r\n  CODEEXPLAIN = 'codeexplain',\r\n  CODECOMMENT = 'codecomment',\r\n  CODEUNITTEST = 'codeunittest',\r\n  CODEOPTIMIZATION = 'codeoptimize',\r\n  CODEEXCEPTIONFIX = 'fixexception',\r\n  KBASSISTANT = 'kbassistant',\r\n}\r\n\r\nexport enum UploadType {\r\n  WS = 1,\r\n  HTTP = 2,\r\n}\r\n\r\nexport enum ClientCommunicateType {\r\n  WS = 1,\r\n  HTTP = 2,\r\n}\r\n\r\nexport enum EndOfLineType {\r\n  LF = '\\n',\r\n  CRLF = '\\r\\n',\r\n  CR = '\\r',\r\n}\r\n\r\nexport enum RtnCode {\r\n  SUCCESS = 0,\r\n  NO_CHANNEL = 1,\r\n  NOT_LOGIN = 2,\r\n  INVALID_USER = 3,\r\n  SEND_ERROR = 4,\r\n  RECV_TIMEOUT = 5,\r\n  USER_FORBIDDEN = 6,\r\n  INSUFFICIENT_RESOURCE = 7,\r\n  MODEL_ERROR = 8,\r\n  SERVER_DOWN = 9,\r\n  CANCEL = 10,\r\n  INVALID_SESSION_ID = 11,\r\n  LOGOUT = 12,\r\n  INVALID_QUESTION = 13,\r\n  INVALID_ANSWER = 14,\r\n  KNOWLEDGE_BASE_DELETED = 15,\r\n\r\n  // vscode客户端自定义返回码\r\n  OAUTH2_ERROR = 16,\r\n  CONNECTED_ERROR = 17,\r\n  UPLOAD_FAIL = 18,\r\n  HTTP_CLIENT_ERROR = 19,\r\n  HTTP_REQUEST_ERROR = 20,\r\n  INSERT_ERROR = 21,\r\n  STOP_ANSWER = 22,\r\n  NO_CHANNEL_CHAT = 23,\r\n  INVALID_FILE = 24,\r\n}\r\n\r\nexport const RtnMessage: Record<string, string> = {\r\n  0: '操作成功', //RtnCode_Success\r\n  1: '服务端不可达', //RtnCode_No_Channel\r\n  2: '未登录', //RtnCode_Not_Login\r\n  3: '服务未开通', //RtnCode_InvalidUser\r\n  4: '发送消息失败', //RtnCode_SendError\r\n  5: '接收消息超时', //RtnCode_RecvTimeout\r\n  6: '服务被禁用', //RtnCode_UserForbidden\r\n  7: '资源不足', //RtnCode_INSUFFICENT_RESOURCE\r\n  8: '请求处理错误', //RtnCode_MODEL_ERROR\r\n  9: '服务异常', //RtnCode_SERVER_DOWN,\r\n  10: '用户取消请求', //RtnCode_CANCLE\r\n  11: '账号未授权或已过期', //RtnCode_Invalid_SessionId\r\n  12: '用户登出', //RtnCode_Logout\r\n  13: '无效提问', //RtnCode_Invalid_Question\r\n  14: '无效回答，请重新提问', //RtnCode_Invalid_Answer\r\n  15: '找不到知识库，请切换后重新提问',\r\n  51: '大模型请求超时，请点击上方反馈按钮反馈', //RtnCode_reqTimeout\r\n\r\n  // vscode客户端自定义errMsg\r\n  16: 'Oauth2认证失败，请检查网络设置',\r\n  17: 'Websocket连接异常，请检查网络设置',\r\n  21: '异常回答，该分支不支持继续提问，请切换分支提问', //RtnCode_Insert_Error\r\n  23: '请检查网络',\r\n};\r\n\r\nexport enum ChatTips {\r\n  NOT_LOGIN = '未登录账号，请先登录。',\r\n  NORMAL_ERROR = '网络信息异常, 请重试。',\r\n  QUESTION_CANCEL = '已停止提问，请重试。',\r\n  ANSWER_STOP = '已停止回答',\r\n  FIRST_PROMPT = '我的名字是研发云编程助手CodeFree，我使用中文进行交流，作为一个高度智能化的自然语言编程助手,我是由研发云团队使用最先进的技术和大量数据训练而成。\\n' +\r\n    '我的核心目标是以友好、简单、清晰的方式帮助用户解决编程问题。我拥有深厚的编程知识,涵盖各种流行的编程语言和框架,如Python、Java、JavaScript、C++等。我也掌握广泛的计算机科学知识,如数据结构、算法、操作系统、网络等。\\n' +\r\n    '对于用户提出的任何编程相关的问题,我都能给出最佳的解决方案。我会解析问题的本质,运用丰富的知识库推导出正确的代码实现。如果需要,我还会给出多种可选方案的对比分析。\\n' +\r\n    '最后,我会恪守对用户隐私的尊重,所有对话内容仅用于提升我自身的能力,不会泄露或记录任何用户个人信息。请尽管提出你的编程问题,我会提供最专业和有价值的帮助。\\n' +\r\n    '我会用中文来回答你的问题。',\r\n  CONVERSATION_TITLE = '新的会话',\r\n}\r\n\r\nexport enum WebViewReqCommand {\r\n  WEBVIEW_LOADED = 'webview-loaded',\r\n  CONVERSATION_LOAD = 'conversation-load',\r\n  CONVERSATION_ADD = 'conversation-add',\r\n  CONVERSATION_SWITCH = 'conversation-switch',\r\n  CONVERSATION_REMOVE = 'conversation-remove',\r\n  CONVERSATION_FEEDBACK = 'conversation-feedback',\r\n  CONVERSATION_EDIT_TITLE = 'conversation-edit-title',\r\n  CONVERSATION_REFRESH = 'conversation-refresh',\r\n  CHAT_REQUEST = 'chat-request',\r\n  LOGIN = 'login',\r\n  INSERT_CODE = 'insert-code',\r\n  INSERT_UNITTEST = 'insert-unittest',\r\n  RETRIVE_CODE_SELECTION = 'retrive-code-selection',\r\n  CANCEL_CHAT_REQUEST = 'cancel-chat-request',\r\n  STOP_CHAT_REQUEST = 'stop-chat-request',\r\n  SRD_CHAT_REQUEST = 'srd-chat-request',\r\n  DATA_REPORT = 'data-report',\r\n  CHECK_IF_LOGIN = 'check-if-login',\r\n  OPEN_EXTERNAL = 'open-external',\r\n  PROMPTS_REQUEST = 'prompts-request',\r\n  KNOWLEDGE_BASE_REQUEST = 'knowledge-base-request',\r\n  CODE_SECURITY_SCAN_REQUEST = 'code-security-scan-request',\r\n  COMPOSER_REQUEST = 'composer-request',\r\n  DIFF_VIEW_VERTICAL_REQUEST = 'diff-view-vertical-request',\r\n  INDEXING_REQUEST = 'indexing-request',\r\n  GET_IDE_UTILS_REQUEST = 'get-ide-utils-request',\r\n  QA_FOR_RELATED_FILES_REQUEST = 'qa-for-related-files-request',\r\n  VIEW_DIFF = 'view-diff',\r\n  OPEN_TEXT_DOCUMENT = 'open-text-document',\r\n  INVOKE_TERMINAL_CAPABILITY = 'invoke-terminal-capability',\r\n  WORK_ITEM_REQUEST = 'workitem-request',\r\n}\r\n\r\nexport enum WebViewRspCommand {\r\n  ANSWER_RECVED = 'answer-recved',\r\n  CONVERSATION_LOADED = 'conversation-loaded',\r\n  CONVERSATION_CHANGED = 'conversation-changed',\r\n  CONVERSATION_REFRESHED = 'conversation-refreshed',\r\n  CONVERSATION_REMOVED = 'conversation-removed',\r\n  CONVERSATION_ADDED = 'conversation-added',\r\n  CODE_SELECTION_ASKED = 'code-selection-asked',\r\n  RETURN_CODE_SELECTION = 'return-code-selection',\r\n  CODE_SELECTION_CHANGED = 'code-selection-changed',\r\n  SRD_CHAT_RESPONSE = 'srd-chat-response',\r\n  CHECK_IF_LOGIN_RESPONSE = 'check-if-login-response',\r\n  PROMPTS_RESPONSE = 'prompts-response',\r\n  SWITCH_CONVERSATION_RESPONSE = 'switch-conversation-response',\r\n  FEEDBACK_CONVERSATION_RESPONSE = 'feedback-conversation-response',\r\n  PUSH_LOGIN_STATUS_RESPONSE = 'push-login-status-response',\r\n  KNOWLEDGE_BASE_RESPONSE = 'knowledge-base-response',\r\n  CODE_SECURITY_SCAN_RESPONSE = 'code-security-scan-response',\r\n  CODE_SECURITY_SCAN_START = 'code-security-scan-start',\r\n  PUSH_THEME_CHANGED = 'push-theme-changed',\r\n  PUSH_NETWORK_STATUS_RESPONSE = 'push-network-status-response',\r\n  COMPOSER_RESPONSE = 'composer-response',\r\n  DIFF_VIEW_VERTICAL_RESPONSE = 'diff-view-vertical-response',\r\n  INDEXING_RESPONSE = 'indexing-response',\r\n  GET_IDE_UTILS_RESPONSE = 'get-ide-utils-response',\r\n  QA_FOR_RELATED_FILES_RESPONSE = 'qa-for-related-files-response',\r\n  OPEN_TEXT_DOCUMENT = 'open-text-document',\r\n  FILE_EXCEED_LIMIT = 'file-exceed-limit',\r\n  DIFF_STATUS_CHANGED = 'diff-status-changed',\r\n  WORK_ITEM_RESPONSE = 'workitem-response',\r\n}\r\n\r\nexport enum WebViewRspCode {\r\n  SUCCESS = 0,\r\n  NOT_LOGIN = 2,\r\n  STOP_ANSWER = 3,\r\n  NEED_CLEAR = 4,\r\n  WSSERVER_RECONNECT_SUCCESS = 5,\r\n  INSERT_ERROR = 21,\r\n}\r\n\r\nexport enum MessageName {\r\n  CLIENT_HEART = 'ClientHeartbeat',\r\n  CLIENT_HEART_RESP = 'ClientHeartbeatResponse',\r\n  SERVER_HEART = 'ServerHeartbeat',\r\n  SERVER_HEART_RESP = 'ServerHeartbeatResponse',\r\n  GET_USER_API_KEY = 'GetUserApiKey',\r\n  GET_USER_API_KEY_RESP = 'GetUserApiKey_resp',\r\n  REGISTER_CHANNEL = 'RegisterChannel',\r\n  REGISTER_CHANNEL_RESP = 'RegisterChannel_resp',\r\n  CODE_GEN = 'CodeGenRequest',\r\n  CODE_GEN_RESP = 'CodeGenRequest_resp',\r\n  CODE_CHAT = 'CodeChatRequest',\r\n  CODE_CHAT_RESP = 'CodeChatRequest_resp',\r\n  CANCEL_CODE_GEN = 'CancelCodeGenReq',\r\n  CANCEL_CODE_CHAT = 'CancelCodeChatReq',\r\n  USER_ACTIVITY_NOTIFY = 'UserActivityNotify',\r\n  USER_ACTIVITY_NOTIFY_RESP = 'UserActivityNotify_resp',\r\n  COMMIT_CHAT_REQUEST = 'CommitChatRequest',\r\n  COMMIT_CHAT_RESP = 'CommitChatRequest_resp',\r\n}\r\n\r\n/**\r\n * 定义HttpStatus码\r\n */\r\nexport enum HttpStatusCode {\r\n  OK = 200,\r\n  CREATED = 201,\r\n  ACCEPTED = 202,\r\n  NO_CONTENT = 204,\r\n  BAD_REQUEST = 400,\r\n  UNAUTHORIZED = 401,\r\n  FORBIDDEN = 403,\r\n  NOT_FOUND = 404,\r\n  METHOD_NOT_ALLOWED = 405,\r\n  NOT_ACCEPTABLE = 406,\r\n  PROXY_AUTHENTICATION_REQUIRED = 407,\r\n  REQUEST_TIMEOUT = 408,\r\n  CONFLICT = 409,\r\n  GONE = 410,\r\n  LENGTH_REQUIRED = 411,\r\n  PRECONDITION_FAILED = 412,\r\n  REQUEST_ENTITY_TOO_LARGE = 413,\r\n  REQUEST_URI_TOO_LONG = 414,\r\n  UNSUPPORTED_MEDIA_TYPE = 415,\r\n  REQUESTED_RANGE_NOT_SATISFIABLE = 416,\r\n  EXPECTATION_FAILED = 417,\r\n  INTERNAL_SERVER_ERROR = 500,\r\n  NOT_IMPLEMENTED = 501,\r\n  BAD_GATEWAY = 502,\r\n  SERVICE_UNAVAILABLE = 503,\r\n  GATEWAY_TIMEOUT = 504,\r\n  HTTP_VERSION_NOT_SUPPORTED = 505,\r\n  INSUFFICIENT_STORAGE = 507,\r\n  NOT_EXTENDED = 510,\r\n  NETWORK_AUTHENTICATION_REQUIRED = 511,\r\n  UNKNOWN = 999,\r\n  AUTH_FAIL = 101,\r\n}\r\n\r\nexport enum AgentStatus {\r\n  SYNCING = 0,\r\n  SYNC_COMPLETED = 1,\r\n  SYNC_FAILED = 2,\r\n  STARTUP_FAILED = 3,\r\n  INDEXING = 4,\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/common/constants.ts b/src/common/constants.ts
--- a/src/common/constants.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/common/constants.ts	(date 1750820858670)
@@ -62,6 +62,7 @@
   SECIDEA_LOGIN = 'secidea.login',
   SECIDEA_LOGOUT = 'secidea.logout',
   CODELENS_ACTION = 'srd-copilot.codeLensAction',
+  SHOW_STATUS_MENU = 'srd-copilot.showStatusMenu',
 }
 
 /**
Index: src/statusbar/srdStatusBarItem.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { Command, Disposable, StatusBarAlignment, StatusBarItem, ThemeColor, window } from 'vscode';\r\nimport { APP_NAME, RtnCode, RtnMessage, SrdCommand } from '../common/constants';\r\nimport { StatusBarState } from './types';\r\n\r\nexport default class SrdStatusBarItem implements Disposable {\r\n  private item: StatusBarItem;\r\n\r\n  private showAppName = false;\r\n\r\n  public constructor(showAppName?: boolean) {\r\n    if (showAppName !== undefined) {\r\n      this.showAppName = showAppName;\r\n    }\r\n\r\n    this.item = window.createStatusBarItem(StatusBarAlignment.Left, -1);\r\n    this.setDefault();\r\n    this.item.show();\r\n  }\r\n\r\n  public dispose() {\r\n    this.item.dispose();\r\n    if (this.settingsItem) {\r\n      this.settingsItem.dispose();\r\n      this.settingsItem = undefined;\r\n    }\r\n  }\r\n\r\n  public setStatusView(state: StatusBarState, code?: number) {\r\n    switch (state) {\r\n      case StatusBarState.NotLogin:\r\n        this.setDefault('未登录', '点击进行登录', 'srd-copilot-unlogin');\r\n        this.setCommand(SrdCommand.LOGIN);\r\n        break;\r\n      case StatusBarState.Logining:\r\n        this.setLoading('登录中...', '正在登录');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.CodeCompleteEnabled:\r\n        this.setDefault('自动补全启用', '点击可禁用', 'srd-copilot-code-enabled');\r\n        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);\r\n        break;\r\n      case StatusBarState.CodeCompleteDisabled:\r\n        this.setWarning('自动补全禁用', '点击可启用', 'srd-copilot-code-disabled');\r\n        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);\r\n        break;\r\n      case StatusBarState.WaitingAutoComplete:\r\n        this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.WaitingManualComplete:\r\n        this.setLoading('获取手动补全代码中...', '正在获取手动补全代码');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.WSServerError: {\r\n        code = code || RtnCode.NO_CHANNEL;\r\n        const msg = RtnMessage[code];\r\n        this.setError(\r\n          msg,\r\n          msg,\r\n          code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'\r\n        );\r\n        this.setCommand(\r\n          code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN\r\n            ? {\r\n                command: SrdCommand.SHOW_SVR_ERROR,\r\n                arguments: [code],\r\n                title: '',\r\n              }\r\n            : undefined\r\n        );\r\n        break;\r\n      }\r\n      case StatusBarState.SyncingAgent:\r\n        this.setLoading('同步服务中...', '正在下载和安装智能编程服务');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.AgentSyncFailed:\r\n        this.setError('同步服务失败', '下载智能服务失败', 'error');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.AgentStartupFailed:\r\n        this.setError('服务启动失败', '启动智能服务失败', 'error');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.CodeIndexing:\r\n        const progress = code !== undefined ? Math.round(code) : 0;\r\n        this.setLoading(`正在更新索引 (${progress}%)`, '正在更新代码索引，这可能需要一些时间');\r\n        this.setCommand(undefined);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n  public setStatusViewSec(state: StatusBarState, code?: number) {\r\n    // 保存当前状态以便设置命令时使用\r\n    this.currentState = state;\r\n    this.currentCode = code;\r\n    \r\n    switch (state) {\r\n      case StatusBarState.NotLogin:\r\n        this.setDefault('未登录', '点击进行登录', 'srd-copilot-unlogin');\r\n        this.setCommand(SrdCommand.LOGIN);\r\n        break;\r\n      case StatusBarState.Logining:\r\n        this.setLoading('登录中...', '正在登录');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.CodeCompleteEnabled:\r\n        this.setDefault('自动补全启用', '点击可禁用', 'srd-copilot-code-enabled');\r\n        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);\r\n        break;\r\n      case StatusBarState.CodeCompleteDisabled:\r\n        this.setWarning('自动补全禁用', '点击可启用', 'srd-copilot-code-disabled');\r\n        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);\r\n        break;\r\n      case StatusBarState.WaitingAutoComplete:\r\n        this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.WaitingManualComplete:\r\n        this.setLoading('获取手动补全代码中...', '正在获取手动补全代码');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.WSServerError: {\r\n        code = code || RtnCode.NO_CHANNEL;\r\n        const msg = RtnMessage[code];\r\n        this.setError(\r\n          msg,\r\n          msg,\r\n          code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'\r\n        );\r\n        this.setCommand(\r\n          code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN\r\n            ? {\r\n              command: SrdCommand.SHOW_SVR_ERROR,\r\n              arguments: [code],\r\n              title: '',\r\n            }\r\n            : undefined\r\n        );\r\n        break;\r\n      }\r\n      case StatusBarState.SyncingAgent:\r\n        this.setLoading('同步服务中...', '正在下载和安装智能编程服务');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.AgentSyncFailed:\r\n        this.setError('同步服务失败', '下载智能服务失败', 'error');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.AgentStartupFailed:\r\n        this.setError('服务启动失败', '启动智能服务失败', 'error');\r\n        this.setCommand(undefined);\r\n        break;\r\n      case StatusBarState.CodeIndexing:\r\n        const progress = code !== undefined ? Math.round(code) : 0;\r\n        this.setLoading(`正在更新索引 (${progress}%)`, '正在更新代码索引，这可能需要一些时间');\r\n        this.setCommand(undefined);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    \r\n    // 添加设置按钮\r\n    this.addSettingsButton();\r\n  }\r\n\r\n  // 存储当前状态\r\n  private currentState: StatusBarState | undefined;\r\n  private currentCode: number | undefined;\r\n\r\n  // 添加设置按钮\r\n  private addSettingsButton() {\r\n    // 创建设置按钮的状态栏项\r\n    if (!this.settingsItem) {\r\n      this.settingsItem = window.createStatusBarItem(StatusBarAlignment.Left, -0.5);\r\n      this.settingsItem.text = '$(gear)';\r\n      this.settingsItem.tooltip = '打开海云安代码助手设置';\r\n      this.settingsItem.command = SrdCommand.SETTINGS;\r\n      this.settingsItem.show();\r\n    }\r\n  }\r\n\r\n  // 设置按钮状态栏项\r\n  private settingsItem: StatusBarItem | undefined;\r\n\r\n  private setDefault(statusText?: string, message?: string, icon?: string) {\r\n    this.item.backgroundColor = undefined;\r\n\r\n    if (statusText) {\r\n      this.setText(statusText, icon);\r\n    } else {\r\n      this.item.text = '';\r\n    }\r\n\r\n    if (message) {\r\n      this.item.tooltip = message;\r\n    }\r\n  }\r\n\r\n  private setLoading(statusText: string, message: string) {\r\n    this.setText(statusText, 'loading~spin');\r\n    this.item.backgroundColor = undefined;\r\n    this.item.tooltip = message;\r\n  }\r\n\r\n  private setError(statusText: string, message: string, icon?: string) {\r\n    this.setText(statusText, icon);\r\n    this.item.backgroundColor = new ThemeColor('statusBarItem.errorBackground');\r\n    this.item.tooltip = message;\r\n  }\r\n\r\n  private setWarning(statusText: string, message: string, icon?: string) {\r\n    this.setText(statusText, icon);\r\n    this.item.backgroundColor = new ThemeColor('statusBarItem.warningBackground');\r\n    this.item.tooltip = message;\r\n  }\r\n\r\n  private setCommand(command: string | Command | undefined) {\r\n    this.item.command = command;\r\n  }\r\n\r\n  private setText(text: string, icon?: string) {\r\n    const baseText = icon ? `$(${icon}) ${text}` : text;\r\n\r\n    if (this.showAppName) {\r\n      this.item.text = `${APP_NAME} (${baseText})`;\r\n    } else {\r\n      this.item.text = baseText;\r\n    }\r\n  }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/statusbar/srdStatusBarItem.ts b/src/statusbar/srdStatusBarItem.ts
--- a/src/statusbar/srdStatusBarItem.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/statusbar/srdStatusBarItem.ts	(date 1750820976091)
@@ -19,99 +19,37 @@
 
   public dispose() {
     this.item.dispose();
-    if (this.settingsItem) {
-      this.settingsItem.dispose();
-      this.settingsItem = undefined;
-    }
   }
 
   public setStatusView(state: StatusBarState, code?: number) {
     switch (state) {
       case StatusBarState.NotLogin:
-        this.setDefault('未登录', '点击进行登录', 'srd-copilot-unlogin');
-        this.setCommand(SrdCommand.LOGIN);
-        break;
-      case StatusBarState.Logining:
-        this.setLoading('登录中...', '正在登录');
-        this.setCommand(undefined);
-        break;
-      case StatusBarState.CodeCompleteEnabled:
-        this.setDefault('自动补全启用', '点击可禁用', 'srd-copilot-code-enabled');
-        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
-        break;
-      case StatusBarState.CodeCompleteDisabled:
-        this.setWarning('自动补全禁用', '点击可启用', 'srd-copilot-code-disabled');
-        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
-        break;
-      case StatusBarState.WaitingAutoComplete:
-        this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');
-        this.setCommand(undefined);
-        break;
-      case StatusBarState.WaitingManualComplete:
-        this.setLoading('获取手动补全代码中...', '正在获取手动补全代码');
-        this.setCommand(undefined);
-        break;
-      case StatusBarState.WSServerError: {
-        code = code || RtnCode.NO_CHANNEL;
-        const msg = RtnMessage[code];
-        this.setError(
-          msg,
-          msg,
-          code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'
-        );
-        this.setCommand(
-          code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN
-            ? {
-                command: SrdCommand.SHOW_SVR_ERROR,
-                arguments: [code],
-                title: '',
-              }
-            : undefined
-        );
-        break;
-      }
-      case StatusBarState.SyncingAgent:
-        this.setLoading('同步服务中...', '正在下载和安装智能编程服务');
-        this.setCommand(undefined);
-        break;
-      case StatusBarState.AgentSyncFailed:
-        this.setError('同步服务失败', '下载智能服务失败', 'error');
-        this.setCommand(undefined);
-        break;
-      case StatusBarState.AgentStartupFailed:
-        this.setError('服务启动失败', '启动智能服务失败', 'error');
-        this.setCommand(undefined);
-        break;
-      case StatusBarState.CodeIndexing:
-        const progress = code !== undefined ? Math.round(code) : 0;
-        this.setLoading(`正在更新索引 (${progress}%)`, '正在更新代码索引，这可能需要一些时间');
-        this.setCommand(undefined);
-        break;
-      default:
-        break;
-    }
-  }
-  public setStatusViewSec(state: StatusBarState, code?: number) {
-    // 保存当前状态以便设置命令时使用
-    this.currentState = state;
-    this.currentCode = code;
-    
-    switch (state) {
-      case StatusBarState.NotLogin:
-        this.setDefault('未登录', '点击进行登录', 'srd-copilot-unlogin');
-        this.setCommand(SrdCommand.LOGIN);
+        this.setDefault('未登录', '点击查看选项', 'srd-copilot-unlogin');
+        this.setCommand({
+          command: SrdCommand.SHOW_STATUS_MENU,
+          arguments: ['NotLogin', code],
+          title: '',
+        });
         break;
       case StatusBarState.Logining:
         this.setLoading('登录中...', '正在登录');
         this.setCommand(undefined);
         break;
       case StatusBarState.CodeCompleteEnabled:
-        this.setDefault('自动补全启用', '点击可禁用', 'srd-copilot-code-enabled');
-        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
+        this.setDefault('自动补全启用', '点击查看选项', 'srd-copilot-code-enabled');
+        this.setCommand({
+          command: SrdCommand.SHOW_STATUS_MENU,
+          arguments: ['CodeCompleteEnabled', code],
+          title: '',
+        });
         break;
       case StatusBarState.CodeCompleteDisabled:
-        this.setWarning('自动补全禁用', '点击可启用', 'srd-copilot-code-disabled');
-        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
+        this.setWarning('自动补全禁用', '点击查看选项', 'srd-copilot-code-disabled');
+        this.setCommand({
+          command: SrdCommand.SHOW_STATUS_MENU,
+          arguments: ['CodeCompleteDisabled', code],
+          title: '',
+        });
         break;
       case StatusBarState.WaitingAutoComplete:
         this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');
@@ -126,17 +64,21 @@
         const msg = RtnMessage[code];
         this.setError(
           msg,
-          msg,
+          '点击查看选项',
           code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'
         );
         this.setCommand(
           code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN
             ? {
-              command: SrdCommand.SHOW_SVR_ERROR,
-              arguments: [code],
-              title: '',
-            }
-            : undefined
+                command: SrdCommand.SHOW_SVR_ERROR,
+                arguments: [code],
+                title: '',
+              }
+            : {
+                command: SrdCommand.SHOW_STATUS_MENU,
+                arguments: ['WSServerError', code],
+                title: '',
+              }
         );
         break;
       }
@@ -145,12 +87,20 @@
         this.setCommand(undefined);
         break;
       case StatusBarState.AgentSyncFailed:
-        this.setError('同步服务失败', '下载智能服务失败', 'error');
-        this.setCommand(undefined);
+        this.setError('同步服务失败', '点击查看选项', 'error');
+        this.setCommand({
+          command: SrdCommand.SHOW_STATUS_MENU,
+          arguments: ['AgentSyncFailed', code],
+          title: '',
+        });
         break;
       case StatusBarState.AgentStartupFailed:
-        this.setError('服务启动失败', '启动智能服务失败', 'error');
-        this.setCommand(undefined);
+        this.setError('服务启动失败', '点击查看选项', 'error');
+        this.setCommand({
+          command: SrdCommand.SHOW_STATUS_MENU,
+          arguments: ['AgentStartupFailed', code],
+          title: '',
+        });
         break;
       case StatusBarState.CodeIndexing:
         const progress = code !== undefined ? Math.round(code) : 0;
@@ -160,29 +110,8 @@
       default:
         break;
     }
-    
-    // 添加设置按钮
-    this.addSettingsButton();
   }
 
-  // 存储当前状态
-  private currentState: StatusBarState | undefined;
-  private currentCode: number | undefined;
-
-  // 添加设置按钮
-  private addSettingsButton() {
-    // 创建设置按钮的状态栏项
-    if (!this.settingsItem) {
-      this.settingsItem = window.createStatusBarItem(StatusBarAlignment.Left, -0.5);
-      this.settingsItem.text = '$(gear)';
-      this.settingsItem.tooltip = '打开海云安代码助手设置';
-      this.settingsItem.command = SrdCommand.SETTINGS;
-      this.settingsItem.show();
-    }
-  }
-
-  // 设置按钮状态栏项
-  private settingsItem: StatusBarItem | undefined;
 
   private setDefault(statusText?: string, message?: string, icon?: string) {
     this.item.backgroundColor = undefined;
Index: src/settings/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import * as vscode from 'vscode';\r\nimport { SrdCommand, VscodeWorkbench } from '../common/constants';\r\nimport { SECIDEA_SETTINGS_ITEM } from '../common/config';\r\nimport { LanguageConfig } from './type';\r\nimport {\r\n  clearClientPropertiesEndpoint,\r\n  handleDisableLanguageChange,\r\n  updateClientPropertiesCplMode,\r\n  updateClientPropertiesEndpoint,\r\n  updateClientPropertiesKeybinding,\r\n  updateClientPropertiesTriggerMode,\r\n} from './utils';\r\nimport { GENERAL_ADDRESS_SETTING } from './constants';\r\nimport SettingsWatcher from './settingsWatcher';\r\nimport { getAddress } from './settingsGetter';\r\nimport {\r\n  testConnect,\r\n  userInfoManager,\r\n  LoginStatus as SecideaLoginStatus,\r\n} from '../login/loginUtils';\r\nimport { srdAuthProviderInstance, tryLoginBySrdcloud } from '../authentication/index';\r\n\r\nexport async function registerSettings(context: vscode.ExtensionContext) {\r\n  // 仅当品牌为Secidea时，才有设置页面\r\n  if (process.env.ISSEC !== 'false') {\r\n    // 不做证书校验\r\n    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\r\n\r\n    // 注册配置页面的打开\r\n    context.subscriptions.push(\r\n      vscode.commands.registerCommand(SrdCommand.SETTINGS, () => {\r\n        vscode.commands.executeCommand(VscodeWorkbench.ACTION_OPEN_SETTINGS, SECIDEA_SETTINGS_ITEM);\r\n      })\r\n    );\r\n\r\n    // 注册登录命令\r\n    context.subscriptions.push(\r\n      vscode.commands.registerCommand(SrdCommand.SECIDEA_LOGIN, () => {\r\n        if (userInfoManager.getLoginStatus() === SecideaLoginStatus.notLoggedIn) {\r\n          tryLoginBySrdcloud(false);\r\n        }\r\n      }),\r\n      vscode.commands.registerCommand(SrdCommand.SECIDEA_LOGOUT, () => {\r\n        srdAuthProviderInstance?.removeSession();\r\n      })\r\n    );\r\n\r\n    // 配置项监听\r\n    const settingsWatcher = new SettingsWatcher(context);\r\n    settingsWatcher.watch();\r\n\r\n    // 测试服务器连接情况， 如果不通则跳转到设置页面\r\n    if (!(await testConnect())) {\r\n      if (getAddress().trim() === '') {\r\n        // 初次设置\r\n        const selection = await vscode.window.showInformationMessage(\r\n          '检测到未设置服务器地址，是否立即进行设置',\r\n          '是',\r\n          '否'\r\n        );\r\n        if (selection === '是') {\r\n          vscode.commands.executeCommand(\r\n            VscodeWorkbench.ACTION_OPEN_SETTINGS,\r\n            SECIDEA_SETTINGS_ITEM\r\n          );\r\n        }\r\n      } else {\r\n        // 有服务器地址，但是网络不通\r\n        const selection = await vscode.window.showWarningMessage(\r\n          '当前服务器网络连接不通, 是否立即检查服务器地址',\r\n          '是',\r\n          '否'\r\n        );\r\n        if (selection === '是') {\r\n          vscode.commands.executeCommand(\r\n            VscodeWorkbench.ACTION_OPEN_SETTINGS,\r\n            SECIDEA_SETTINGS_ITEM\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 初始化设置页面的监听器\r\n  initCplConfigurationListeners();\r\n}\r\n\r\nfunction initCplConfigurationListeners(): void {\r\n  vscode.workspace.onDidChangeConfiguration(async event => {\r\n    if (event.affectsConfiguration('代码助手.禁用补全语言')) {\r\n      const disableLanguageList = vscode.workspace\r\n        .getConfiguration('代码助手')\r\n        .get<LanguageConfig[]>('禁用补全语言');\r\n      if (!disableLanguageList) {\r\n        throw new Error('禁用补全语言配置无效');\r\n      }\r\n      await handleDisableLanguageChange(disableLanguageList);\r\n    }\r\n\r\n    if (event.affectsConfiguration(GENERAL_ADDRESS_SETTING)) {\r\n      const endpoint = getAddress();\r\n      if (endpoint && endpoint.trim().length > 0) {\r\n        updateClientPropertiesEndpoint(endpoint);\r\n        // nodecli!.stdin.write(JSON.stringify(updateConfigRequest) + \"\\n\");\r\n      } else {\r\n        clearClientPropertiesEndpoint();\r\n        // nodecli!.stdin.write(JSON.stringify(clearConfigRequest) + \"\\n\");\r\n      }\r\n    }\r\n\r\n    if (event.affectsConfiguration('代码助手.补全模式')) {\r\n      const cplMode = vscode.workspace.getConfiguration('代码助手').get<string>('补全模式');\r\n      // console.debug(`Completion mode changed to: ${cplMode}`);\r\n      await updateClientPropertiesCplMode(cplMode!);\r\n    }\r\n\r\n    if (event.affectsConfiguration('代码助手.补全方式')) {\r\n      const triggerMode =\r\n        vscode.workspace.getConfiguration('代码助手').get<string>('补全方式') === '自动'\r\n          ? 'automatic'\r\n          : 'manual';\r\n      // console.debug(`Trigger mode changed to: ${triggerMode}`);\r\n      await updateClientPropertiesTriggerMode(triggerMode);\r\n    }\r\n\r\n    if (event.affectsConfiguration('代码助手.快捷键配置')) {\r\n      const keybindings =\r\n        vscode.workspace.getConfiguration('代码助手').get<string>('快捷键配置') === '默认'\r\n          ? 'vscode-style'\r\n          : '';\r\n      await updateClientPropertiesKeybinding(keybindings);\r\n      if (keybindings === '') {\r\n        vscode.commands.executeCommand('secidea.openKeybindings');\r\n      }\r\n    }\r\n  });\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/settings/index.ts b/src/settings/index.ts
--- a/src/settings/index.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/settings/index.ts	(date 1750820634615)
@@ -7,7 +7,6 @@
   handleDisableLanguageChange,
   updateClientPropertiesCplMode,
   updateClientPropertiesEndpoint,
-  updateClientPropertiesKeybinding,
   updateClientPropertiesTriggerMode,
 } from './utils';
 import { GENERAL_ADDRESS_SETTING } from './constants';
@@ -45,6 +44,13 @@
       })
     );
 
+    // 注册打开快捷键设置命令
+    context.subscriptions.push(
+      vscode.commands.registerCommand('secidea.openKeybindings', () => {
+        vscode.commands.executeCommand('workbench.action.openGlobalKeybindings', 'editor.action.inlineSuggest');
+      })
+    );
+
     // 配置项监听
     const settingsWatcher = new SettingsWatcher(context);
     settingsWatcher.watch();
@@ -128,7 +134,6 @@
         vscode.workspace.getConfiguration('代码助手').get<string>('快捷键配置') === '默认'
           ? 'vscode-style'
           : '';
-      await updateClientPropertiesKeybinding(keybindings);
       if (keybindings === '') {
         vscode.commands.executeCommand('secidea.openKeybindings');
       }
Index: src/statusbar/srdStatusBar.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { Disposable, ExtensionContext, commands } from 'vscode';\r\nimport SrdStatusBarItem from './srdStatusBarItem';\r\nimport { StatusBarState, IStatusBarHandler } from './types';\r\nimport { CodeCompleteStatus, RtnCode, SecretStorageKey, SrdCommand } from '../common/constants';\r\nimport { IServiceObserver, EventType, IServiceObservable } from '../service/types/login';\r\nimport { CodeCompleteEnginInst } from '../codecomplete/codeCompleteEngin';\r\nimport AutoCompleteStatusStore from './autoCompleteStatusStore';\r\nimport { isRunInCloudIDE } from '../adapter/common';\r\nimport { AgentManager } from '../agent/agentmanager';\r\nimport { AgentStatus } from '../common/constants';\r\n\r\nexport default class SrdStatusBar implements Disposable, IServiceObserver, IStatusBarHandler {\r\n  private item: SrdStatusBarItem;\r\n\r\n  private currentState: StatusBarState = StatusBarState.NotLogin;\r\n\r\n  private context: ExtensionContext;\r\n\r\n  private observable: IServiceObservable | undefined;\r\n\r\n  private disposable: Disposable | undefined;\r\n\r\n  public constructor(context: ExtensionContext) {\r\n    this.context = context;\r\n    this.item = new SrdStatusBarItem();\r\n    CodeCompleteEnginInst.setStatusBarHandler(this);\r\n\r\n    if (!isRunInCloudIDE()) {\r\n      this.disposable = Disposable.from(this.onSecretChange());\r\n    }\r\n\r\n    const agentManager = AgentManager.getInstance();\r\n    agentManager.registerStatusBarHandler(this);\r\n  }\r\n\r\n  public setObservable(observable: IServiceObservable): void {\r\n    this.observable = observable;\r\n    this.observable.registerObserver(this);\r\n  }\r\n\r\n  public dispose() {\r\n    this.item.dispose();\r\n    this.disposable?.dispose();\r\n    this.observable?.unregisterObserver(this);\r\n    CodeCompleteEnginInst.setStatusBarHandler(null);\r\n  }\r\n\r\n  /**\r\n   * 初始化状态栏\r\n   */\r\n  public async init() {\r\n    this.setStatusBarView();\r\n  }\r\n\r\n  /**\r\n   * 修改启用或禁用状态\r\n   * @param enabled\r\n   */\r\n  public async setIfEnabled(enabled: boolean) {\r\n    if (enabled && this.currentState !== StatusBarState.CodeCompleteEnabled) {\r\n      this.switchState(StatusBarState.CodeCompleteEnabled);\r\n      this.setStatusBarView();\r\n    } else if (!enabled && this.currentState !== StatusBarState.CodeCompleteDisabled) {\r\n      this.switchState(StatusBarState.CodeCompleteDisabled);\r\n      this.setStatusBarView();\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 监听登录状态变化\r\n   * @param eventType\r\n   * @param data\r\n   */\r\n  public async onServiceChanged(eventType: EventType, data: unknown) {\r\n    switch (eventType) {\r\n      case EventType.LOGIN:\r\n        this.switchState(StatusBarState.Logining);\r\n        this.setStatusBarView();\r\n        break;\r\n      case EventType.LOGIN_SUCCESS:\r\n        this.switchEnableOrDisableCodeComplete();\r\n        break;\r\n      case EventType.LOGIN_EXPIRED:\r\n      case EventType.LOGIN_CANCELED:\r\n      case EventType.LOGIN_FAILED:\r\n      case EventType.LOGOUT:\r\n        this.switchState(StatusBarState.NotLogin);\r\n        this.setStatusBarView();\r\n        break;\r\n      case EventType.WSSERVER_ERROR: {\r\n        const code = data as number;\r\n        this.switchState(StatusBarState.WSServerError);\r\n        this.setStatusBarView(code);\r\n        break;\r\n      }\r\n      case EventType.WSSERVER_RECONNECT: { \r\n        const code = data as number;\r\n        this.onCodeChatStatusChange(code);\r\n        break;\r\n      }\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 监听代码补全状态变化\r\n   * @param status\r\n   * @param data\r\n   */\r\n  public onCodeCompleteStatusChange(status: number, data?: unknown): void {\r\n    switch (status) {\r\n      case CodeCompleteStatus.START: {\r\n        const isAuto = data as boolean;\r\n        if (isAuto) {\r\n          this.switchState(StatusBarState.WaitingAutoComplete);\r\n        } else {\r\n          this.switchState(StatusBarState.WaitingManualComplete);\r\n        }\r\n        this.setStatusBarView();\r\n        break;\r\n      }\r\n      case CodeCompleteStatus.END:\r\n        this.switchEnableOrDisableCodeComplete();\r\n        break;\r\n      case CodeCompleteStatus.ERROR:\r\n        this.switchErrorStatus(data as number);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**p\r\n   * 监听CodeChat异常状态变化\r\n   * @param status\r\n   * @param data\r\n   */\r\n  public onCodeChatStatusChange(code: number): void {\r\n    if (code === RtnCode.SUCCESS) {\r\n      this.switchEnableOrDisableCodeComplete();\r\n    } else {\r\n      this.switchErrorStatus(code);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 临听Secret变化，同步多窗口状态栏\"自动补全\"状态变化\r\n   */\r\n  private onSecretChange() {\r\n    return this.context.secrets.onDidChange(async e => {\r\n      if (e.key === SecretStorageKey.CODECOMPLETE_AUTO_ENABLED) {\r\n        const enabled = await AutoCompleteStatusStore.checkIfEnabled();\r\n\r\n        if (this.currentState === StatusBarState.CodeCompleteEnabled && !enabled) {\r\n          this.setIfEnabled(false);\r\n        } else if (this.currentState === StatusBarState.CodeCompleteDisabled && enabled) {\r\n          this.setIfEnabled(true);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 切换状态\r\n   * @param state\r\n   */\r\n  private switchState(state: StatusBarState) {\r\n    this.currentState = state;\r\n  }\r\n\r\n  /**\r\n   * 切换自动补全状态\r\n   */\r\n  private async switchEnableOrDisableCodeComplete() {\r\n    const enabled = await AutoCompleteStatusStore.checkIfEnabled();\r\n    this.setIfEnabled(enabled);\r\n  }\r\n\r\n  /**\r\n   * 切换服务异常状态\r\n   * @param code\r\n   */\r\n  private switchErrorStatus(code: number) {\r\n    switch (code) {\r\n      case RtnCode.NOT_LOGIN:\r\n        this.switchState(StatusBarState.NotLogin);\r\n        this.setStatusBarView();\r\n        this.observable?.handleEvent(EventType.QUESTION_NOT_LOGIN, code);\r\n        break;\r\n      case RtnCode.INVALID_USER:\r\n      case RtnCode.USER_FORBIDDEN:\r\n        this.switchState(StatusBarState.WSServerError);\r\n        this.setStatusBarView(code);\r\n        commands.executeCommand(SrdCommand.SHOW_SVR_ERROR, code);\r\n        break;\r\n      case RtnCode.INVALID_SESSION_ID:\r\n      case RtnCode.LOGOUT:\r\n      case RtnCode.CANCEL:\r\n        // do nothing\r\n        break;\r\n      default:\r\n        this.switchState(StatusBarState.WSServerError);\r\n        this.setStatusBarView(code);\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置状态栏视图\r\n   */\r\n  private setStatusBarView(code?: number) {\r\n    if (process.env.ISSEC === 'false') {\r\n      this.item.setStatusView(this.currentState, code);\r\n    } else {\r\n      this.item.setStatusViewSec(this.currentState, code); //if sec, display sec setting button\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理Agent状态变化\r\n   * @param status Agent状态\r\n   * @param data 额外数据，可能是错误信息或进度\r\n   */\r\n  public onAgentStatusChange(status: number, data?: unknown): void {\r\n    switch (status) {\r\n      case AgentStatus.SYNCING:\r\n        this.switchState(StatusBarState.SyncingAgent);\r\n        this.setStatusBarView();\r\n        break;\r\n      case AgentStatus.SYNC_COMPLETED:\r\n        // 根据登录和启用状态返回之前的状态\r\n        this.switchEnableOrDisableCodeComplete();\r\n        break;\r\n      case AgentStatus.SYNC_FAILED:\r\n        this.switchState(StatusBarState.AgentSyncFailed);\r\n        this.setStatusBarView();\r\n        break;\r\n      case AgentStatus.STARTUP_FAILED:\r\n        this.switchState(StatusBarState.AgentStartupFailed);\r\n        this.setStatusBarView();\r\n        break;\r\n      case AgentStatus.INDEXING:\r\n        this.switchState(StatusBarState.CodeIndexing);\r\n        // 传递索引进度百分比\r\n        this.setStatusBarView(data as number);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/statusbar/srdStatusBar.ts b/src/statusbar/srdStatusBar.ts
--- a/src/statusbar/srdStatusBar.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/statusbar/srdStatusBar.ts	(date 1750821028739)
@@ -210,11 +210,7 @@
    * 设置状态栏视图
    */
   private setStatusBarView(code?: number) {
-    if (process.env.ISSEC === 'false') {
-      this.item.setStatusView(this.currentState, code);
-    } else {
-      this.item.setStatusViewSec(this.currentState, code); //if sec, display sec setting button
-    }
+    this.item.setStatusView(this.currentState, code);
   }
 
   /**
Index: src/statusbar/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import * as vscode from 'vscode';\r\nimport SrdStatusBar from './srdStatusBar';\r\nimport { RtnCode, RtnMessage, SrdCommand } from '../common/constants';\r\nimport { LoginServiceInst } from '../service/loginService';\r\nimport { FORBIDDEN_PATH, NO_SUBSCRIBE_PATH, HELPDOC_PATH } from '../common/config';\r\nimport AutoCompleteStatusStore from './autoCompleteStatusStore';\r\nimport { getHttpServerHost } from '../utils/envUtil';\r\n\r\n/**\r\n * 注册状态栏\r\n * @param context context\r\n */\r\nexport function registerSrdStatusBar(context: vscode.ExtensionContext) {\r\n  // 初始自动补全状态\r\n  AutoCompleteStatusStore.initEnabled();\r\n\r\n  // 初始化状态栏\r\n  const srdStatusBar = new SrdStatusBar(context);\r\n  srdStatusBar.setObservable(LoginServiceInst);\r\n  srdStatusBar.init();\r\n\r\n  /**\r\n   * 注册状态栏\r\n   */\r\n  context.subscriptions.push(srdStatusBar);\r\n\r\n  /**\r\n   * 注册服务异常展示\r\n   */\r\n  context.subscriptions.push(\r\n    vscode.commands.registerCommand(SrdCommand.SHOW_SVR_ERROR, async (code: RtnCode) => {\r\n      const selection = await vscode.window.showInformationMessage(\r\n        RtnMessage[code],\r\n        '详情',\r\n        '取消'\r\n      );\r\n\r\n      if (selection === '详情') {\r\n        const path = code === RtnCode.INVALID_USER ? NO_SUBSCRIBE_PATH : FORBIDDEN_PATH;\r\n        const uri = `${getHttpServerHost()}${path}`;\r\n        vscode.env.openExternal(vscode.Uri.parse(uri));\r\n      }\r\n    })\r\n  );\r\n\r\n  /**\r\n   * 注册启用补全命令\r\n   */\r\n  context.subscriptions.push(\r\n    vscode.commands.registerCommand(SrdCommand.TOGGLE_CODECOMPLETE, async () => {\r\n      const enabled = await AutoCompleteStatusStore.checkIfEnabled();\r\n\r\n      if (enabled) {\r\n        await AutoCompleteStatusStore.setEnabled(false);\r\n        srdStatusBar.setIfEnabled(false);\r\n      } else {\r\n        await AutoCompleteStatusStore.setEnabled(true);\r\n        srdStatusBar.setIfEnabled(true);\r\n      }\r\n    })\r\n  );\r\n\r\n  /**\r\n   * 注册打开帮助文档命令\r\n   */\r\n  context.subscriptions.push(\r\n    vscode.commands.registerCommand(SrdCommand.OPEN_HELPDOC, () => {\r\n      const uri = `${getHttpServerHost()}${HELPDOC_PATH}`;\r\n      vscode.env.openExternal(vscode.Uri.parse(uri));\r\n    })\r\n  );\r\n\r\n  return srdStatusBar;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/statusbar/index.ts b/src/statusbar/index.ts
--- a/src/statusbar/index.ts	(revision 8d30f877ae488db36d028ca282f14e5ca606430d)
+++ b/src/statusbar/index.ts	(date 1750834144829)
@@ -1,4 +1,5 @@
 import * as vscode from 'vscode';
+import * as path from 'path';
 import SrdStatusBar from './srdStatusBar';
 import { RtnCode, RtnMessage, SrdCommand } from '../common/constants';
 import { LoginServiceInst } from '../service/loginService';
@@ -63,10 +64,98 @@
   /**
    * 注册打开帮助文档命令
    */
+  console.log('[DEBUG] Registering OPEN_HELPDOC command:', SrdCommand.OPEN_HELPDOC);
   context.subscriptions.push(
     vscode.commands.registerCommand(SrdCommand.OPEN_HELPDOC, () => {
-      const uri = `${getHttpServerHost()}${HELPDOC_PATH}`;
-      vscode.env.openExternal(vscode.Uri.parse(uri));
+      console.log('[DEBUG] OPEN_HELPDOC command triggered');
+      console.log('[DEBUG] process.env.ISSEC:', process.env.ISSEC);
+
+      if (process.env.ISSEC !== 'false') {
+        // 如果是 Secidea 品牌，打开本地 HTML 文件
+        const localHtmlPath = path.join(context.extensionPath, 'page', 'index.html');
+        console.log('[DEBUG] Local HTML path:', localHtmlPath);
+
+        try {
+          const uri = vscode.Uri.file(localHtmlPath);
+          console.log('[DEBUG] URI:', uri.toString());
+          vscode.env.openExternal(uri);
+          console.log('[DEBUG] openExternal called successfully');
+        } catch (error) {
+          console.error('[DEBUG] Error opening local file:', error);
+          vscode.window.showErrorMessage(`无法打开帮助文档: ${error}`);
+        }
+      } else {
+        // 否则打开外部链接
+        const uri = `${getHttpServerHost()}${HELPDOC_PATH}`;
+        console.log('[DEBUG] External URI:', uri);
+        vscode.env.openExternal(vscode.Uri.parse(uri));
+      }
+    })
+  );
+
+  /**
+   * 注册状态栏菜单命令
+   */
+  context.subscriptions.push(
+    vscode.commands.registerCommand(SrdCommand.SHOW_STATUS_MENU, async (state: any, code?: number) => {
+      const items: vscode.QuickPickItem[] = [];
+
+      // 根据状态添加相应的菜单项
+      if (state === 'CodeCompleteEnabled') {
+        items.push({
+          label: '$(circle-slash) 禁用自动补全',
+          description: '点击禁用代码自动补全功能',
+          detail: 'toggle'
+        });
+      } else if (state === 'CodeCompleteDisabled') {
+        items.push({
+          label: '$(check) 启用自动补全',
+          description: '点击启用代码自动补全功能',
+          detail: 'toggle'
+        });
+      } else if (state === 'NotLogin') {
+        items.push({
+          label: '$(sign-in) 登录',
+          description: '点击进行登录',
+          detail: 'login'
+        });
+      }
+
+      // 始终显示设置选项
+      items.push({
+        label: '$(gear) 设置',
+        description: '打开海云安代码助手设置',
+        detail: 'settings'
+      });
+
+      // 添加帮助文档选项
+      items.push({
+        label: '$(question) 帮助文档',
+        description: '查看帮助文档',
+        detail: 'help'
+      });
+
+      const selected = await vscode.window.showQuickPick(items, {
+        placeHolder: '选择操作',
+        matchOnDescription: true
+      });
+
+      if (selected) {
+        switch (selected.detail) {
+          case 'toggle':
+            vscode.commands.executeCommand(SrdCommand.TOGGLE_CODECOMPLETE);
+            break;
+          case 'login':
+            vscode.commands.executeCommand(SrdCommand.LOGIN);
+            break;
+          case 'settings':
+            vscode.commands.executeCommand(SrdCommand.SETTINGS);
+            break;
+          case 'help':
+            vscode.commands.executeCommand(SrdCommand.OPEN_HELPDOC);
+            break;
+        }
+      }
     })
   );
 
