import * as http from 'http';
import * as url from 'url';
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { LOGIN_REDIRECT_URI, userInfoManager } from './loginUtils';

export async function startHttpServer(context: vscode.ExtensionContext, port: number) {
  const server = http.createServer(async (req, res) => {
    const reqUrl = url.parse(req.url || '', true);
    // 使用 JetBrains 插件相同的重定向 URL
    if (reqUrl.pathname === LOGIN_REDIRECT_URI) {
      // 获取 code 参数
      const code = reqUrl.query.code as string;
      const authResult = await userInfoManager.handleCode(code);
      // 选择不同展示页面
      let htmlPath = '';
      if (authResult === '') {
        htmlPath = path.join(context.extensionPath, 'page', 'error.html');
      } else {
        htmlPath = path.join(context.extensionPath, 'page', 'index.html');
      }
      fs.readFile(htmlPath, 'utf8', (err, htmlContent) => {
        if (err) {
          res.writeHead(500);
          return res.end('Failed to load index.html');
        }

        // 返回 HTML 页面
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(htmlContent);
      });
    }
  });

  server.listen(port, '127.0.0.1', () => {});
  context.subscriptions.push(new vscode.Disposable(() => server.close()));
}
