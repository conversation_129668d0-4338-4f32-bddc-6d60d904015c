import * as vscode from 'vscode';
import { CodeIndexService } from "../codeindex/CodeIndexService";
import { ComposerService } from '../composer/ComposerService';
import { generateCoreAgentConfig, generateTabbyAgentConfig } from '../composer/util';
import { Logger } from '../utils/logger';
import { AgentManager } from './agentmanager';
import { IAgentMessageReceiver } from './commclient/MessageReceiver';
import { TabbyAgentMessageReceiver } from './commclient/tabbyAgentReceiver';
import { AgentConfig } from './types';
import { getCodeAIConfig } from '../common/globalContext';

/**
 * 注册ActivityBar的ChatView
 * @param context Extension context
 */
export async function initAgent(
  composerServicer: ComposerService,
  indexVersion?: string
) {
  /**
   * 实例化composerService和其他接收器
   */
  const coreConfigTemp = await generateCoreAgentConfig();
  const coreConfig = { ...coreConfigTemp, indexVersion }
  const tabbyConfig = await generateTabbyAgentConfig();
  const agentConfig = new Map<string, AgentConfig>([
    ['core', coreConfig],
    ['tabby', tabbyConfig]
  ]);

  // 创建接收器映射，包含核心和Tabby接收器
  const tabbyAgentReceiver = new TabbyAgentMessageReceiver();

  // 创建接收器映射，包含核心和Tabby接收器
  const agentMessageReceiver = new Map<string, IAgentMessageReceiver>([
    ['core', composerServicer],
    ['tabby', tabbyAgentReceiver]
  ]);

  // 初始化Agent管理器
  AgentManager.getInstance().init(agentConfig, process.env.ISSEC !== 'false' ? 'oscap' : 'codefree', agentMessageReceiver);
  const downloadSuccess = await AgentManager.getInstance().checkAndDownloadAgents();

  // 只有全部下载成功时才继续初始化
  if (downloadSuccess) {
    await AgentManager.getInstance().initialize();

    // 启动代码索引服务
    CodeIndexService.getInstance().startIndexing(60);

    // 补全服务初始化：
    // todo:延期启动
    const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
    // 启动代码补全服务
    if (tabbyClient) {
      tabbyClient.request('initialize', {}, null, (data: any) => {
        Logger.debug(`cplService initialize response:${JSON.stringify(data)}`);
      });


      // todo:扫描整个workspace
      const projectPath = vscode.workspace.workspaceFolders![0].uri.fsPath;

      let codeCompleteStrategy = ['rlcc'];
      try {
        codeCompleteStrategy = JSON.parse(getCodeAIConfig()!.codeCompleteStrategy)
      } catch {
        Logger.error(`[agent index] initAgent codeCompleteStrategy: ${getCodeAIConfig()!.codeCompleteStrategy}`)
      }

      const projectInitializeRequest = [
        // 项目路径
        projectPath,
        // 执行策略：目前只支持rlcc
        // codeCompleteStrategy,
      ['rlcc'],
        // ['rlcc', 'bm25']
      ]

      // 启动项目初始化: 项目路径、代码补全策略
      tabbyClient.request('projectInitialize', projectInitializeRequest, null, (data: any) => {
        Logger.debug(`cplService start projectInitialize response:${JSON.stringify(data)}`);
      });

      // 初始化需要时间，等待4秒
      // await new Promise(resolve => setTimeout(resolve, 4000));
    }
  } else {
    Logger.error('[AgentIndex] 由于下载失败，跳过初始化操作');
  }
}
