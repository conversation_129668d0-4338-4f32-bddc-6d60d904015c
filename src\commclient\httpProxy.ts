import { HttpProxyAgent } from 'http-proxy-agent';
import { workspace } from 'vscode';

/**
 * 获取vscode设置中配置的代理
 * @returns
 */
export function getHttpProxy() {
  const proxySettings = workspace.getConfiguration().get<string>('http.proxy');

  if (!proxySettings) {
    return { agent: undefined, rejectUnauthorized: false };
  }

  const proxyUrl = new URL(proxySettings);
  if (proxyUrl.protocol !== 'https:' && proxyUrl.protocol !== 'http:') {
    return { agent: undefined, rejectUnauthorized: false };
  }

  const rejectUnauthorized = workspace.getConfiguration().get('http.proxyStrictSSL', true);

  return {
    agent: new HttpProxyAgent(proxySettings),
    rejectUnauthorized,
  };
}
