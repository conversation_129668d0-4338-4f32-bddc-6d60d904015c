import { WebViewReqCommand } from "./constants/common";

export const isJet = !(window as any).acquireVsCodeApi

export const isMac = navigator.userAgent.toLowerCase().includes('mac')

let vscode: any = null

if (!isJet) {
  vscode = acquireVsCodeApi()
}
/**
 * 
 * @param msg { command, data }
 */
export function postMessage(msg: any): void {
  if (!isJet) {
    if (![WebViewReqCommand.RETRIVE_CODE_SELECTION].includes(msg.command)) {
      // console.time(`postMessage[${msg.command}]`)
    }
    vscode.postMessage(msg);
  } else {
    console.log("post: ", msg);
    window.ideQuery({
      request: JSON.stringify(msg),
      persistent: false,
      onSuccess: function (response: any) {
        window.postMessage("");
      },
      onFailure: function (error_code: number, error_message: string) {
        console.log(error_code + ":" + error_message);
      }
    })
  }
}
/**
 * postMessage 的 promise 写法
 * @param message { command, _command, data }
 * @returns 
 */
export async function postRequest(message: any): Promise<any> {
  return new Promise((resolve, reject) => {
    const handleResponse = ({ data: payload }: any) => {
      if (payload.command === message._command && payload.data.reqType === message.data.reqType) {
        resolve(payload);
        window.removeEventListener('message', handleResponse);
      }
    };
    window.addEventListener('message', handleResponse);
    setTimeout(() => {
      window.removeEventListener('message', handleResponse);
    }, 1000 * 60) // 60 秒后自动移除事件，怕请求没回应导致重复回应问题
    console.log('postRequest', message)
    postMessage(message)

  });
};
