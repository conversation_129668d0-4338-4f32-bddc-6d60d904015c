import * as vscode from 'vscode';
import * as fs from 'fs';
import { Logger } from '../utils/logger';
import * as path from 'path';
import RunCompletionCache from './runCompletionCache';
import { inferLanguage, isSupportedLanguage } from './languages';
import { WorkspaceFoldersChangeEvent } from 'vscode';
import { AgentManager } from '../agent/agentmanager';
import { TabbyAgentClient } from '../agent/commclient/tabbyAgentClient';
import { UpdatedFile } from './types';

// 文件系统监听器，包含工作区监听以及文件变动监听
export class FileWatchManager implements vscode.Disposable {
  private watcher!: vscode.FileSystemWatcher;
  private disposables: vscode.Disposable[] = [];
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
  }


  // 初始化项目监听器（包含工作区监听以及文件变动监听）
  public async init(context: vscode.ExtensionContext, globPattern: string) {
    this.disposables.push(
      vscode.Disposable.from(

        // 注册workspace监听器
        this.registerWorkspaceListener(context),

        // 注册文件监听系统
        this.registerCreateFileChangeListener(globPattern)
      )
    );
  }

  // 用于注册文件监听系统
  private registerCreateFileChangeListener(globPattern: string) {
    this.watcher = vscode.workspace.createFileSystemWatcher(globPattern);

    this.watcher.onDidCreate(this.handleCreate, this);
    this.watcher.onDidChange(this.handleChange, this);
    this.watcher.onDidDelete(this.handleDelete, this);
    return this.watcher;
  }


  // 用于注册工作区监听器
  private registerWorkspaceListener(context: vscode.ExtensionContext) {
    // 使用 Logger 输出调试信息，标记当前正在注册工作区提供者
    Logger.debug('[WorkspaceProvider] registerWorkspaceProvider');

    // 注册一个当工作区文件夹发生变化时的监听器，返回该监听器的引用
    return vscode.workspace.onDidChangeWorkspaceFolders(async event => {
      Logger.debug(`[WorkspaceProvider] onDidChangeWorkspaceFolders, ${event.added.length}`);
      await this.handleWorkspaceChange(event).catch(err =>
        Logger.error(`Error reading files: ${err}`)
      );
    });
  }


  // 用于处理工作区文件夹变化事件
  private async handleWorkspaceChange(event: WorkspaceFoldersChangeEvent) {

    // 处理工作区新增逻辑
    if (event.added.length > 0) {
      for (const folder of event.added) {
        const folderPath = folder.uri.fsPath;
        await this.onDirectoryCreated(folderPath);
      }
    }

    // 处理工作区文件删除逻辑
    if (event.removed.length > 0) {
      for (const folder of event.removed) {
        const folderPath = folder.uri.fsPath;
        await this.onDirectoryDeleted(folderPath);
      }
    }
  }

  private async handleCreate(uri: vscode.Uri): Promise<void> {
    Logger.debug(`File created: ${uri.fsPath}`);
    RunCompletionCache.clearCache();
    const path = uri.fsPath;
    try {
      if (fs.lstatSync(path).isDirectory()) {
        await this.onDirectoryCreated(path);
      } else {
        await this.onFileCreate(path);
      }
    } catch (e) {
      Logger.debug(`[registerWorkspaceProvider] handleCreate e: ${e}`);
    }
  }

  private async handleDelete(uri: vscode.Uri): Promise<void> {
    Logger.debug(`File deleted: ${uri.fsPath}`);
    RunCompletionCache.clearCache();
    const path = uri.fsPath;

    // 如果是文件
    if (this.isSupportFile(path)) {
      await this.onFileDeleted(path);
    }

    // todo：此处仍然需要做判断去识别是否为目录
    else if (this.isSupportDir(path)) {
      await this.onDirectoryDeleted(path);
    }
  }

  private async handleChange(uri: vscode.Uri): Promise<void> {
    Logger.debug(`File changed: ${uri.fsPath}`);
    RunCompletionCache.clearCache();
    const fullPath = uri.fsPath;
    await this.onFileChanged(fullPath);
  }

  private async onFileCreate(fullPath: string): Promise<void> {
    // 过滤文件再发给agent
    if (this.isSupportFile(fullPath)) {
      const updatedFile = {
        filePath: fullPath,
        updateType: 'create',
        isDirectory: false
      } as UpdatedFile

      this.sendToAgent(updatedFile)
    }
  }

  private onFileDeleted(fullPath: string) {
    // 过滤文件再发给agent
    if (this.isSupportFile(fullPath)) {
      const updatedFile = {
        filePath: fullPath,
        updateType: 'delete',
        isDirectory: false
      } as UpdatedFile

      this.sendToAgent(updatedFile)
    }
  }

  private async onFileChanged(fullPath: string): Promise<void> {
    // 过滤文件再发给agent
    if (this.isSupportFile(fullPath)) {
      const updatedFile = {
        filePath: fullPath,
        updateType: 'change',
        isDirectory: false
      } as UpdatedFile

      this.sendToAgent(updatedFile)
    }
  }

  private async onDirectoryCreated(dirPath: string) {
    // 过滤目录再发给agent
    if (this.isSupportDir(dirPath)) {
      const updatedFile = {
        filePath: dirPath,
        updateType: 'create',
        isDirectory: true
      } as UpdatedFile

      this.sendToAgent(updatedFile)
    }
  }

  private onDirectoryDeleted(dirPath: string) {
    // 过滤目录再发给agent
    if (this.isSupportDir(dirPath)) {
      const updatedFile = {
        filePath: dirPath,
        updateType: 'delete',
        isDirectory: true
      } as UpdatedFile

      this.sendToAgent(updatedFile)
    }
  }

  private sendToAgent(updatedFile :UpdatedFile): void {
    const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
    const id = TabbyAgentClient.generateUuid();

    tabbyClient?.request('updateFiles', [
      [updatedFile],
      { "signal": true }
    ],
      // agent client 对应消息 id
      id,
      (data: any) => this.receiveResult(data))
  }

  private receiveResult(data: any): void {
    // 输出日志
    Logger.info(`[registerWorkspaceProvider] updatedFile result: ${JSON.stringify(data)}`);
  }

  private isSupportDir(dirPath: string): boolean {
    // todo: 完善此处，优化io
    return true
  }

  private isSupportFile(path: string): boolean {
    const languageId = inferLanguage(path);
    return isSupportedLanguage(languageId)
  }

  public dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
  }
}
