import * as vscode from 'vscode';
import { OutputChannel } from 'vscode';
import { formatDate } from './time.utils';
import { APP_NAME } from '../common/constants';

enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export const Logger = new (class Logger implements vscode.Disposable {
  private outputChannel: OutputChannel;

  private logLevel: LogLevel = LogLevel.DEBUG;

  public constructor() {
    this.outputChannel = vscode.window.createOutputChannel(APP_NAME);

    if (process.env.LOG_LEVEL !== undefined) {
      this.logLevel = parseInt(process.env.LOG_LEVEL) as LogLevel;
    }
  }

  public dispose() {
    this.outputChannel.dispose();
  }

  public show(): void {
    this.outputChannel.show();
  }

  public debug(message: string): void {
    this.log(LogLevel.DEBUG, message);
  }

  public info(message: string): void {
    this.log(LogLevel.INFO, message);
  }

  public warn(message: string): void {
    this.log(LogLevel.WARN, message);
  }

  public error(message: string): void {
    this.log(LogLevel.ERROR, message);
  }

  private log(level: LogLevel, message: string): void {
    if (level < this.logLevel) {
      return;
    }

    const now = new Date();
    const prefix = `${formatDate(now)} [${LogLevel[level]}]`;
    this.outputChannel.appendLine(`${prefix}: ${message}\n`);
  }
})();
