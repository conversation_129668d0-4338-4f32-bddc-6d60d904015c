/**
 * 计算耗时
 */
export default class TimeCounter {
  private startTime: number | [number, number];

  public constructor() {
    this.startTime = process.hrtime();
  }

  public getDuration() {
    return this.hrtimeToMs(process.hrtime(this.startTime as [number, number]));
  }

  private hrtimeToMs(hrtime: [number, number]): number {
    const seconds = hrtime[0];
    const nanoseconds = hrtime[1];
    return seconds * 1000 + nanoseconds / 1000000;
  }
}
