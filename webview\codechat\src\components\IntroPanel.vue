<template>
  <div class="intro-panel-container">
    <div class="intro-notes">
      <CodeLoadingBubble style="border-radius: 4px;padding-top: 5px;" :silent="true" v-if="!chatStore.codeFreeLoginStatus" :content="ChatTips
        .LOGIN_MSG" :logo="false" @shortcut="shortcut" @submitMsg="submitMsg"></CodeLoadingBubble>
      <CodeLoadingBubble :silent="true" v-else :content="firstMsg" :logo="false" @shortcut="shortcut" @submitMsg="submitMsg"></CodeLoadingBubble>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ChatTips } from '@/constants/common';
import CodeLoadingBubble from './CodeLoadingBubble.vue';
import { useChatStore } from '@/store/chat';
import {ref, onMounted} from 'vue'
const firstMsg = ref('')
const emit = defineEmits(['shortcut', 'submitMsg']);
const chatStore = useChatStore()
function shortcut(message: number) {
  emit('shortcut', message)
}
function submitMsg(message: string) {
  emit('submitMsg', message)
}

onMounted(() => {
  const version = chatStore.config.clientVersion
  console.log(localStorage.getItem('__new__' + version))
  if (localStorage.getItem('__new__' + version)) {
    firstMsg.value = ChatTips.FIRST_PROMPT_LOADED
  } else {
    firstMsg.value = ChatTips.FIRST_PROMPT
  }
})



</script>

<style>
.intro-panel-container {
  border-radius: 4px;
  padding-left: 20px;
  padding-right: 20px;
  width: 100%
}

.intro-notes {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 4px;
  background-color: var(--vscode-list-inactiveSelectionBackground);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-none-color);
}
</style>