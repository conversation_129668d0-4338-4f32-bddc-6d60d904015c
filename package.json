{"name": "Secidea-c10", "displayName": "海云智码", "publisher": "Secidea", "description": "你的 AI 代码助手，致力于提高编码效率", "version": "3.5.0-c10", "private": true, "engines": {"vscode": "^1.68.1"}, "icon": "assets/logo.png", "license": "SEE LICENSE IN LICENSE", "categories": ["Programming Languages", "Machine Learning", "Education", "Snippets"], "keywords": ["ai", "openai", "codex", "pilot", "snippets", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "c#", "java", "kotlin", "co-pilot"], "homepage": "https://secidea.com/Secidea/secidea", "repository": {"type": "git", "url": "https://secidea.com/Secidea/secidea"}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "enabledApiProposals": ["interactive", "interactiveUserActions"], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"commands": [{"command": "srd-copilot.settings", "title": "设置", "category": "海云智码", "icon": "$(gear)"}, {"command": "srd-copilot.startChat", "title": "开始聊天", "category": "海云智码"}, {"command": "srd-copilot.helpDocument", "title": "帮助文档", "category": "海云智码"}, {"command": "srd-copilot.login", "title": "海云智码登录", "category": "海云智码"}, {"command": "srd-copilot.codeComplete.enableToggle", "title": "代码补全启用/禁用", "category": "海云智码"}, {"command": "srd-copilot.showStatusMenu", "title": "显示状态菜单", "category": "海云智码"}, {"command": "srd-copilot.codeSelection.explain", "title": "解释代码", "category": "海云智码"}, {"command": "srd-copilot.codeSelection.unitTest", "title": "生成单元测试", "category": "海云智码"}, {"command": "srd-copilot.codeSelection.comment", "title": "生成代码注释", "category": "海云智码"}, {"command": "srd-copilot.codeSelection.optimization", "title": "生成代码优化建议", "category": "海云智码"}, {"command": "srd-copilot.openQuestion", "title": "帮助", "icon": {"light": "assets/toolbar/question-light-draw.svg", "dark": "assets/toolbar/question-dark-draw.svg"}}, {"command": "srd-copilot.openFeedback", "title": "设置", "icon": {"light": "assets/toolbar/setting-light-draw.svg", "dark": "assets/toolbar/setting-dark-draw.svg"}}, {"command": "srd-copilot.closeSidebar", "title": "关闭", "icon": {"light": "assets/toolbar/close-light-draw.svg", "dark": "assets/toolbar/close-dark-draw.svg"}}, {"command": "srd-copilot.codeDiff.acceptAllChanges", "title": "Accept All Changes", "icon": "$(check-all)"}, {"command": "srd-copilot.codeDiff.revertAllChanges", "title": "Revert All <PERSON>", "icon": "$(discard)"}, {"command": "srd-copilot.codeLensAction", "title": "代码透镜"}], "keybindings": [{"command": "editor.action.inlineSuggest.trigger", "key": "ctrl+enter", "mac": "cmd+enter", "when": "editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"}, {"command": "editor.action.inlineSuggest.showPrevious", "key": "alt+[", "mac": "alt+[", "when": "inlineSuggestionsVisible"}, {"command": "editor.action.inlineSuggest.showNext", "key": "alt+]", "mac": "alt+]", "when": "inlineSuggestionsVisible"}, {"command": "editor.action.inlineSuggest.commit", "key": "tab", "mac": "tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus"}, {"command": "srd-copilot.codeComplete.enableToggle", "key": "ctrl+alt+shift+o", "mac": "cmd+alt+shift+o", "when": "srd-copilot.activated"}, {"command": "srd-copilot.chat.focus", "key": "alt+shift+k", "mac": "alt+shift+k"}, {"command": "srd-copilot.startChat", "key": "alt+shift+k", "mac": "alt+shift+k"}], "menus": {"view/title": [{"command": "srd-copilot.openQuestion", "group": "navigation@0", "when": "view == srd-copilot.chat"}, {"command": "srd-copilot.openFeedback", "group": "navigation@1", "when": "view == srd-copilot.chat"}, {"command": "srd-copilot.closeSidebar", "group": "navigation@2", "when": "view == srd-copilot.chat"}], "editor/context": [{"submenu": "srd-copilot/editor/context/menuItems", "group": "navigation"}], "srd-copilot/editor/context/menuItems": [{"command": "srd-copilot.helpDocument", "group": "srd-copilot-menu-group@1"}, {"command": "srd-copilot.login", "group": "srd-copilot-menu-group@2", "when": "!srd-copilot.activated"}, {"command": "srd-copilot.startChat", "group": "srd-copilot-menu-group@3"}, {"command": "srd-copilot.codeComplete.enableToggle", "group": "srd-copilot-menu-group@4", "when": "srd-copilot.activated"}, {"command": "srd-copilot.codeSelection.explain", "group": "srd-copilot-menu-group@5"}, {"command": "srd-copilot.codeSelection.unitTest", "group": "srd-copilot-menu-group@6"}, {"command": "srd-copilot.codeSelection.comment", "group": "srd-copilot-menu-group@7"}, {"command": "srd-copilot.codeSelection.optimization", "group": "srd-copilot-menu-group@8"}], "commandPalette": [], "editor/title": [{"when": "isInDiffEditor", "command": "srd-copilot.codeDiff.acceptAllChanges", "group": "navigation@1"}, {"when": "isInDiffEditor", "command": "srd-copilot.codeDiff.revertAllChanges", "group": "navigation@2"}]}, "submenus": [{"id": "srd-copilot/editor/context/menuItems", "label": "海云智码"}], "viewsContainers": {"activitybar": [{"id": "srd-chat", "title": "海云智码", "icon": "assets/chat.svg"}]}, "views": {"srd-chat": [{"id": "srd-copilot.chat", "type": "webview", "name": "海云智码"}]}, "icons": {"srd-copilot-unlogin": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e791"}}, "srd-copilot-error-info": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7a0"}}, "srd-copilot-unconnect": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e78d"}}, "srd-copilot-code-enabled": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e78e"}}, "srd-copilot-code-disabled": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e78f"}}, "srd-copilot-select": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7b4"}}, "srd-copilot-delete": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e763"}}, "srd-copilot-refresh": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7f7"}}, "srd-copilot-logo": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7b3"}}, "srd-copilot-cancel": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e65f"}}}, "iconFonts": [{"id": "srd-copilot-font", "src": [{"path": "assets/iconfont.woff", "format": "woff"}]}], "configuration": [{"title": "基础配置", "properties": {"海云智码.服务器地址": {"type": "string", "order": 0, "description": "协议://IP地址:端口/项目名,如 http://***********:3000/oscap", "pattern": "^https?://.+"}, "海云智码.登录状态": {"type": "string", "order": 1, "default": "未登录", "readOnly": true, "markdownDescription": "[**点此登录**](command:secidea-c10.login)  [**点此登出**](command:secidea-c10.logout)  \n  请先配置正确服务器地址，登录成功后，下方会显示用户信息"}, "海云智码.版本信息": {"type": "object", "order": 2, "markdownDescription": "插件版本号：3.5.0-c10 [检查更新](command:srd-copilot.checkUpdate)", "default": {"新版本插件安装包存放目录": "", "是否自动下载新版本": false, "是否开启更新提醒": false, "更新提醒间隔小时": 1}, "properties": {"新版本插件安装包存放目录": {"type": "string"}, "是否自动下载新版本": {"type": "boolean"}, "是否开启更新提醒": {"type": "boolean"}, "更新提醒间隔小时": {"type": "number"}}, "additionalProperties": false}, "海云智码.安全密钥": {"type": "string", "order": 10, "description": "此密钥为平台随机生成，需要密钥请向安全管理员索取"}}}, {"title": "补全对话配置", "properties": {"海云智码.是否展示函数级别快捷键": {"type": "boolean", "default": true, "description": "是否展示函数级别快捷键"}, "海云智码.补全模式": {"type": ["string"], "enum": ["精准优先", "平衡模式", "速度优先"], "default": "精准优先", "markdownDescription": "选择使用海云智码进行补全的模式", "order": 1}, "海云智码.快捷键配置": {"type": ["string"], "enum": ["默认", "自定义"], "default": "默认", "enumDescriptions": ["`Ctrl + Enter` 手动发起补全请求\n`Tab` 选择当前补全建议\n`Alt + [` 或 `Alt + ]` 查看上一条/下一条补全建议\n`Ctrl + Alt + Shift + O` 启用/禁用代码补全", "自定义快捷键"], "markdownDescription": "配置海云智码对应的补全快捷键", "order": 3}}}, {"title": "禁用补全语言", "properties": {"海云智码.禁用补全语言.ABAP": {"type": "boolean", "default": false, "description": "禁用ABAP语言的代码补全", "order": 1}, "海云智码.禁用补全语言.ActionScript": {"type": "boolean", "default": false, "description": "禁用ActionScript语言的代码补全", "order": 2}, "海云智码.禁用补全语言.Ada": {"type": "boolean", "default": false, "description": "禁用Ada语言的代码补全", "order": 3}, "海云智码.禁用补全语言.AsciiDoc": {"type": "boolean", "default": false, "description": "禁用AsciiDoc语言的代码补全", "order": 4}, "海云智码.禁用补全语言.Makefile": {"type": "boolean", "default": false, "description": "禁用Makefile语言的代码补全", "order": 5}, "海云智码.禁用补全语言.AppleScript": {"type": "boolean", "default": false, "description": "禁用AppleScript语言的代码补全", "order": 6}, "海云智码.禁用补全语言.Arc": {"type": "boolean", "default": false, "description": "禁用Arc语言的代码补全", "order": 7}, "海云智码.禁用补全语言.ASP": {"type": "boolean", "default": false, "description": "禁用ASP语言的代码补全", "order": 8}, "海云智码.禁用补全语言.Assembly": {"type": "boolean", "default": false, "description": "禁用Assembly语言的代码补全", "order": 9}, "海云智码.禁用补全语言.AutoHotkey": {"type": "boolean", "default": false, "description": "禁用AutoHotkey语言的代码补全", "order": 10}, "海云智码.禁用补全语言.AutoIt": {"type": "boolean", "default": false, "description": "禁用AutoIt语言的代码补全", "order": 11}, "海云智码.禁用补全语言.Awk": {"type": "boolean", "default": false, "description": "禁用Awk语言的代码补全", "order": 12}, "海云智码.禁用补全语言.Batch": {"type": "boolean", "default": false, "description": "禁用Batch语言的代码补全", "order": 13}, "海云智码.禁用补全语言.Bazel": {"type": "boolean", "default": false, "description": "禁用Bazel语言的代码补全", "order": 14}, "海云智码.禁用补全语言.BibTeX": {"type": "boolean", "default": false, "description": "禁用BibTeX语言的代码补全", "order": 15}, "海云智码.禁用补全语言.Bison": {"type": "boolean", "default": false, "description": "禁用Bison语言的代码补全", "order": 16}, "海云智码.禁用补全语言.BitBake": {"type": "boolean", "default": false, "description": "禁用BitBake语言的代码补全", "order": 17}, "海云智码.禁用补全语言.Blade": {"type": "boolean", "default": false, "description": "禁用Blade语言的代码补全", "order": 18}, "海云智码.禁用补全语言.Bash": {"type": "boolean", "default": false, "description": "禁用Bash语言的代码补全", "order": 19}, "海云智码.禁用补全语言.C": {"type": "boolean", "default": false, "description": "禁用C语言的代码补全", "order": 20}, "海云智码.禁用补全语言.CSharp": {"type": "boolean", "default": false, "description": "禁用C#语言的代码补全", "order": 21}, "海云智码.禁用补全语言.CPlusPlus": {"type": "boolean", "default": false, "description": "禁用C++语言的代码补全", "order": 22}, "海云智码.禁用补全语言.CMake": {"type": "boolean", "default": false, "description": "禁用CMake语言的代码补全", "order": 23}, "海云智码.禁用补全语言.COBOL": {"type": "boolean", "default": false, "description": "禁用COBOL语言的代码补全", "order": 24}, "海云智码.禁用补全语言.CoffeeScript": {"type": "boolean", "default": false, "description": "禁用CoffeeScript语言的代码补全", "order": 25}, "海云智码.禁用补全语言.ColdFusion": {"type": "boolean", "default": false, "description": "禁用ColdFusion语言的代码补全", "order": 26}, "海云智码.禁用补全语言.Clojure": {"type": "boolean", "default": false, "description": "禁用Clojure语言的代码补全", "order": 27}, "海云智码.禁用补全语言.CSS": {"type": "boolean", "default": false, "description": "禁用CSS语言的代码补全", "order": 28}, "海云智码.禁用补全语言.CSV": {"type": "boolean", "default": false, "description": "禁用CSV语言的代码补全", "order": 29}, "海云智码.禁用补全语言.CUDA": {"type": "boolean", "default": false, "description": "禁用CUDA语言的代码补全", "order": 30}, "海云智码.禁用补全语言.D": {"type": "boolean", "default": false, "description": "禁用D语言的代码补全", "order": 31}, "海云智码.禁用补全语言.Dart": {"type": "boolean", "default": false, "description": "禁用Dart语言的代码补全", "order": 32}, "海云智码.禁用补全语言.Delphi": {"type": "boolean", "default": false, "description": "禁用Delphi语言的代码补全", "order": 33}, "海云智码.禁用补全语言.Pascal": {"type": "boolean", "default": false, "description": "禁用Pascal语言的代码补全", "order": 34}, "海云智码.禁用补全语言.Diff": {"type": "boolean", "default": false, "description": "禁用Diff语言的代码补全", "order": 35}, "海云智码.禁用补全语言.Patch": {"type": "boolean", "default": false, "description": "禁用Patch语言的代码补全", "order": 36}, "海云智码.禁用补全语言.Dockerfile": {"type": "boolean", "default": false, "description": "禁用Dockerfile语言的代码补全", "order": 37}, "海云智码.禁用补全语言.DTD": {"type": "boolean", "default": false, "description": "禁用DTD语言的代码补全", "order": 38}, "海云智码.禁用补全语言.Erlang": {"type": "boolean", "default": false, "description": "禁用Erlang语言的代码补全", "order": 39}, "海云智码.禁用补全语言.Elixir": {"type": "boolean", "default": false, "description": "禁用Elixir语言的代码补全", "order": 40}, "海云智码.禁用补全语言.ElixirScript": {"type": "boolean", "default": false, "description": "禁用<PERSON><PERSON><PERSON>语言的代码补全", "order": 41}, "海云智码.禁用补全语言.Elm": {"type": "boolean", "default": false, "description": "禁用Elm语言的代码补全", "order": 42}, "海云智码.禁用补全语言.EEx": {"type": "boolean", "default": false, "description": "禁用EEx语言的代码补全", "order": 43}, "海云智码.禁用补全语言.LiveEEx": {"type": "boolean", "default": false, "description": "禁用LiveEEx语言的代码补全", "order": 44}, "海云智码.禁用补全语言.FSharp": {"type": "boolean", "default": false, "description": "禁用F#语言的代码补全", "order": 45}, "海云智码.禁用补全语言.Fortran": {"type": "boolean", "default": false, "description": "禁用Fortran语言的代码补全", "order": 46}, "海云智码.禁用补全语言.Fortran90": {"type": "boolean", "default": false, "description": "禁用Fortran90语言的代码补全", "order": 47}, "海云智码.禁用补全语言.Fish": {"type": "boolean", "default": false, "description": "禁用Fish语言的代码补全", "order": 48}, "海云智码.禁用补全语言.Forth": {"type": "boolean", "default": false, "description": "禁用Forth语言的代码补全", "order": 49}, "海云智码.禁用补全语言.GLSL": {"type": "boolean", "default": false, "description": "禁用GLSL语言的代码补全", "order": 50}, "海云智码.禁用补全语言.Go": {"type": "boolean", "default": false, "description": "禁用Go语言的代码补全", "order": 51}, "海云智码.禁用补全语言.GraphQL": {"type": "boolean", "default": false, "description": "禁用GraphQL语言的代码补全", "order": 52}, "海云智码.禁用补全语言.Groovy": {"type": "boolean", "default": false, "description": "禁用Groovy语言的代码补全", "order": 53}, "海云智码.禁用补全语言.Gradle": {"type": "boolean", "default": false, "description": "禁用Gradle语言的代码补全", "order": 54}, "海云智码.禁用补全语言.Haml": {"type": "boolean", "default": false, "description": "禁用Haml语言的代码补全", "order": 55}, "海云智码.禁用补全语言.Haskell": {"type": "boolean", "default": false, "description": "禁用Haskell语言的代码补全", "order": 56}, "海云智码.禁用补全语言.HCL": {"type": "boolean", "default": false, "description": "禁用HCL语言的代码补全", "order": 57}, "海云智码.禁用补全语言.HLSL": {"type": "boolean", "default": false, "description": "禁用HLSL语言的代码补全", "order": 58}, "海云智码.禁用补全语言.HTML": {"type": "boolean", "default": false, "description": "禁用HTML语言的代码补全", "order": 59}, "海云智码.禁用补全语言.HTTP": {"type": "boolean", "default": false, "description": "禁用HTTP语言的代码补全", "order": 60}, "海云智码.禁用补全语言.Haxe": {"type": "boolean", "default": false, "description": "禁用Haxe语言的代码补全", "order": 61}, "海云智码.禁用补全语言.INI": {"type": "boolean", "default": false, "description": "禁用INI语言的代码补全", "order": 62}, "海云智码.禁用补全语言.Java": {"type": "boolean", "default": false, "description": "禁用Java语言的代码补全", "order": 63}, "海云智码.禁用补全语言.JavaScript": {"type": "boolean", "default": false, "description": "禁用JavaScript语言的代码补全", "order": 64}, "海云智码.禁用补全语言.JSX": {"type": "boolean", "default": false, "description": "禁用JSX语言的代码补全", "order": 65}, "海云智码.禁用补全语言.JSON": {"type": "boolean", "default": false, "description": "禁用JSON语言的代码补全", "order": 66}, "海云智码.禁用补全语言.Julia": {"type": "boolean", "default": false, "description": "禁用Julia语言的代码补全", "order": 67}, "海云智码.禁用补全语言.Kotlin": {"type": "boolean", "default": false, "description": "禁用Kotlin语言的代码补全", "order": 68}, "海云智码.禁用补全语言.KotlinScript": {"type": "boolean", "default": false, "description": "禁用<PERSON><PERSON><PERSON>语言的代码补全", "order": 69}, "海云智码.禁用补全语言.LaTeX": {"type": "boolean", "default": false, "description": "禁用LaTeX语言的代码补全", "order": 70}, "海云智码.禁用补全语言.Less": {"type": "boolean", "default": false, "description": "禁用Less语言的代码补全", "order": 71}, "海云智码.禁用补全语言.Lisp": {"type": "boolean", "default": false, "description": "禁用Lisp语言的代码补全", "order": 72}, "海云智码.禁用补全语言.Lua": {"type": "boolean", "default": false, "description": "禁用Lua语言的代码补全", "order": 73}, "海云智码.禁用补全语言.MATLAB": {"type": "boolean", "default": false, "description": "禁用MATLAB/Objective-C语言的代码补全", "order": 74}, "海云智码.禁用补全语言.ObjectiveCPlusPlus": {"type": "boolean", "default": false, "description": "禁用Objective-C++语言的代码补全", "order": 75}, "海云智码.禁用补全语言.Markdown": {"type": "boolean", "default": false, "description": "禁用Markdown语言的代码补全", "order": 76}, "海云智码.禁用补全语言.Nix": {"type": "boolean", "default": false, "description": "禁用Nix语言的代码补全", "order": 77}, "海云智码.禁用补全语言.OCaml": {"type": "boolean", "default": false, "description": "禁用OCaml语言的代码补全", "order": 78}, "海云智码.禁用补全语言.Perl": {"type": "boolean", "default": false, "description": "禁用Perl语言的代码补全", "order": 79}, "海云智码.禁用补全语言.PHP": {"type": "boolean", "default": false, "description": "禁用PHP语言的代码补全", "order": 80}, "海云智码.禁用补全语言.PowerShell": {"type": "boolean", "default": false, "description": "禁用PowerShell语言的代码补全", "order": 81}, "海云智码.禁用补全语言.ProtocolBuffers": {"type": "boolean", "default": false, "description": "禁用Protocol Buffers语言的代码补全", "order": 82}, "海云智码.禁用补全语言.Python": {"type": "boolean", "default": false, "description": "禁用Python语言的代码补全", "order": 83}, "海云智码.禁用补全语言.R": {"type": "boolean", "default": false, "description": "禁用R语言的代码补全", "order": 84}, "海云智码.禁用补全语言.Ruby": {"type": "boolean", "default": false, "description": "禁用Ruby语言的代码补全", "order": 85}, "海云智码.禁用补全语言.Rust": {"type": "boolean", "default": false, "description": "禁用Rust语言的代码补全", "order": 86}, "海云智码.禁用补全语言.Sass": {"type": "boolean", "default": false, "description": "禁用Sass语言的代码补全", "order": 87}, "海云智码.禁用补全语言.Scala": {"type": "boolean", "default": false, "description": "禁用Scala语言的代码补全", "order": 88}, "海云智码.禁用补全语言.SCSS": {"type": "boolean", "default": false, "description": "禁用SCSS语言的代码补全", "order": 89}, "海云智码.禁用补全语言.SQL": {"type": "boolean", "default": false, "description": "禁用SQL语言的代码补全", "order": 90}, "海云智码.禁用补全语言.Stylus": {"type": "boolean", "default": false, "description": "禁用Stylus语言的代码补全", "order": 91}, "海云智码.禁用补全语言.SVG": {"type": "boolean", "default": false, "description": "禁用SVG语言的代码补全", "order": 92}, "海云智码.禁用补全语言.Swift": {"type": "boolean", "default": false, "description": "禁用Swift语言的代码补全", "order": 93}, "海云智码.禁用补全语言.Tcl": {"type": "boolean", "default": false, "description": "禁用Tcl语言的代码补全", "order": 94}, "海云智码.禁用补全语言.Terraform": {"type": "boolean", "default": false, "description": "禁用Terraform语言的代码补全", "order": 95}, "海云智码.禁用补全语言.TypeScript": {"type": "boolean", "default": false, "description": "禁用TypeScript语言的代码补全", "order": 96}, "海云智码.禁用补全语言.TSX": {"type": "boolean", "default": false, "description": "禁用TSX语言的代码补全", "order": 97}, "海云智码.禁用补全语言.Twig": {"type": "boolean", "default": false, "description": "禁用Twig语言的代码补全", "order": 98}, "海云智码.禁用补全语言.Text": {"type": "boolean", "default": false, "description": "禁用Text语言的代码补全", "order": 99}, "海云智码.禁用补全语言.VBNET": {"type": "boolean", "default": false, "description": "禁用VB.NET语言的代码补全", "order": 100}, "海云智码.禁用补全语言.Vue": {"type": "boolean", "default": false, "description": "禁用Vue语言的代码补全", "order": 101}, "海云智码.禁用补全语言.XML": {"type": "boolean", "default": false, "description": "禁用XML语言的代码补全", "order": 102}, "海云智码.禁用补全语言.XSLT": {"type": "boolean", "default": false, "description": "禁用XSLT语言的代码补全", "order": 103}, "海云智码.禁用补全语言.YAML": {"type": "boolean", "default": false, "description": "禁用YAML语言的代码补全", "order": 104}, "海云智码.禁用补全语言.Zig": {"type": "boolean", "default": false, "description": "禁用Zig语言的代码补全", "order": 105}}}]}, "scripts": {"vsce:package": "node scripts/switch-assets.js && vsce package", "vsce:package:prod": "cross-env NODE_ENV=production node scripts/switch-assets.js && vsce package", "vscode:prepublish": "yarn run clean:dist && yarn run package", "compile": "yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run build-codechat", "package": "node scripts/switch-assets.js && webpack --mode production --devtool hidden-source-map && yarn run build-codechat", "watch": "yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run watch-codechat", "watch-vscode": "yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && webpack --watch", "watch-vscode-prod": "yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && cross-env NODE_ENV=production webpack --watch", "watch-codechat": "cd webview/codechat && yarn run build-watch", "build-codechat": "cd webview/codechat && yarn run build", "clean:dist": "<PERSON><PERSON><PERSON> dist", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "yarn run compile-tests && yarn run compile && yarn run lint", "lint": "eslint ./src --ext ts --fix", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "20.2.5", "@types/proper-lockfile": "^4.1.4", "@types/tmp": "^0.2.3", "@types/vscode": "^1.68.1", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@vscode/test-electron": "^2.3.2", "cross-env": "^7.0.3", "dotenv-webpack": "^8.0.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "glob": "^8.1.0", "mocha": "^10.2.0", "prettier": "^2.8.3", "rimraf": "^5.0.5", "ts-loader": "^9.4.3", "typescript": "^5.1.3", "vsce": "^2.15.0", "webpack": "^5.85.0", "webpack-cli": "^5.1.1"}, "dependencies": {"@ai-zen/node-fetch-event-source": "2.1.0", "@types/archiver": "^6.0.2", "@types/diff": "^7.0.2", "@types/markdown-it-attrs": "^4.1.3", "abort-controller": "^3.0.0", "archiver": "^7.0.1", "axios": "^1.8.4", "diff": "^7.0.0", "dotenv": "^8.6.0", "form-data": "^4.0.0", "fs-extra": "^11.3.0", "fuse.js": "6.4.6", "http-proxy-agent": "^7.0.0", "macaddress": "^0.5.3", "markdown-it-attrs": "^4.1.6", "node-fetch": "^3.3.2", "node-machine-id": "^1.1.12", "proper-lockfile": "^4.1.2", "rxjs": "^7.8.2", "uuid": "^11.1.0", "web-tree-sitter": "^0.23.0", "ws": "^8.14.0", "zip-lib": "^1.0.5"}, "optionalDependencies": {"bufferutil": "^4.0.7", "utf-8-validate": "^6.0.3"}, "workspaces": ["webview/*"], "__metadata": {"publisherDisplayName": "海云安"}}