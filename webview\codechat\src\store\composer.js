import { defineStore } from 'pinia';
import * as sender from '@/MessageSender'
import * as messageStorage from '@/composer/message-storage.js'
import { ActivityType } from '@/types';
import { WebViewReqCommand } from '@/constants/common';
import { normalizeLineEndings } from '@/utils';

export const useComposer = defineStore({
  id: 'composer',
  state: () => ({
    isAnswering: false,
    lastAnsweringText: '',
    stopBy: '', // createChat, stopBtn, '' 正常
    isWheeling: false, // 是否正在滚动消息列表
    stopBy: '', // createChat, stopBtn, '' 正常
    messageList: [
      // {
      //   role: 'user',
      //   text: '消息文本',
      //   reqTime: '2025-01-01 21:23:00',
      //   files: []
      // },
      // {
      //   role: 'assistant',
      //   text: '回答的内容',
      //   reqTime: '2025-01-01 21:23:00'
      // }
    ],
    diffList: [],
    recentlyUsedFiles: [],
    fileTree: [],
    folderList: [],
    currentSelectedList: [
      // {
      // type: 'file',//file // project // dir
      // }
    ], // 文件，代码库，目录
  }),
  getters: {
    // ...
  },
  actions: {
    async loadMessageList() {
      const dialogId = messageStorage.getDialogId()
      const payload = await sender.postRequest({
        command: 'composer-request',
        _command: 'composer-response',
        data: {
          reqType: 'loadhistory',
          dialogId,
        }
      })

      this.messageList = (payload.data.historyList || []).map(item => {
        const message = item.messages[0] || {}
        return {
          ...message,
          text: message.displayContent || message.content,
          reqTime: message.createTime,
          msg: { data: { contextInputItems: message.contextInputItems || [] } }
        }
      })
    },
    scrollToBottom() {
      if (this.isWheeling) return
      setTimeout(() => {
        const listView = document.querySelector('.message-list-view-root')
        console.log('scrollToBottom')
        if (listView) {
          listView.scrollTop = listView.scrollHeight
        }
      }, 200)
    },
    onStop() {
      const composer = this
      composer.stopAnswer()
    },

    createChat() {
      this.stopBy = 'createChat'
      this.onStop()
      this.messageList = []
      this.diffList = []
      this.currentSelectedList = []
      const dialogId = messageStorage.setProjectDialogId()
      sender.postRequest({
        command: 'composer-request',
        _command: 'composer-response',
        data: {
          reqType: 'deletehistory',
          dialogId
        }
      })
    },
    handleReportData(code) {
      if(!code) return
      code = normalizeLineEndings(code)
      console.log('handleReportData', {code})
      sender.postMessage({
        command: WebViewReqCommand.DATA_REPORT,
        data: {
          activityType: ActivityType.COMPOSER_ACCEPTED_CODE,
          answer: code,
        },
      });
    },
    handleChatGenReportData(code) {
      if (!code) return
      code = normalizeLineEndings(code)
      console.log('composer.handleChatGenReportData', {code})
      sender.postMessage({
        command: WebViewReqCommand.DATA_REPORT,
        data: {
          activityType: ActivityType.COMPOSER_GEN_CODE,
          answer: code
        },
      });
    },
    showDiff(path = '', content = '') {
      const data = {
        command: 'diff-view-vertical-request',
        _command: 'diff-view-vertical-response',
        data: {
          reqType: 'opendiffviewvertical',
          path,
          content
        }
      }
      console.log('show Diff', data)
      sender.postMessage(data)
    },
    stopAnswer() {
      sender.postMessage({
        command: 'composer-request',
        _command: 'composer-response',
        data: {
          reqType: 'stopcomposerchat',
          dialogId: messageStorage.getDialogId()
        }
      })
    },
    acceptChanged(path = '', content = '') {
      sender.postMessage({
        command: 'diff-view-vertical-request',
        _command: 'diff-view-vertical-response',

        data: {
          reqType: 'acceptfilediff',
          path, // 路径
          content, // 文件内容(变更后)
        }
      })
    },
    rejectChanged(path, content) {
      const data = {
        command: 'diff-view-vertical-request',
        _command: 'diff-view-vertical-response',

        data: {
          reqType: 'rejectfilediff',
          path, // 路径
          content // 文件内容(原始内容)
        }
      }
      sender.postMessage(data)
      console.log('rejectChanged', data)
    },
    // 撤销变更
    withdrawChange({ path, content, originalContent }) {
      const data = {
        command: 'diff-view-vertical-request',
        _command: 'diff-view-vertical-response',

        data: {
          reqType: 'undofilediff',
          path, // 路径
          originalContent, // 原始内容
          content // 文件内容(生成内容)
        }
      }
      sender.postMessage(data)
      console.log('withdrawChange', data)
    },

    // git 跳转
    gitJump() {
      const data = {
        command: 'workitem-request',
        _command: 'workitem-request',
        data: {
          reqType: 'openworkitempanel',
        }
      }
      sender.postMessage(data)
      console.log('gitJump', data)
    }
  }

})