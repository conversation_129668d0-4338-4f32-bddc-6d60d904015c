import { ExtensionContext, commands, window } from 'vscode';
import CodeNaturalManager from './codeNaturaManager';
import { IStatusBarHandler } from '../statusbar/types';
import { SrdCommand } from '../common/constants';
import CodeNaturalTask from './codeNaturalTask';
import { CodeNaturalDataType } from './types';

export async function registerCodeNatural(
  context: ExtensionContext,
  statusHandler: IStatusBarHandler
) {
  const naturalManager = new CodeNaturalManager(context, statusHandler);
  await naturalManager.init();

  /**
   * 注册打开自然语言输入框命令, 注册CodeLensProvider
   */
  context.subscriptions.push(naturalManager);

  /**
   * 注册自然语言接受回答指令
   */
  context.subscriptions.push(
    commands.registerCommand(SrdCommand.ACCEPT_CODE_NATURAL_ANSWER, (task?: CodeNaturalTask) => {
      if (!task && window.activeTextEditor) {
        task = naturalManager.getEventData(
          CodeNaturalDataType.TASK,
          window.activeTextEditor.document
        );
      }

      task?.acceptAnswer();
    })
  );

  /**
   * 注册自然语言拒绝回答指令
   */
  context.subscriptions.push(
    commands.registerCommand(SrdCommand.REJECT_CODE_NATURAL_ANSWER, (task?: CodeNaturalTask) => {
      if (!task && window.activeTextEditor) {
        task = naturalManager.getEventData(
          CodeNaturalDataType.TASK,
          window.activeTextEditor.document
        );
      }

      task?.rejectAnswer();
    })
  );

  /**
   * 注册自然语言重新生成回答指令
   */
  context.subscriptions.push(
    commands.registerCommand(
      SrdCommand.REGENERATE_CODE_NATURAL_ANSWER,
      (task?: CodeNaturalTask) => {
        if (!task && window.activeTextEditor) {
          task = naturalManager.getEventData(
            CodeNaturalDataType.TASK,
            window.activeTextEditor.document
          );
        }

        task?.regenerateAnswer();
      }
    )
  );

  /**
   * 注册自然语言取消提问指令
   */
  context.subscriptions.push(
    commands.registerCommand(SrdCommand.CANCEL_CODE_NATURAL_QUESTION, (task?: CodeNaturalTask) => {
      if (!task && window.activeTextEditor) {
        task = naturalManager.getEventData(
          CodeNaturalDataType.TASK,
          window.activeTextEditor.document
        );
      }

      task?.cancelQuestion();
    })
  );
}
