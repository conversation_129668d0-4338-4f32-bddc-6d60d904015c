export class Conversation {
  private conversationId: string;
  private messageCache: Map<string, string>;
  private status: string;

  constructor(id: string, status: string = 'initial') {
    this.conversationId = id;
    this.messageCache = new Map<string, string>();
    this.status = status;
  }

  public getConversationId(): string {
    return this.conversationId;
  }

  public getStatus(): string {
    return this.status;
  }

  public setStatus(status: string): void {
    this.status = status;
  }

  public hasMessage(key: string): boolean {
    return this.messageCache.has(key);
  }

  public addMessage(key: string, value: string): void {
    this.messageCache.set(key, value);
  }

  public getMessage(key: string): string | undefined {
    return this.messageCache.get(key);
  }

  public clearMessages(): void {
    this.messageCache.clear();
  }

  public getAllMessages(): Map<string, string> {
    return new Map(this.messageCache);
  }

  public deleteMessageById(messageId: string): boolean {
    if (this.messageCache.has(messageId)) {
      this.messageCache.delete(messageId);
      return true;
    }
    return false;
  }
}