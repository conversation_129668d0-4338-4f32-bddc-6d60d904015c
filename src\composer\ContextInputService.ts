import { Logger } from '../utils/logger';
import { AgentManager } from '../agent/agentmanager';
import { CORE_AGENT } from '../agent/types';
import { ContextInputItem, ContextOutputItem, Provider } from './types';
import { v4 as uuidv4 } from 'uuid';
import * as vscode from 'vscode';
import { getProjectInfo } from '../utils/textEditor';
import { join } from 'path';

// 定义 PendingTask 接口
interface PendingTask {
  contextType: string;
  query: string;
  messageId: string;
  future: Promise<ContextOutputItem[]>;
  resolve: (value: ContextOutputItem[]) => void;
  reject: (reason?: any) => void;
}

export class ContextInputService {
  private pendingTasks = new Map<string, PendingTask>();

  private timeoutDuration = 30; // 超时时间（秒）

  private timeoutTimers = new Map<string, NodeJS.Timeout>();

  /**
   * 获取输入的上下文代码项
   *
   * @param input 用户输入的查询字符串
   * @param contextInputItems 上下文输入项列表，包含代码库和文件夹类型的上下文项
   * @returns 一个元组，包含：
   *         - 上下文输出项列表，包含名称、描述、内容和提供者信息
   *         - 按格式 "itemValue:name" 排序的组合列表
   */
  public async getContextCodeItemForInput(
    input: string,
    contextInputItems: ContextInputItem[]
  ): Promise<[ContextOutputItem[], string[]]> {
    const results: ContextOutputItem[] = [];
    const combinedList: string[] = [];

    // 处理每个上下文项
    for (const item of contextInputItems) {
      try {
        // 仅处理工作区和文件夹类型的上下文项
        if (item.itemTag !== 'codebase' && item.itemTag !== 'folder') {
          continue;
        }

        const messageId = uuidv4();

        // 创建一个将在接收到响应时被解析的 Promise
        let resolvePromise: (value: ContextOutputItem[]) => void;
        let rejectPromise: (reason?: any) => void;

        const future = new Promise<ContextOutputItem[]>((resolve, reject) => {
          resolvePromise = resolve;
          rejectPromise = reject;
        });
        // 创建待处理任务
        const task: PendingTask = {
          contextType: item.itemTag === 'codebase' ? 'codebase' : 'folder',
          // query: item.itemTag === 'codebase' ? getProjectInfo().projectPath: item.itemValue,
          query: item.itemTag === 'codebase' ? '' : item.itemValue,
          messageId,
          future,
          resolve: resolvePromise!,
          reject: rejectPromise!,
        };

        this.pendingTasks.set(messageId, task);

        // 启动超时监控
        this.startTimeoutMonitor(messageId);

        // 发送请求
        const requestData = {
          name: task.contextType,
          query: task.query,
          fullInput: input,
          selectedCode: [] as string[],
        };


        const agentManager = AgentManager.getInstance();
        const agentClient = agentManager.getAgentCommClient(CORE_AGENT);

        if (!agentClient) {
          throw new Error('核心代理客户端不可用');
        }
        Logger.info(`[ContextInputService] send request: context/getContextItems, messageId: ${messageId}, requestData: ${JSON.stringify(requestData)}`);
        agentClient.request('context/getContextItems', requestData, messageId, (response: any) => {
          this.handleResponse(messageId, response);
        });

        // 等待结果，设置超时
        const result = await future;

        // 为每个结果项设置origin值，以便将召回的对象与原始的ContextInputItem对应起来
        // 如果itemTag是codebase，则origin设为'@codebase'，否则设为itemValue
        for (const outputItem of result) {
          outputItem.origin = item.itemTag === 'codebase' ? '@codebase' : item.itemValue;
        }

        results.push(...result);

        // 为每个结果项设置origin值，以便将召回的对象与原始的ContextInputItem对应起来
        // 如果itemTag是codebase，则origin设为'@codebase'，否则设为itemValue
        for (const outputItem of result) {
          outputItem.origin = item.itemTag === 'codebase' ? '@codebase' : item.itemValue;
        }

        // 组合 itemValue 和 name
        for (const outputItem of result) {
          // combinedList.push(`${item.itemValue}:${outputItem.name}`);
          if (outputItem.name === 'Instructions') { 
            continue;
          }
          combinedList.push(`${outputItem.name}`);
        }
      } catch (e) {
        Logger.error(`处理上下文项时出错: ${item.itemTag}, error=${e}`);
      }
    }

    let fileList: string[] = [];
    contextInputItems
      .filter(item => item.itemTag === 'codefile' || item.itemTag === 'file')
      .forEach(item => {
        const relativePath = vscode.workspace.asRelativePath(item.itemValue, false);
        // fileList.push(relativePath);
        fileList.push(this.formatFileRange(relativePath));
      })


    // 排序组合列表
    const sortedCombinedList = this.sortCombinedList(contextInputItems, results, combinedList);

    Logger.info(`[ContextInputService] getContextCodeItemForInput fileList: ${JSON.stringify(fileList)},
    results: ${JSON.stringify(results)}, sortedCombinedList:${JSON.stringify(sortedCombinedList)}`);


    return [results, [...fileList, ...sortedCombinedList]];
  }

  private formatFileRange(input: string): string {
    // 匹配括号及其内容
    const matches = input.match(/\((\d+)-(\d+)\)/);
    if (!matches) {
      return input;
    }

    // 移除括号部分，添加冒号
    return input.replace(/\s*\(\d+-\d+\)/, `:${matches[1]}-${matches[2]}`);
  }

  /**
   * 排序组合列表
   *
   * 排序策略：
   * 1. 优先添加 itemTag 为 "codefile" 或 "file" 的项。
   * 2. 然后添加 contextType 为 "folder" 的项。
   * 3. 最后添加 contextType 为 "codebase" 的项。
   */
  private sortCombinedList(
    contextInputItems: ContextInputItem[],
    contextOutputItems: ContextOutputItem[],
    combinedList: string[]
  ): string[] {
    const sortedList: string[] = [];

    // 添加 itemTag 为 "codefile" 或 "file" 的项
    contextInputItems
      .filter(item => item.itemTag === 'codefile' || item.itemTag === 'file')
      .forEach(item => {
        combinedList
          .filter(combined => combined.startsWith(item.itemValue))
          .forEach(combined => sortedList.push(combined));
      });

    // 添加 contextType 为 "folder" 的项
    contextOutputItems
      .filter(outputItem => outputItem.id.providerTitle === 'folder')
      .forEach(outputItem => {
        combinedList
          .filter(combined => combined.endsWith(outputItem.name))
          .forEach(combined => sortedList.push(combined));
      });

    // 添加 contextType 为 "codebase" 的项
    contextOutputItems
      .filter(outputItem => outputItem.id.providerTitle === 'codebase')
      .forEach(outputItem => {
        combinedList
          .filter(combined => combined.endsWith(outputItem.name))
          .forEach(combined => sortedList.push(combined));
      });
    const formattedSortedList: string[] = [];

    sortedList.forEach(sortedItem => {
      formattedSortedList.push(this.formatFileRange(sortedItem));
    });

    return formattedSortedList;
    // return sortedList;
  }

  /**
   * 从输入字符串解析上下文项
   */
  private parseContextItems(input: string): Set<string> {
    const items = new Set<string>();
    const workspacePattern = /@codebase/g;
    const dirPattern = /@dir:\s*([^\s]+)/g;

    // 查找所有 @codebase 出现的位置并添加到待处理上下文
    let match;
    while ((match = workspacePattern.exec(input)) !== null) {
      items.add('@codebase');
    }

    // 查找所有 @dir 出现的位置并添加到待处理上下文
    while ((match = dirPattern.exec(input)) !== null) {
      const dir = match[1];
      items.add(`@dir:${dir}`);
    }

    return items;
  }

  /**
   * 启动超时监控
   */
  private startTimeoutMonitor(messageId: string): void {
    const timer = setTimeout(() => {
      const task = this.pendingTasks.get(messageId);
      if (task) {
        this.pendingTasks.delete(messageId);
        task.reject(new Error(`请求在 ${this.timeoutDuration} 秒后超时`));
      }
    }, this.timeoutDuration * 1000);

    this.timeoutTimers.set(messageId, timer);
  }

  /**
   * 处理响应数据
   */
  private handleResponse(messageId: string, response: any): void {
    const task = this.pendingTasks.get(messageId);
    if (!task) {
      return;
    }

    // 清除超时计时器
    const timer = this.timeoutTimers.get(messageId);
    if (timer) {
      clearTimeout(timer);
      this.timeoutTimers.delete(messageId);
    }

    try {
      if (!response) {
        task.resolve([]);
        return;
      }


      const items: ContextOutputItem[] = [];

      // 将响应数据解析为 ContextOutputItem 对象
      // if (response.items) {
      //   const itemsList = response.items as any[];
      //   for (const item of itemsList) {
      //     const provider: Provider = {
      //       providerTitle: item.providerTitle,
      //       itemId: item.itemId,
      //     };

      //     items.push({
      //       name: item.name,
      //       description: item.description,
      //       content: item.content,
      //       id: provider,
      //     });
      //   }
      // }
      const itemsList = response;
      for (const item of itemsList) {
        const provider: Provider = {
          providerTitle: item.id.providerTitle,
          itemId: item.id.itemId,
        };

        items.push({
          name: item.name,
          description: item.description,
          content: item.content,
          id: provider,
          origin: '',
        });
      }

      task.resolve(items);
    } catch (e) {
      Logger.error(`处理消息 ID: ${messageId} 的响应时出错, error=${e}`);
      task.reject(e);
    } finally {
      this.pendingTasks.delete(messageId);
    }
  }
}
