/**
 * 资源文件切换脚本
 * 根据环境变量 ISSEC 的值决定使用哪套静态资源
 * 
 * 脚本功能：
 * - 当 ISSEC=true 时, 从 assets/sec/ 目录复制文件到 assets/ 根目录
 * - 当 ISSEC=false 时, 从 assets/srd/ 目录复制文件到 assets/ 根目录
 * - 在复制前会清理 assets/ 根目录下除了 'srd' 和 'sec' 以外的所有文件
 * - 记录当前模式，避免重复切换
 */

const fs = require('fs-extra');
const path = require('path');
const dotenv = require('dotenv');

// 读取环境变量
const envPath = path.join(__dirname, '..', process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env');
const envConfig = dotenv.config({ path: envPath }).parsed || {};
const ISSEC = envConfig.ISSEC === 'true';

// 定义路径
const rootDir = path.join(__dirname, '..');
const assetsDir = path.join(rootDir, 'assets');
const srdDir = path.join(assetsDir, 'srd');
const secDir = path.join(assetsDir, 'sec');

console.log(`资源切换: ISSEC=${ISSEC}, 环境=${process.env.NODE_ENV || 'development'}`);

/**
 * 清理目标目录中的文件（除了特定排除的目录）
 * @param {string} dir 要清理的目录
 * @param {Array<string>} excludeDirs 要排除的目录名称
 */
function cleanDirectory(dir, excludeDirs) {
  // 检查目录是否存在
  if (!fs.existsSync(dir)) {
    return;
  }

  // 获取目录中的所有文件和文件夹
  const items = fs.readdirSync(dir);

  // 遍历所有项
  for (const item of items) {
    // 如果是排除的目录，则跳过
    if (excludeDirs.includes(item)) {
      continue;
    }

    const itemPath = path.join(dir, item);
    
    // 删除文件或目录
    fs.removeSync(itemPath);
    console.log(`删除文件/目录: ${item}`);
  }
}

/**
 * 备份并替换资源文件
 */
async function switchAssets() {
  try {
    // 根据ISSEC标志确定源目录
    const sourceDir = ISSEC ? secDir : srdDir;
    
    // 检查源目录是否存在
    if (!fs.existsSync(sourceDir)) {
      console.log(`未找到 ${ISSEC ? 'sec' : 'srd'} 资源目录，保持原有资源`);
      return;
    }

    console.log(`切换为 ${ISSEC ? 'sec' : 'srd'} 资源...`);
    
    // 清理 assets 目录下除了 'srd' 和 'sec' 以外的所有文件
    console.log('清理 assets 目录...');
    cleanDirectory(assetsDir, ['srd', 'sec']);
    
    // 获取源目录下的所有文件和文件夹
    const sourceFiles = fs.readdirSync(sourceDir);
    
    // 处理每个文件，复制到目标目录
    for (const file of sourceFiles) {
      const srcPath = path.join(sourceDir, file);
      const destPath = path.join(assetsDir, file);
      
      // 复制源目录下的文件到assets根目录
      console.log(`复制文件: ${file}`);
      fs.copySync(srcPath, destPath, { overwrite: true });
    }
    
    // 处理 README 文件
    const readmePath = path.join(rootDir, 'README.md');
    const readmeSecPath = path.join(rootDir, 'README-sec.md');
    const readmeSrdPath = path.join(rootDir, 'README-srd.md');
    
    // 删除原有的 README.md
    if (fs.existsSync(readmePath)) {
      console.log('删除原有 README.md');
      fs.removeSync(readmePath);
    }
    
    // 根据 ISSEC 复制对应的 README 文件
    const sourceReadmePath = ISSEC ? readmeSecPath : readmeSrdPath;
    if (fs.existsSync(sourceReadmePath)) {
      console.log(`复制 ${ISSEC ? 'README-sec.md' : 'README-srd.md'} 到 README.md`);
      fs.copySync(sourceReadmePath, readmePath);
    } else {
      console.log(`未找到 ${ISSEC ? 'README-sec.md' : 'README-srd.md'} 文件`);
    }
    
    // 处理 launch.json 文件
    const vscodeDir = path.join(rootDir, '.vscode');
    const launchPath = path.join(vscodeDir, 'launch.json');
    const launchSecPath = path.join(vscodeDir, 'launch-sec.json');
    const launchSrdPath = path.join(vscodeDir, 'launch-srd.json');
    
    // 删除原有的 launch.json
    if (fs.existsSync(launchPath)) {
      console.log('删除原有 launch.json');
      fs.removeSync(launchPath);
    }
    
    // 根据 ISSEC 复制对应的 launch 文件
    const sourceLaunchPath = ISSEC ? launchSecPath : launchSrdPath;
    if (fs.existsSync(sourceLaunchPath)) {
      console.log(`复制 ${ISSEC ? 'launch-sec.json' : 'launch-srd.json'} 到 launch.json`);
      fs.copySync(sourceLaunchPath, launchPath);
    } else {
      console.log(`未找到 ${ISSEC ? 'launch-sec.json' : 'launch-srd.json'} 文件`);
    }
    
    // 生成 webview 品牌配置
    const webviewDir = path.join(rootDir, 'webview', 'codechat');
    const brandConfigPath = path.join(webviewDir, 'brand.json');
    
    const brandConfig = {
      isSec: ISSEC,
    };
    
    fs.writeFileSync(
      brandConfigPath, 
      JSON.stringify(brandConfig, null, 2), 
      'utf-8'
    );
    
    console.log(`生成 webview 品牌配置: ${ISSEC ? 'Secidea' : 'SRD'}`);
    
    console.log(`成功切换为 ${ISSEC ? 'sec' : 'srd'} 资源`);
  } catch (error) {
    console.error('资源切换失败:', error);
    process.exit(1);
  }
}

// 执行切换
switchAssets(); 