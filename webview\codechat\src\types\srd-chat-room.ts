/**
 * qa对
 */
export type QuestionAnswerVo = {
  /**
   * 答案
   */
  answer?: string;
  /**
   * 答案是否由大语言模型生成
   */
  fromGlm?: boolean;
  /**
   * qa对的id，前端使用，后端不保存
   */
  qaId?: string;
  /**
   * 问题
   */
  question?: string;
  /**
   * 用户问题的记录id
   */
  questionRecordId?: number;
};

/**
 * 助手回答
 */
export type AssitResponseVo = {
  /**
   * 回答内容的记录id，最新版为qa内容
   */
  answerRecordId?: number;
  /**
   * 答案qa
   */
  qa?: QuestionAnswerVo;
  /**
   * 用户问题
   */
  question?: string;
  /**
   * 用户问题记录id
   */
  questionRecordId?: number;
};

export enum QuestionAnswersFrom {
  SERVICE = 'service', // 机器人
  USER = 'user', // 用户
}

// qa对
export type QA = AssitResponseVo;

// 问题和回答
export interface QuestionAnswers {
  historyRecordId?: number; // 历史记录id
  questionRecordId?: number; // 用户提问问题的id
  answerRecordId?: number; // 机器人回答的记录id
  userQuestion?: string; // 用户提问的问题
  qaQuestion?: string; // 机器人回答内容中返回的问题
  answer?: string; // 当前展示的答案，以富文本显示
  qa?: QA[]; // 当前展示的回答，以问题列表展示
  notHelpAnswers?: QA[]; // 保存机器人返回的相似问题列表，点击没帮助时会显示相似问题列表
  helpful?: boolean | undefined; // 是否有帮助 true有帮助 false无帮助 undefined未评价
  fromGlm?: boolean; // 是否是大模型回答
  time: string; // 时间
  from: QuestionAnswersFrom; // service机器人 user用户
  isShowHelp?: boolean; // 是否显示有帮助无帮助
  isShowFeedback?: boolean; // 是否显示去反馈
  isShowLoading?: boolean; // 是否显示加载中
  isShow?: boolean; // 是否显示该条消息
}

// 帮助指引
export interface GuideType {
  name: string;
  url: string;
}

/**
 * 聊天记录
 */
export type WebChatRecord = {
  chatDate?: string;
  chatTime?: string;
  helpful?: boolean;
  id: number;
  loginName?: string;
  msg?: string;
  /**
   * 用户提问时，传给智能助手的问题记录的id
   */
  questionRecordId?: number;
  /**
   * 类型  USER_QUESTION:用户提问  QA_ANSWER:qa对回答  PLAIN_TEXT_ANSWER：普通文本回答 LIST_ANSWER：列表回答；RELATED_QUESTION_LIST 相关问题列表;BEST_ANSWER 最佳答案回答
   */
  type?: WebChatRecord.type;
  userId?: number;
};

export namespace WebChatRecord {
  /**
   * 类型  USER_QUESTION:用户提问  QA_ANSWER:qa对回答  PLAIN_TEXT_ANSWER：普通文本回答 LIST_ANSWER：列表回答；RELATED_QUESTION_LIST 相关问题列表;BEST_ANSWER 最佳答案回答
   */
  export enum type {
    BEST_ANSWER = 'BEST_ANSWER',
    LIST_ANSWER = 'LIST_ANSWER',
    PLAIN_TEXT_ANSWER = 'PLAIN_TEXT_ANSWER',
    QA_ANSWER = 'QA_ANSWER',
    RELATED_QUESTION_LIST = 'RELATED_QUESTION_LIST',
    USER_QUESTION = 'USER_QUESTION',
  }
}

export interface SrdChatRequest { 
  type: string;
  question?: string;
  timeBefore?: string;
  currentPage?: number;
  pageSize?: number;
  requestBody?: unknown;
}