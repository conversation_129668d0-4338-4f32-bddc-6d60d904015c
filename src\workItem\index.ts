import * as vscode from 'vscode';
import { WorkItemViewProvider } from './workItemViewProvider';

export function registerWorkItemList(context: vscode.ExtensionContext) { 
  // todo: 1. 登录流程check? 未登录时拿不到apiKey 显示按钮和工作项list?
  // todo: 2. theme在webview端如何影响图标和css的变化 引入css（包括jetbrain）
  const provider = new WorkItemViewProvider(context);
  provider.initDisposable();
  
  const disposable = vscode.commands.registerCommand(
    'srd-copilot.aiScmCommand',
    () => vscode.commands.executeCommand('srd-copilot.workItemList.focus')
  );

  // Register stop commit analysis command
  const stopCommitAnalysisDisposable = vscode.commands.registerCommand(
    'srd-copilot.stopCommitAnalysis',
    () => provider.getWorkItemManager().stopCommitAnalysis()
  );

  context.subscriptions.push(disposable);
  context.subscriptions.push(stopCommitAnalysisDisposable);

  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(
      'srd-copilot.workItemList',
      provider, {
      webviewOptions: {
        retainContextWhenHidden: true,
      },
    }
  )
  );
}