<template>
  <div class="code-bubble ai-bubble">
    <div class="message-base-info">
      <div class="logo">
        <svg-icon :name="brandStore.isSec ? 'ai-logo-sec' : 'ai-logo'" size="24px"></svg-icon>
      </div>
      <div class="message-base-info-text">
        <span class="message-user-name">{{ brandStore.brandName }}</span>
      </div>
    </div>
    <div class="message">
      <div class="message-text">
        <p>未登录账号，
          <span class="link-btn" @click="submitLogin">请登录</span>
        </p>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/store/chat';
import { useBrandStore } from '@/store/brand';

const props = defineProps({
  msg: {
    type: String,
  },
});
const chatStore = useChatStore();
const brandStore = useBrandStore();

function submitLogin() {
  chatStore.handleSubmitLogin();
}
</script>

<style lang="scss">
.tip-bubble {
  padding: 20px;
  display: flex;
  justify-content: flex-start;

  .message {
    width: calc(100% - 32px);

    &-text {
      line-height: 20px;
      color: var(--vscode-inputOption-activeForeground);

      p:first-child,
      pre:first-child {
        margin-top: 0;
      }
    }
  }
}
</style>
