import { DiffViewManager } from './index';
import * as vscode from 'vscode';
import * as fsPromise from 'fs/promises';
import * as path from 'path';
import { SrdCommand } from '../../common/constants';
import { Logger } from '../../utils/logger';
import { DataReportServiceInst } from '../../service/dataReportService';
import { ActivityType } from '../../service/types/dataReport';
import { diffLines } from 'diff';

class DiffContentProvider implements vscode.TextDocumentContentProvider {
  provideTextDocumentContent(uri: vscode.Uri): string {
    return Buffer.from(uri.query, 'base64').toString('utf-8');
  }
}

export class DiffEditorViewManager extends DiffViewManager {
  static readonly DiffContentProviderId = 'codefree-diff';
  private static instance: DiffEditorViewManager | null;

  public static getInstance(): DiffEditorViewManager {
    if (DiffEditorViewManager.instance == null) {
      DiffEditorViewManager.instance = new DiffEditorViewManager();
    }
    return DiffEditorViewManager.instance;
  }

  // uri -> diffContent
  // private fileChangeSet = new Map<string, string>();

  constructor() {
    super();

    const diffProvider = new DiffContentProvider();
    const providerRegistration =
      vscode.workspace.registerTextDocumentContentProvider(
        DiffEditorViewManager.DiffContentProviderId,
        diffProvider,
      );

    this.disposables.push(
      providerRegistration,
      vscode.commands.registerCommand(
        SrdCommand.CODE_DIFF_ACCEPT_ALL_CHANGES,
        async (uri: vscode.Uri, group: unknown) => {
          // console.error(`[secidea] ConfirmModify: ${uri.path} + ${group}`);
          const modifiedContent = Buffer.from(uri.query, 'base64');
          const fileUri = vscode.Uri.file(uri.path);

          let originalContent = '';
          try {
            const fileContent = await vscode.workspace.fs.readFile(fileUri);
            originalContent = Buffer.from(fileContent).toString('utf-8');
          } catch (error) {
            originalContent = '';
          }

          try {
            await vscode.workspace.fs.writeFile(fileUri, modifiedContent);
          } catch (error) {
            vscode.window.showErrorMessage(`Error writing file: ${error}`);
          }

          // this.fileChangeSet.delete(uri.toString());
          this._onDidChange.fire({
            type: 'accept',
            path: [fileUri.fsPath],
          });

          await vscode.commands.executeCommand(
            'workbench.action.closeActiveEditor',
          );

          // 代码采纳率数据上报
          const ModifiedText = modifiedContent.toString();
          const changesOnly = this.extractChanges(originalContent, ModifiedText);
          
          DataReportServiceInst.notifyUserActivity(ActivityType.COMPOSER_ACCEPTED_CODE, changesOnly);

          vscode.window.showInformationMessage(
            `[codefree] Diff: ${uri.path} 已写入修改内容`,
          );
        },
      ),

      vscode.commands.registerCommand(SrdCommand.CODE_DIFF_REVERT_ALL_CHANGES, (uri: vscode.Uri) => { 
        if (
          uri.scheme === DiffEditorViewManager.DiffContentProviderId
        ) {
          // this.fileChangeSet.delete(document.uri.toString());
          vscode.commands.executeCommand(
            'workbench.action.closeActiveEditor',
          );
        }
      }),

      vscode.workspace.onDidCloseTextDocument((document) => {
        if (
          document.uri.scheme === DiffEditorViewManager.DiffContentProviderId
        ) {
          // this.fileChangeSet.delete(document.uri.toString());
          this._onDidChange.fire({
            type: 'reject',
            path: [document.uri.fsPath],
          });
        }
      }),
    );
  }
  //   async writeDiffContentWithoutOpening(data: { path: string; content: string }): Promise<void> {
  //     let isNewFile = false;
  //     let originalContent = '';

  //     try {
  //       await fsPromise.access(data.path, fsPromise.constants.R_OK);
  //       // Read original content if file exists
  //       const fileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(data.path));
  //       originalContent = Buffer.from(fileContent).toString('utf-8');
  //     } catch (error) {
  //       isNewFile = true;
  //     }

  //     try {
  //     } catch (error) {
  //       vscode.window.showErrorMessage(`Error storing diff content: ${error}`);
  //     }

  //     this._onDidChange.fire({
  //       type: 'accept',
  //       path: [data.path],
  //     });
  // }
  async openDiffView(data: { path: string; content: string }): Promise<void> {
    let isNewFile = false;
    let originalContent = '';

    try {
      await fsPromise.access(data.path, fsPromise.constants.R_OK);
      // Read original content if file exists
      const fileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(data.path));
      originalContent = Buffer.from(fileContent).toString('utf-8');
    } catch (error) {
      isNewFile = true;
    }

    try {
      const originalUri = isNewFile
        ? vscode.Uri.parse(
          `${DiffEditorViewManager.DiffContentProviderId}:${data.path}`,
        ).with({
          query: Buffer.from('').toString('base64'),
        })
        : vscode.Uri.file(data.path);
      const modifiedUri = vscode.Uri.parse(
        `${DiffEditorViewManager.DiffContentProviderId}:${data.path}`,
      ).with({
        query: Buffer.from(data.content).toString('base64'),
      });

      const name = path.basename(data.path);

      // Store both original and modified content
      // this.fileChangeSet.set(vscode.Uri.file(data.path).toString(), data.content);


      await vscode.commands.executeCommand(
        'vscode.diff',
        originalUri,
        modifiedUri,
        `${name} ${isNewFile ? 'Created' : 'Modified'}`,
        {
          viewColumn: vscode.ViewColumn.Two,
          preview: false,
          renderSideBySide: false,
        },
      );
    } catch (error) {
      vscode.window.showErrorMessage(`Error opening diff: ${error}`);
    }

    this._onDidChange.fire({
      type: 'add',
      path: [data.path],
    });
  }

  private async closeAllDiffEditor(): Promise<void> {
    const tabGroups = vscode.window.tabGroups.all;

    for (const group of tabGroups) {
      const diffTabs = group.tabs.filter(
        (tab) =>
          tab.input instanceof vscode.TabInputTextDiff &&
          tab.input.modified.scheme ===
          DiffEditorViewManager.DiffContentProviderId,
      );

      if (diffTabs.length > 0) {
        await vscode.window.tabGroups.close(diffTabs);
      }
    }

    // this.fileChangeSet.clear();
  }

  async acceptAllFile(dataEntries: { path: string; content: string }[]): Promise<void> {
    for (const data of dataEntries) {
      try {
        await vscode.workspace.fs.writeFile(
          vscode.Uri.file(data.path), // Use vscode.Uri.file to parse the file path
          Buffer.from(data.content)
        );
        Logger.debug(`[diffEditor] Successfully wrote file: ${data.path}`);
      } catch (error) {
        Logger.error(`[diffEditor] Error writing file ${data.path}: ${error}`);
      }
    }
    await this.closeAllDiffEditor();
  }

  // async rejectAllFile(): Promise<void> {
  //   await this.closeAllDiffEditor();
  // }

  private async closeDiffEditor(path: string): Promise<void> {
    const tabGroups = vscode.window.tabGroups.all;
    const targetUri = vscode.Uri.file(path).toString();

    for (const group of tabGroups) {
      const diffTabs = group.tabs.filter(
        (tab) =>
          tab.input instanceof vscode.TabInputTextDiff &&
          tab.input.modified.scheme ===
          DiffEditorViewManager.DiffContentProviderId &&
          tab.input.modified.path === path,
      );

      if (diffTabs.length > 0) {
        await vscode.window.tabGroups.close(diffTabs);
      }
    }

    // this.fileChangeSet.delete(targetUri);
  }

  async acceptFile(data: { path: string; content: string }): Promise<void> {
    const uri = vscode.Uri.file(data.path);
    const content = data.content;

    if (content) {
      await vscode.workspace.fs.writeFile(uri, Buffer.from(content));
      this._onDidChange.fire({
        type: 'accept',
        path: [uri.fsPath],
      });
      // await this.closeDiffEditor(data.path);
    }
  }
  async undoAcceptFile(data: { path: string; content: string }): Promise<void> {
    const uri = vscode.Uri.file(data.path);
    const content = data.content;

    // 如果内容为空，删除文件
    if (content === '') {
        await fsPromise.access(data.path, fsPromise.constants.R_OK);
        await vscode.workspace.fs.delete(uri, { useTrash: false });
        this._onDidChange.fire({
          type: 'undo',
          path: [data.path],
        });
        return;
    }
    // 内容不为空，写入文件
    if (content) {
      await vscode.workspace.fs.writeFile(uri, Buffer.from(content));
      this._onDidChange.fire({
        type: 'undo',
        path: [uri.fsPath],
      });
      // await this.closeDiffEditor(data.path);
    }
  }

  // async rejectFile(path: string): Promise<void> {
  //   await this.closeDiffEditor(path);
  // }

  /**
   * 提取修改后内容中的变化部分
   * @param originalText 原始文本内容
   * @param modifiedText 修改后的文本内容
   * @returns 修改后内容中的变化部分
   */
  private extractChanges(originalText: string, modifiedText: string): string {
    // 如果完全相同，返回空字符串
    if (originalText === modifiedText) {
      return '';
    }

    // 使用diffLines来获取所有的差异
    const changes = diffLines(originalText, modifiedText);
    let diffContent = '';

    // 处理所有变化，包括新增和删除的内容
    for (const change of changes) {
      if (change.added || change.removed) {
        diffContent += change.value;
      }
    }

    return diffContent;
  }
}