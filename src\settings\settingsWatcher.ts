import * as vscode from 'vscode';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import { GENERAL_ADDRESS_SETTING, GENERAL_VERSION_SETTING } from './constants';
import { getUpdatedCheckSettings, setIsAddressAccessable } from './settingsGetter';
import { checkNewVersion, downloadNewVersion } from '../updater/updateServices';
import { testConnect } from '../login/loginUtils';
import { SrdCommand } from '../common/constants';

class SettingsWatcher {
  private intervalHandle: NodeJS.Timeout | null = null;

  private lastUpdateVersion: string | null = null;

  public constructor(protected context: vscode.ExtensionContext) {
    this.context = context;
  }

  public watch(): void {
    // ------------配置项初始化-----------
    this.initiateSettings();

    // ------------监听设置变更------------
    vscode.workspace.onDidChangeConfiguration(
      async (event: vscode.ConfigurationChangeEvent): Promise<void> => {
        const change = [GENERAL_ADDRESS_SETTING, GENERAL_VERSION_SETTING].find(config =>
          event.affectsConfiguration(config)
        );

        if (change) {
          try {
            await this.onChangeConfiguration(change);
          } catch (error) {
            /* empty */
          }
        }
      }
    );
  }

  /**
   * 初始化设置
   */
  private initiateSettings() {
    // 初始化插件更新包存放位置
    const updateCheckItem = getUpdatedCheckSettings();

    if (updateCheckItem.saveDir === '') {
      // 如果是默认的空值，则设置为用户目录下的.secidea/updates目录
      const defaultPath = this.getDefaultUpdatePackagePath();
      vscode.workspace
        .getConfiguration()
        .update(GENERAL_VERSION_SETTING, { 新版本插件安装包存放目录: defaultPath }, true);
    }

    if (updateCheckItem.isUpdateTip) {
      // 开启更新
      const interval = updateCheckItem.updateTipInterval * 3600 * 1000;
      this.startTimer(interval);
    }

    // 如果设置了自动下载，就直接下载。 如果只设置了更新提示，弹框提示下载
    this.autoDownloadNewVersionOrNotice();
  }

  private async onChangeConfiguration(key: string): Promise<void> {
    if (key === GENERAL_ADDRESS_SETTING) {
      vscode.commands.executeCommand(SrdCommand.SECIDEA_LOGOUT);
      if (!(await testConnect())) {
        vscode.window.showErrorMessage('当前网络连接不通,请检查地址格式');
        setIsAddressAccessable(false);
      } else {
        vscode.window.showInformationMessage('当前网络连接通畅!');
        setIsAddressAccessable(true);
      }
      return;
    } else if (key === GENERAL_VERSION_SETTING) {
      const updateCheckItem = getUpdatedCheckSettings();

      if (updateCheckItem.saveDir === '') {
        // 如果为空，则设置默认路径
        const defaultPath = this.getDefaultUpdatePackagePath();
        vscode.workspace
          .getConfiguration()
          .update(GENERAL_VERSION_SETTING, { 新版本插件安装包存放目录: defaultPath }, true);
      } else {
        const packageSaveDir = updateCheckItem.saveDir;
        // 判断路径是否存在，不存在则创建(同时检测路径是否是合法的路径，用path.isAbsolute判断)， 创建失败则提示用户输入正确的路径
        if (!fs.existsSync(packageSaveDir) && path.isAbsolute(packageSaveDir)) {
          try {
            fs.mkdirSync(packageSaveDir, { recursive: true });
          } catch (error) {
            vscode.window.showErrorMessage('请输入正确的路径(新版本插件安装包存放目录)');
          }
        } else if (!fs.existsSync(packageSaveDir)) {
          vscode.window.showErrorMessage('请输入正确的路径(新版本插件安装包存放目录)');
        }
      }

      if (updateCheckItem.isUpdateTip || updateCheckItem.updateTipInterval) {
        // 校验： 判断value[UPDATE_TIP_INTERVAL_HOUR]如果小于0或者是非整数，则提示用户输入正确的值然后将value[UPDATE_TIP_INTERVAL_HOUR]值改回为1
        const rawInterval = updateCheckItem.updateTipInterval;
        // 判断是否小于 0 或者不是整数
        if (rawInterval <= 0 || !Number.isInteger(rawInterval)) {
          const defaultUpdateInterval = 1;
          vscode.workspace
            .getConfiguration()
            .update(GENERAL_VERSION_SETTING, { 更新提醒间隔小时: defaultUpdateInterval }, true);
          vscode.window.showErrorMessage(
            '更新提醒间隔小时必须是一个大于0的整数，请输入正确的值！已重置为1'
          );
        }

        // 设置定时器
        if (updateCheckItem.isUpdateTip) {
          const interval = updateCheckItem.updateTipInterval * 3600 * 1000;
          // const interval = 10 * 1000; // todo 临时测试10秒间隔
          this.startTimer(interval);
        }
      }

      if (!updateCheckItem.isUpdateTip) {
        this.stopTimer();
      }
    }
  }

  private async autoDownloadNewVersionOrNotice() {
    // 如果设置了自动下载，就直接下载。 如果只设置了更新提示，弹框提示下载
    const updateCheckSettings = getUpdatedCheckSettings();

    if (updateCheckSettings.isAutoDownload) {
      const currentVersion = this.getCurrentVersion();
      const response = await checkNewVersion(currentVersion);
      if (response.code !== 0) {
        return;
      }
      if (response.hasUpdate) {
        const isDownloadSuccessfully = await downloadNewVersion(
          updateCheckSettings.saveDir,
          currentVersion
        );
        if (isDownloadSuccessfully) {
          vscode.window.showInformationMessage(
            `新版本插件安装包已自动下载到：${updateCheckSettings.saveDir}，可手动进行安装`
          );
        }
      }
    } else if (updateCheckSettings.isUpdateTip) {
      const currentVersion = this.getCurrentVersion();

      const selected = await vscode.window.showInformationMessage(
        `检测到新版本海云智码，是否立即下载新版本安装包？`,
        '是',
        '否'
      );
      if (selected === '是') {
        vscode.window.showInformationMessage('正在下载新版本安装包，请稍等...');
        const isDownloadSuccessfully = await downloadNewVersion(
          updateCheckSettings.saveDir,
          currentVersion
        );
        if (isDownloadSuccessfully) {
          vscode.window.showInformationMessage(
            `新版本插件安装包已下载到：${updateCheckSettings.saveDir}，请手动进行安装`
          );
        } else {
          vscode.window.showWarningMessage('下载新版本失败，请检查相关配置项并重试');
        }
      }
    }
  }

  // interval单位是ms
  private startTimer(interval: number) {
    if (this.intervalHandle) {
      clearInterval(this.intervalHandle);
    }

    this.intervalHandle = setInterval(async () => {
      let currentVersion = this.getCurrentVersion();

      if (this.lastUpdateVersion) {
        // 下载过，但是没有安装，也不会再下载
        currentVersion = this.lastUpdateVersion;
      }

      const response = await checkNewVersion(currentVersion);
      if (response.code !== 0) {
        return;
      }

      if (response.hasUpdate) {
        const updateCheckSettings = getUpdatedCheckSettings();

        // 显示更新提示对话框
        const selected = await vscode.window.showInformationMessage(
          `检测到新版本海云智码，是否立即下载新版本安装包？`,
          '是',
          '否'
        );
        if (selected === '是') {
          vscode.window.showInformationMessage('正在下载新版本安装包');
          const isDownloadSuccessfully = await downloadNewVersion(
            updateCheckSettings.saveDir,
            currentVersion
          );
          if (isDownloadSuccessfully) {
            this.lastUpdateVersion = response.latestVersion;
            vscode.window.showInformationMessage('新版本安装包下载成功');
          } else {
            vscode.window.showWarningMessage('下载新版本失败，请检查相关配置项并重试');
          }
        }
      }
    }, interval);
  }

  private stopTimer() {
    if (this.intervalHandle) {
      clearInterval(this.intervalHandle);
      this.intervalHandle = null;
    }
  }

  private getDefaultUpdatePackagePath(): string {
    const updateDirPath = path.join(os.homedir(), '.secidea', 'updates');
    if (!fs.existsSync(updateDirPath)) {
      fs.mkdirSync(updateDirPath, { recursive: true });
    }
    return updateDirPath;
  }

  private getCurrentVersion(): string {
    const packageJsonPath = this.context.asAbsolutePath(path.join('package.json'));
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath).toString());
    return packageJson.version;
  }
}

export default SettingsWatcher;
