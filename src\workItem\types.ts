import { CodeAIResponsePayload } from '../service/types/codeAI';

export interface WebViewRequest {
  command: string;
  data: unknown;
}

export interface WebViewResponse {
  command: string;
  type?: string;
  data?: unknown;
}


export interface WorkItem {
  id: string;
  workItemKey: string;
  description: string;
  title: string;
  creatorLoginName: string;
  creatorDisplayName: string;
  assigneeLoginName: string;
  assigneeDisplayName: string;
  workitemTypeKey: string;
  workitemTypeName: string;
  statusKey: string;
  statusName: string;
  statusGroupKey: string;
  url?: string;
}

export enum WorkItemEventType {
  SEARCH_WORKITEMS = 'searchworkitems',
  SELECT_WORKITEM = 'selectworkitem',
  VIEW_WORKITEM_DETAIL = 'viewworkitemdetail',
  COMMIT_ANALYSIS = 'commitanalysis',
  ANSWER_RECVED = 'answerrecved',
}

export enum WebViewReqCommand {
  WORKITEM_REQUEST = 'workitem-request',
  WEBVIEW_LOADED = 'webview-loaded'
}

export enum WebViewRspCommand {
  WORKITEM_RESPONSE = 'workitem-response',
  PUSH_THEME_CHANGED = 'push-theme-changed'
}

export type ChangeItem = string;

export interface ICommitMessageHandler {
  onAnswer(reqId: string, isEnd: number, answer: string, payload?: CodeAIResponsePayload): void;
  onTaskError(reqId: string, rtnCode: number, errMsg?: string): void;
}