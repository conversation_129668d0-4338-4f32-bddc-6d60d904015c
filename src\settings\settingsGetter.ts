import * as vscode from 'vscode';
import {
  COMPLETION_MODE_SETTING,
  GENERAL_ADDRESS_SETTING,
  GENERAL_IDENTIFIER,
  GENERAL_VERSION_SETTING,
  SECRET_KEY_SETTING,
} from './constants';

// -----------基础方法和接口-----------
export function getConfigName(setting: string): string {
  return setting.split(`.`)[1]!;
}

export interface UpdatedCheckSettings {
  saveDir: string;
  isAutoDownload: boolean;
  isUpdateTip: boolean;
  updateTipInterval: number;
}

// ------------获取设置参数------------
export function getAddress(): string {
  return vscode.workspace
    .getConfiguration(GENERAL_IDENTIFIER)
    .get(getConfigName(GENERAL_ADDRESS_SETTING)) as string;
}

let isAddressAccessable = false;

export function setIsAddressAccessable(value: boolean): void {
  isAddressAccessable = value;
}

export function getIsAddressAccessable(): boolean {
  return isAddressAccessable;
}

export function getUpdatedCheckSettings(): UpdatedCheckSettings {
  const value = vscode.workspace
    .getConfiguration(GENERAL_IDENTIFIER)
    .get(getConfigName(GENERAL_VERSION_SETTING)) as any;
  return {
    saveDir: value.新版本插件安装包存放目录,
    isAutoDownload: value.是否自动下载新版本,
    isUpdateTip: value.是否开启更新提醒,
    updateTipInterval: value.更新提醒间隔小时,
  } as UpdatedCheckSettings;
}

export function getCompletionMode(): string {
  return vscode.workspace
    .getConfiguration(GENERAL_IDENTIFIER)
    .get(getConfigName(COMPLETION_MODE_SETTING)) as string;
}

export function getSecretKey(): string {
  return vscode.workspace
    .getConfiguration(GENERAL_IDENTIFIER)
    .get(getConfigName(SECRET_KEY_SETTING)) as string;
}
