export function flattenNestedArrayBFS(arr) {
  const result = [];
  const queue = [...arr.map(item => ({ ...item }))];

  while (queue.length > 0) {
    const current = queue.shift();
    const { children, ...rest } = current;

    // 只添加包含 path 属性的对象
    if (rest.hasOwnProperty('path') && !children) {
      result.push(rest);
    }

    if (children && children.length > 0) {
      queue.push(...children.map(item => ({ ...item })));
    }
  }

  return result;
}
export function isElementVisible(element, container, offset = 20) {
  const elementTop = element.offsetTop;          // 元素顶部到容器顶部的距离
  const elementBottom = elementTop + element.offsetHeight;

  const containerScrollTop = container.scrollTop; // 当前滚动位置
  const containerHeight = container.clientHeight; // 容器可视高度

  // 判断是否在可视区域（考虑上下偏移）
  const isVisible = (
    elementTop >= containerScrollTop + offset &&
    elementBottom <= containerScrollTop + containerHeight - offset
  );

  return isVisible;
}

export function calculateScrollPosition(element, container, offset = 20) {
  const elementTop = element.offsetTop;
  const containerHeight = container.clientHeight;

  // 向上滚动：保持元素在顶部下方 offset 处
  if (elementTop < container.scrollTop + offset) {
    return Math.max(0, elementTop - offset);
  }
  // 向下滚动：保持元素在底部上方 offset 处
  else {
    return elementTop + element.offsetHeight - containerHeight + offset;
  }
}

export function ensureFullVisibility(element, container, margin = 5) {
  const elemTop = element.offsetTop;
  const elemBottom = elemTop + element.offsetHeight;
  const containerTop = container.scrollTop;
  const containerBottom = containerTop + container.clientHeight;

  // 判断是否需要滚动（严格模式）
  const shouldScrollUp = elemTop < containerTop + margin;
  const shouldScrollDown = elemBottom > containerBottom - margin;

  if (shouldScrollUp || shouldScrollDown) {
    // 计算目标位置（确保完整显示）
    const targetScroll = shouldScrollUp
      ? elemTop - margin  // 向上滚动：顶部留白
      : elemBottom - container.clientHeight + margin; // 向下滚动：底部留白

    container.scrollTo({
      top: targetScroll,
      behavior: 'smooth'
    });
  }
}

export const getTextWithNewlines = (element) => {
  const clone = element.cloneNode(true);
  // 将 <br> 替换为换行符
  clone.querySelectorAll('br').forEach(br => {
    br.replaceWith('\n');
  });
  // 获取纯文本
  return clone.textContent;
};
