import { Command, InlineCompletionItem, Range } from 'vscode';
import { ResultEntry } from '../types';

export default class SrdInlineCompletionItem extends InlineCompletionItem {
  public suggestionEntry: ResultEntry;

  public constructor(text: string, suggestionEntry: ResultEntry, range?: Range, command?: Command) {
    super(text, range, command);

    this.suggestionEntry = suggestionEntry;
  }
}
