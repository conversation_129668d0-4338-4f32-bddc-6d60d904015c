import { AgentHistoryUtil } from "./AgentHistoryUtil";
import { ComposerHistoryEventType } from "./types";
import { ComposerHistoryParams } from "../../codechat/types";
import { getProjectInfo } from "../../utils/textEditor";
import { window } from "rxjs";
import { IComposerServiceHandler } from "../../codechat/types";

export class ComposerHistoryHandler {
  private agentHistoryUtil = AgentHistoryUtil.getInstance();
  private actionMap = new Map<ComposerHistoryEventType, (params: any) => void>();

  constructor(private handler: IComposerServiceHandler) {
    this.registerHandlers();
  }

  private registerHandlers(): void {
    this.actionMap.set(
      ComposerHistoryEventType.LOAD_HISTORY,
      (params) => {
        const project = getProjectInfo();
        if (!project) {
          return [];
        }
        const historyList = this.agentHistoryUtil.getHistoryByConversationId({ ...project, windowId: params.dialogId }, params.dialogId);
        this.handler.onComposerHistoryResponse({reqType: params.reqType, historyList});
      }
    );

    this.actionMap.set(
      ComposerHistoryEventType.DELETE_HISTORY,
      (params) => { 
        const result = this.agentHistoryUtil.clearWindowIdHistory(params.dialogId);
        if (result) {
          this.handler.onComposerHistoryResponse({reqType: params.reqType });
        } else {
          this.handler.onComposerHistoryResponse({reqType: params.reqType, error: '清理历史记录失败'});
        }
      }
    );
  }

  public handleComposerHistoryRequest(request: ComposerHistoryParams): void {
    const handler = this.actionMap.get(request.reqType as ComposerHistoryEventType);
    if (handler) {
      handler(request);
    }
  }
}