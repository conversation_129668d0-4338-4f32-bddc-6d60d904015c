import { HttpStatusCode, MessageName, RtnCode } from '../common/constants';
import { Logger } from '../utils/logger';
import { CodeAIResponseMessage, IAnswerHandler, ICodeAIReceiverResult, ICodeAIDataReportReceiveHandler } from './types/codeAI';

export default class CodeAIRequestReceiver {
  private static receiverResult: ICodeAIReceiverResult | undefined;

  private static dataReportReceiveHandler: ICodeAIDataReportReceiveHandler | undefined;

  private static answerHandlers: Map<string, IAnswerHandler> = new Map();

  public static setReceiverResult(result: ICodeAIReceiverResult) {
    this.receiverResult = result;
  }

  public static setDataReportReceiveHandler(handler: ICodeAIDataReportReceiveHandler) { 
    this.dataReportReceiveHandler = handler;
  }

  public static addAnswerHandler(handler: IAnswerHandler) {
    this.answerHandlers.set(handler.getReqId(), handler);
  }

  public static deleteAnswerHandler(reqId: string) {
    this.answerHandlers.delete(reqId);
  }

  public static getAnswerHandler(reqId: string) {
    return this.answerHandlers.get(reqId);
  }


  /**
   * 接收通道正常消息处理
   * @param message
   */
  public static onReceiveMessageFromCodeAI(message: CodeAIResponseMessage) {
    const optResult = message.context.optResult;
    const payload = message.payload;

    switch (message.messageName) {
      case MessageName.REGISTER_CHANNEL_RESP:
        this.receiverResult?.onRegisterChannelResult(optResult);
        break;
      case MessageName.GET_USER_API_KEY_RESP: {
        let code = optResult;
        if (optResult === RtnCode.SUCCESS || payload) {
          code = payload?.retCode || optResult;
        }

        Logger.debug(`[CodeAIRequestReceiver] GetUserApiKeyResp, ${JSON.stringify(message)}`);
        this.receiverResult?.onGetApiKeyResult(code, message.payload);
        break;
      }
      case MessageName.USER_ACTIVITY_NOTIFY_RESP:
        this.dataReportReceiveHandler?.onUserActivityNotify(optResult);
      case MessageName.CODE_GEN_RESP:
      case MessageName.CODE_CHAT_RESP:
      case MessageName.COMMIT_CHAT_RESP:{
        let rtnCode = payload?.retCode !== undefined ? payload.retCode : RtnCode.SEND_ERROR;
        const isEnd = payload?.isEnd || 0;
        const answer = payload?.answer || '';
        const errMsg = payload?.errMsg || '';
        const inValid = payload?.inValid || false;

        Logger.info(
          `[CodeAIRequestReceiver] onReceiveMessageFromCodeAI, reqId: ${message.context.reqId}, retCode:${payload?.retCode}, answerLen: ${answer.length}`
        );

        // optResult=401, 101时, 等同于rtnCode=11时一样处理，需要提示过期
        if (optResult === HttpStatusCode.AUTH_FAIL || optResult === HttpStatusCode.UNAUTHORIZED) {
          rtnCode = RtnCode.INVALID_SESSION_ID;
        }

        if (rtnCode === RtnCode.INVALID_SESSION_ID) {
          this.receiverResult?.onTaskErrorOrClose(false, rtnCode);
        }

        const answerHandler = this.getAnswerHandler(message.context.reqId);
        if (answerHandler) {
          answerHandler.onAnswer(message.context.reqId, rtnCode, payload);
        } 

        break;
      }
      default:
        break;
    }
  }

  /**
   * 接收通道异常处理
   * @param isTerminate
   * @param rtnCode
   */
  public static onTaskErrorOrClose(isTerminate: boolean, rtnCode: RtnCode) {
    this.receiverResult?.onTaskErrorOrClose(isTerminate, rtnCode);
  }

  public static onWSReconnect(isTerminate: boolean, rtnCode: RtnCode) {
    this.receiverResult?.onWSReconnect(isTerminate, rtnCode);
  }
}
