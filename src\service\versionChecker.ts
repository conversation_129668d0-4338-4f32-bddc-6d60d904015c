import HttpClient from '../commclient/httpClient';
import { HttpError } from '../commclient/types/http';
import { LATEST_VERSION_PATH, COMPOSQ_COMPONENT_ID } from '../common/config';
import { RtnCode } from '../common/constants';
import { getExtensionVersion } from '../common/globalContext';
import { Logger } from '../utils/logger';
import { ComponentDetail, UpgradeVerResult } from './types/versionChecker';

/**
 * 登录成功后检测是否有最新插件版本
 */
export default class VersionChecker {
  private httpClient: HttpClient = new HttpClient();

  /**
   * 校验是否有新版本发布
   */
  public async checkIfUpgradeVersion(clientLatestVersion = ''): Promise<UpgradeVerResult> {
    let latestVersion = clientLatestVersion;

    if (!latestVersion) {
      latestVersion = await this.getLatestPublishedVersion();
    }

    const currentVersion = getExtensionVersion();
    Logger.debug(
      `[VersionChecker] checkIfUpgradeVersion, latest:${latestVersion}, current: ${currentVersion}`
    );

    if (this.compareVersion(currentVersion, latestVersion) < 0) {
      return {
        hasNewVersion: true,
        latestVersion: latestVersion,
      };
    }

    return {
      hasNewVersion: false,
    };
  }

  /**
   * 从组件广场接口获取最新发布版本
   */
  private async getLatestPublishedVersion(): Promise<string> {
    const data = await this.httpClient.requestByGet<ComponentDetail>(
      LATEST_VERSION_PATH,
      {
        componentId: COMPOSQ_COMPONENT_ID,
      },
      {
        headers: {
          contentType: 'application/json',
        },
      }
    );

    if (!data || (data instanceof HttpError && (data as HttpError).code !== RtnCode.SUCCESS)) {
      Logger.error(
        `[VersionChecker] getLatestPublishedVersion failed, ${(data as HttpError).message}`
      );
      return '';
    }

    return data.componentDTO?.latestVersion || '';
  }

  /**
   * 比较版本
   */
  private compareVersion(origVer: string, targetVer: string): number {
    if (!origVer && targetVer) {
      return -1;
    }

    if (!targetVer) {
      return 1;
    }

    // version的格式可能是：v1.2.1-beta, v1.2.1, 1.2.1
    if (origVer && targetVer) {
      let mOrigVer = origVer,
        mTargetVer = targetVer;

      // 1) 截取版本数字
      if (origVer.toLowerCase().startsWith('v')) {
        mOrigVer = origVer.slice(1);
      }

      if (targetVer.toLowerCase().startsWith('v')) {
        mTargetVer = targetVer.slice(1);
      }

      const mOrigVerSplitArr = mOrigVer.split('-');
      const mTargetVerSplitArr = mTargetVer.split('-');

      mOrigVer = mOrigVerSplitArr?.[0] || mOrigVer;
      mTargetVer = mTargetVerSplitArr?.[0] || mTargetVer;

      const subOrigVer = mOrigVerSplitArr?.[1] || '';
      const subTargetVer = mTargetVerSplitArr?.[1] || '';

      // 2) 按版本位数字大小依次判断
      const mOrigVerArr = mOrigVer.split('.');
      const mTargetVerArr = mTargetVer.split('.');
      const len = Math.max(mOrigVerArr.length, mTargetVerArr.length);

      for (let i = 0; i < len; i++) {
        const origNum = i < mOrigVerArr.length ? parseInt(mOrigVerArr[i]) : 0;
        const targetNum = i < mTargetVerArr.length ? parseInt(mTargetVerArr[i]) : 0;

        if (origNum < targetNum) {
          return -1;
        } else if (origNum > targetNum) {
          return 1;
        }
      }

      if (subOrigVer < subTargetVer) {
        return -1;
      } else if (subOrigVer > subTargetVer) {
        return 1;
      }
    }

    return 0;
  }
}
