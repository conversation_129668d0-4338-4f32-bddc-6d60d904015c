import { LanguageConfig } from './type';
import { AgentManager } from '../agent/agentmanager';
import { Logger } from '../utils/logger';

export const languageDisableMap: Map<string, boolean> = new Map();

export function isExtensionDisabled(extension: string): boolean {
  return languageDisableMap.get(extension) ?? false;
}
export function generateWeakUniqueNumber(): number {
  // 获取当前时间的毫秒时间戳
  const timestamp = Date.now();
  // 生成一个0到1000之间的随机数（不含1000）
  const randomPart = Math.floor(Math.random() * 1000);
  // 将时间戳和随机数组合起来得到一个相对唯一的数字
  const uniqueNumber = timestamp * 1000 + randomPart; // 乘以1000是为了与时间戳区分，避免因随机数太小导致的直接相加可能产生的重复
  return uniqueNumber;
}

export async function updateClientPropertiesEndpoint(endpoint: string) {
  const updateConfigRequest = [
    generateWeakUniqueNumber(),
    {
      func: 'updateConfig',
      args: ['server.endpoint', endpoint],
    },
  ];

  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
  if (tabbyClient) {
    tabbyClient.request('updateConfig', ['server.endpoint', endpoint], null, (data: any) => {
      Logger.debug(`updateClientPropertiesEndpoint response: ${JSON.stringify(data)}`);
    });
  } else {
    Logger.warn('updateClientPropertiesEndpoint: tabbyClient not available');
  }

  console.debug('update endpoint');
}
export async function clearClientPropertiesEndpoint() {
  const clearConfigRequest = [
    generateWeakUniqueNumber(),
    {
      func: 'clearConfig',
      args: ['server.endpoint'],
    },
  ];

  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
  if (tabbyClient) {
    tabbyClient.request('clearConfig', ['server.endpoint'], null, (data: any) => {
      Logger.debug(`clearClientPropertiesEndpoint response: ${JSON.stringify(data)}`);
    });
  } else {
    Logger.warn('clearClientPropertiesEndpoint: tabbyClient not available');
  }

  console.debug('clear endpoint');
}

export async function updateClientPropertiesTriggerMode(mode: string) {
  const id = generateWeakUniqueNumber();
  const request = [
    id,
    {
      func: 'updateClientProperties',
      args: ['user', 'vscode.triggerMode', mode],
    },
  ];

  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
  if (tabbyClient) {
    tabbyClient.request('updateClientProperties', ['user', 'vscode.triggerMode', mode], null, (data: any) => {
      Logger.debug(`updateClientPropertiesTriggerMode response: ${JSON.stringify(data)}`);
    });
  } else {
    Logger.warn('updateClientPropertiesTriggerMode: tabbyClient not available');
  }

  console.debug('update triggermode');
}

export async function handleDisableLanguageChange(newConfig: LanguageConfig[]): Promise<void> {
  languageDisableMap.clear();

  if (Array.isArray(newConfig)) {
    newConfig.forEach(item => {
      languageDisableMap.set(item.尾缀名, item.是否禁用);
    });
  }
  console.debug(
    'update languageDisableMap: ' + JSON.stringify(Array.from(languageDisableMap.entries()))
  );
}
export async function updateClientPropertiesCplMode(mode: string) {
  const updateCplModeRequest = [
    generateWeakUniqueNumber(),
    {
      func: 'updateConfig',
      args: ['server.cplMode', mode],
    },
  ];

  const tabbyClient = AgentManager.getInstance().getAgentCommClient('tabby');
  if (tabbyClient) {
    tabbyClient.request('updateConfig', ['server.cplMode', mode], null, (data: any) => {
      Logger.debug(`updateClientPropertiesCplMode response: ${JSON.stringify(data)}`);
    });
  } else {
    Logger.warn('updateClientPropertiesCplMode: tabbyClient not available');
  }

  console.debug('update cplMode');
}
