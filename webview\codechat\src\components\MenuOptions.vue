<template>
  <div v-if="answering" class="menu-options-container">
    <div class="answering-box flex-center">
      <span class="stop-btn text-btn" @click="stopMessage">
        <svg-icon name="stop" size="14px" style="margin-right: 3px"></svg-icon>
        停止回答
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/store/chat';
import { getCurrentInstance } from 'vue';

const props = defineProps<{
  answering: boolean;
}>();
const chatStore = useChatStore();

const app = getCurrentInstance();


const $EventBus = app?.appContext.config.globalProperties.$EventBus;

/** 停止回答 */
function stopMessage() {
  chatStore.handleCancelChatRequest();
  $EventBus.emit('onStopAnswer');
  chatStore.endBy = 'stopBtn'
}

</script>

<style lang="scss">
.menu-options-container {
  height: 32px;
  margin-bottom: -12px;
  .answering-box {
    height: 100%;
  }
  .disabled-stop-btn {
    cursor: not-allowed;
  }
  .stop-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .menu-options {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__left {
      white-space: nowrap;

      .text-btn:not(:last-child) {
        margin-right: 10px;
      }
    }

    &__right {
      display: flex;
      .icon-btn {
        margin-left: 10px;
      }
    }
  }
}
</style>
