import * as vscode from 'vscode';
import { WebViewReqCommand, WebViewRequest, WebViewResponse, WorkItemEventType, WebViewRspCommand } from './types';
import { WorkItemManager } from './workItemManager';
import * as fs from 'fs';

export class WorkItemViewProvider implements vscode.WebviewViewProvider, vscode.Disposable {
  private view?: vscode.WebviewView;
  private workItemManager: WorkItemManager;
  private cachedWebViewRspList: WebViewResponse[] = [];
  private disposable: vscode.Disposable | undefined;

  constructor(private context: vscode.ExtensionContext) { 
    this.workItemManager = new WorkItemManager(this);
  }

  /**
   * Get the work item manager instance
   */
  public getWorkItemManager(): WorkItemManager {
    return this.workItemManager;
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    this.view = webviewView;
    this.view.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        vscode.Uri.joinPath(this.context.extensionUri, 'media')
      ]
    };

    this.view.webview.html = this.getHtml();

    // 监听前端消息
    this.view.webview.onDidReceiveMessage((data: WebViewRequest) => { 
      this.didReceiveMessage(data);
    });
  }

  public initDisposable() {
    this.disposable = vscode.Disposable.from(this.registerThemeChange());
  }

  public dispose() {
    this.disposable?.dispose();
  }

  private registerThemeChange() { 
    return vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration('workbench.colorTheme')) {
        const changedTheme = vscode.workspace.getConfiguration().get('workbench.colorTheme');
        this.handleThemeChange(changedTheme);
      }
    });
  }

  private handleThemeChange(changedTheme: unknown) {
    const response = {
      command: WebViewRspCommand.PUSH_THEME_CHANGED,
      data: { theme: changedTheme },
    };
    this.pushMessage2WebView(response);
  }

  private didReceiveMessage(req: WebViewRequest) {
    switch (req.command) {
      case WebViewReqCommand.WORKITEM_REQUEST:
        this.handleWorkItemRequest(req.data);
        break;
      case WebViewReqCommand.WEBVIEW_LOADED:
        this.handleWebViewLoaded();
        break;
    }
  }

  private handleWorkItemRequest(request: any) {
    switch (request.reqType) {
      case WorkItemEventType.SEARCH_WORKITEMS:
        this.workItemManager.searchWorkItems(request.searchParam);
        break;
      case WorkItemEventType.SELECT_WORKITEM:
        this.workItemManager.selectWorkItem(request.workItemList);
        break;
      case WorkItemEventType.VIEW_WORKITEM_DETAIL:
        this.workItemManager.viewWorkItemDetail(request.workItemURL);
        break;
    }
  }
  
  private handleWebViewLoaded() {
    const theme = vscode.workspace.getConfiguration().get('workbench.colorTheme');
    const response = {
      command: WebViewRspCommand.PUSH_THEME_CHANGED,
      data: { theme },
    };
    this.pushMessage2WebView(response);
    this.sendCachedMessageToWebView();
  }

  private sendCachedMessageToWebView() {
    while (this.cachedWebViewRspList.length > 0) {
      const rsp = this.cachedWebViewRspList.shift();

      if (rsp) {
        this.sendMessageToWebView(rsp);
      }
    }
  }

  public handleWorkItemEvent(response: WebViewResponse) {
    switch (response.command) {
      case WebViewRspCommand.WORKITEM_RESPONSE:
        this.pushMessage2WebView(response);
        break;
    }
  }

  private pushMessage2WebView(response: WebViewResponse) {
    if (this.view?.webview) {
      this.sendMessageToWebView(response);
    } else {
      this.cachedWebViewRspList = this.cachedWebViewRspList.filter(
        c => c.command !== response.command
      );
      this.cachedWebViewRspList.push(response);
    }
  }

  private sendMessageToWebView(response: WebViewResponse) {
    this.view?.webview.postMessage(response);
  }

  private getHtml(): string {
    if (!this.view) {
      return '';
    }
    
    const styleUri = this.view.webview.asWebviewUri(
      vscode.Uri.joinPath(this.context.extensionUri, 'media', 'workItem', 'styles.css')
    );
    
    const scriptUri = this.view.webview.asWebviewUri(
      vscode.Uri.joinPath(this.context.extensionUri, 'media', 'workItem', 'script.js')
    );

    const htmlPath = vscode.Uri.joinPath(this.context.extensionUri, 'media', 'workItem', 'index.html');
    let htmlContent = fs.readFileSync(htmlPath.fsPath, 'utf-8');
    
    // Replace placeholders in the template
    htmlContent = htmlContent
      .replace('/styles.css', styleUri.toString())
      .replace('/script.js', scriptUri.toString());

    return htmlContent;
  }
}