<template>
  <div class="conversation-panel-container">
    <div class="nav-header">
      <span class="nav-header__left" @click="chatStore.toggleConversation">
        <svg-icon name="back-arrow" size="14px" style="margin-right: 8px;"></svg-icon>
        历史对话
      </span>
      <span class="nav-header__right">
        <Toolbar @createNewConv="emit('createNewConv')" />
      </span>
    </div>
    <div class="nav-search-input">
      <!-- <svg-icon name="search" class="search-icon" size="13px"></svg-icon> -->
      <svg-icon name="search" class="close-icon" size="13px" @click="() => {
        onEnter()
      }"></svg-icon>
      <input v-model="keyword" class="search-input" @keydown.prevent.enter="onEnter" placeholder="搜索历史对话" />
    </div>
    <div class="conversations">
      <template v-if="chatStore.historyOnline">
        <div v-for="conv in conversations" class="conversation-item" :class="{
          'active-conversation': selectedCurrentConv.id === conv.id,
        }" :key="conv.id" @click="selectConversation(conv)">
          <a-tooltip :content="conv.title" position="bl" mini :arrow-style="{ display: 'none' }">
            <div class="conv-title-text">
              {{ conv.title?.trim() || '无标题' }}
            </div>
            <div class="conv-time-text">
              {{ conv.updateTime || conv.createTime }}
            </div>
          </a-tooltip>

          <div class="conv-icon-box" @click.stop>
            <a-tooltip content="编辑会话" position="bl" mini :arrow-style="{ display: 'none' }">
              <div class="conv-icon-box__content" @click.stop.prevent="editConv(conv)">
                <svg-icon name="icon-edit" size="14px"></svg-icon>
              </div>
            </a-tooltip>
            <a-tooltip content="删除会话" position="bl" mini :arrow-style="{ display: 'none' }">
              <div class="conv-icon-box__content" @click.stop.prevent="deleteConv(conv)">
                <svg-icon name="trash-icon" size="14px"></svg-icon>
              </div>
            </a-tooltip>
          </div>
        </div>
      </template>
      <div v-else style="text-align: center;margin-top: 40px;">
        请检查网络
      </div>
    </div>
  </div>
  <a-modal v-model:visible="delVisible" width="300px" @ok="handleDelOk">
    <template #title>提示</template>
    <div>确定删除该会话?</div>
  </a-modal>
  <a-modal v-model:visible="editTitleVisible" title="编辑标题" width="350px" :ok-button-props="{ disabled: !editingConv.title }"
    @ok="handleEditTitleOk">
    <div style="display: flex">
      <span style="display: inline-block; width: 100px; line-height: 30px">会话标题：</span>
      <a-input v-model="editingConv.title" placeholder="请输入会话标题" allow-clear></a-input>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { WebViewReqCommand } from '@/constants/common';
import { useChatStore } from '@/store/chat';
import { Conv } from '@/types/index';
import { computed, reactive, ref } from 'vue';
import * as sender from '@/MessageSender'
import Toolbar from './Toolbar.vue';

const emit = defineEmits(['selectConv', 'createNewConv']);

const chatStore = useChatStore();
const selectedCurrentConv = computed(() => chatStore.currentConv);
const keyword = ref(''); // 搜索词

const conversations = computed(() => {
  return chatStore.conversations as any[]
})

function selectConversation(conv: Conv) {
  chatStore.handleCancelChatRequest()
  chatStore.helpNotes = ''
  setTimeout(() => {
    chatStore.handleSetCurrentConv(conv);
    emit('selectConv', conv);
    console.log('selectConversation', conv)
    chatStore.toggleConversation()
  }, 300)

}

function onEnter() {
  const data: any = {
    pageNum: 1,
    isFromSearch: true,
  }
  if (keyword.value) {
    data.title = keyword.value
  }
  sender.postMessage({
    command: WebViewReqCommand.CONVERSATION_REFRESH,
    data
  });
}

const delVisible = ref(false);
function deleteConv(conv: any) {
  editingConv.id = conv.id
  delVisible.value = true;
}
function handleDelOk() {
  delVisible.value = false;
  chatStore.handleDeleteConv(editingConv.id || '');
}

const editingConv = reactive({
  id: '',
  title: ''
});
const editTitleVisible = ref(false);
function editConv(conv: any) {
  editingConv.id = conv.id
  editingConv.title = conv.title || '';
  editTitleVisible.value = true;
}
function handleEditTitleOk() {
  editTitleVisible.value = false;
  chatStore.handleEditConv(editingConv.id || '', editingConv.title);
  chatStore.updateConvTitle(editingConv.id, editingConv.title)
}

defineExpose({
  selectConversation,
});
</script>
<style lang="scss">
.conversation-panel-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .nav-header {
    padding: 5px;
    display: flex;
    padding: 0 20px;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    border-bottom: 1px solid var(--vscode-panel-border);


    &__left {
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }

    &__right {}


    .nav-header__left {
      cursor: pointer;
      color: var(--vscode-input-foreground);

      &:hover {
        background-color: var(--vscode-toolbar-hoverBackground);
        outline: 4px solid var(--vscode-toolbar-hoverBackground);
        border-radius: 2px;
      }
    }
  }

  .nav-search-input {
    position: relative;
    margin: 20px;
    box-sizing: border-box;
    height: 36px;
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border, transparent);

    .search-input {
      height: 100%;
      width: 100%;
      border: 1px solid var(--vscode-input-border, transparent);
      color: var(--vscode-inputOption-activeForeground);
      background-color: var(--vscode-input-background);
      padding-left: 20px;
      padding-right: 34px;
      resize: none;

      &:focus {
        outline: 1px solid var(--vscode-input-border, transparent);
      }
    }

    .search-icon {
      cursor: pointer;
      position: absolute;
      left: 13px;
      top: calc(50% - 6.5px);
      color: var(--vscode-inputOption-activeForeground);
    }

    .close-icon {
      cursor: pointer;
      position: absolute;
      right: 13px;
      top: calc(50% - 6.5px);
      color: var(--vscode-inputOption-activeForeground);
      opacity: 0.8;
    }
  }

  .conversations {

    padding: 0 20px;
    flex: 1;
    overflow: auto;

  }

  .conversation-item {
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 8px;
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 10px 16px;
    border-radius: 4px;
    margin-bottom: 8px;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-panel-border);
    outline-offset: -1px;
    position: relative;


    &:hover {
      outline: 1px solid var(--vscode-textLink-foreground);
      outline-offset: -1px;
      .conv-icon-box {
        display: flex;
      }
    }

    .conv-icon-box {
      display: none;
      align-items: center;
      position: absolute;
      right: 16px;
      top: 8px;

      .conv-icon-box__content {
        cursor: pointer;
        margin-left: 8px;
        align-items: center;
        justify-content: center;
        display: inline-flex;
        width: 20px;
        height: 20px;
        color: var(--vscode-input-foreground);

        &:hover {
          background-color: var(--vscode-toolbar-hoverBackground);
          outline: 2px solid var(--vscode-toolbar-hoverBackground);
          border-radius: 3px;
        }
      }

    }
  }

  .active-conversation {
    // outline: 1px solid
    //   var(
    //     --vscode-list-focusAndSelectionOutline,
    //     var(--vscode-contrastActiveBorder, var(--vscode-list-focusOutline))
    //   );
    // outline-offset: -1px;
    color: var(--vscode-textLink-foreground);
    outline: 1px solid var(--vscode-textLink-foreground);
    // background-color: var(--vscode-list-activeSelectionBackground);
  }
}

.conv-title-text {
  text-overflow: ellipsis;
  word-wrap: normal;
  overflow: hidden;
  margin-bottom: 10px;
  width: calc(100% - 90px);
}

.conv-time-text {
  color: var(--vscode-input-placeholderForeground);
  font-size: 14px;
  
}
</style>
