export interface UploadScanFileResp {
  code: number | string;
  msg: string;
  taskId: string;
}

export interface StopScanResp {
  code: number | string;
  msg: string;
}

export interface UploadScanFileResult { 
  code: number | string;
  msg?: string;
  issueList?: Issue[];
}

export interface Issue { 
  issueId: string;
  rule: string;
  fileName: string;
  line: number;
  message: string;
  severity: number;
  detail: string;
}
