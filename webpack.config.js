'use strict';

const path = require('path');
const DotenvWebpack = require('dotenv-webpack');

const envPath = path.join(__dirname, `${process.env.NODE_ENV ? `./.env.${process.env.NODE_ENV}` : './.env'}`);
console.log('NODE_ENV', process.env.NODE_ENV);

const extensionConfig = {
  target: 'node', // VS Code extensions run in a Node.js-context 馃摉 -> https://webpack.js.org/configuration/node/
	mode: 'none', // this leaves the source code as close as possible to the original (when packaging we set this to 'production')

  entry: './src/extension.ts', // the entry point of this extension, 馃摉 -> https://webpack.js.org/configuration/entry-context/
  output: {
    // the bundle is stored in the 'dist' folder (check package.json), 馃摉 -> https://webpack.js.org/configuration/output/
    path: path.resolve(__dirname, 'dist'),
    filename: 'extension.js',
    libraryTarget: 'commonjs2',
  },
  externals: {
    vscode: 'commonjs vscode' // the vscode-module is created on-the-fly and must be excluded. Add other modules that cannot be webpack'ed, 馃摉 -> https://webpack.js.org/configuration/externals/
    // modules added here also need to be added in the .vscodeignore file
  },
  resolve: {
    // support reading TypeScript and JavaScript files, 馃摉 -> https://github.com/TypeStrong/ts-loader
    extensions: ['.ts', '.js'],
  },
  plugins: [
    new DotenvWebpack({
      path: envPath,
    })
  ],
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: [/node_modules/, /webview/],
        use: [
          {
            loader: 'ts-loader'
          }
        ]
      }
    ]
  },
  devtool: 'nosources-source-map',
  infrastructureLogging: {
    level: "log", // enables logging required for problem matchers
  },
};
module.exports = [ extensionConfig ];