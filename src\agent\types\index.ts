export interface AgentConfig {
  apiKey: string;
  invokerId: string;
  pluginType: 'codefree' | 'oscap';
  pluginVersion: string;
  clientType: string;
  clientVersion: string;
  serverType: 'codefree' | 'oscap';
  serverBaseUrl: string;
  embeddingSubservice: string;
  rerankSubservice: string;
  composerSubservice: string;
  // secidea-tabby-agent
  cplSubservice?: string;
  indexVersion?: string;
}

export interface AgentVersion {
  agentName: string;
  version: string;
  downloadUrl: string;
  md5: string;
}

export interface AgentVersionInfo {
  path: string;
  version: string;
}

export interface LocalAgentConfig {
  agent: {
    [agentName: string]: {
      [platform: string]: AgentVersionInfo[];
    };
  };
}

export const TABBY_AGENT = "tabby";
export const CORE_AGENT = "core";
