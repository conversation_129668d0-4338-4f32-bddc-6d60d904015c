export const EXCLUSION_RULES = {
  // 按目录名称排除（全字匹配）
  DIRECTORIES: new Set([
    // Version Control
    '.git', '.svn', '.hg', '.bzr', '.fossil', '.repo', '.cvs', '.cvsignore',

    // IDE and Editors
    '.vscode', '.idea', '.vs', '.eclipse', '.netbeans', '.kate-swp', '.komodotools', '.komodoproject',
    '.visualstudio', '.vscodium', '.webstorm', '.pycharm', '.rubymine', '.phpstorm', '.rider',
    '.goland', '.clion', '.androidstudio', '.xcode',

    // Python Related
    'venv', '.venv', 'env', '.env', '__pycache__', '.pytest_cache', '.coverage', '.tox', '.pytype',
    '.mypy_cache', '.pytest_cache', '.python-version', '.pyenv', 'pip-wheel-metadata', '.ipynb_checkpoints',
    '.spyderproject', '.ropeproject', '.pyre', 'node',

    // Node.js Related
    'node_modules', '.npm', '.yarn', '.pnpm', '.node-gyp', '.node_repl_history', '.v8flags', '.babel',
    '.next', '.nuxt', '.gatsby', '.remix', '.deno', '.bun',

    // Java Related
    'target', '.m2', '.mvn', '.gradle', 'build', 'out', '.settings', '.project', '.classpath', '.factorypath',
    'classes', 'META-INF', 'WEB-INF', '.springBeans', '.sts4-cache', '.grails',

    // Ruby Related
    '.bundle', 'vendor/bundle', '.gem', '.rvm', '.rbenv', '.ruby-version', '.ruby-gemset',
    'coverage', '.solargraph', '.byebug_history',

    // Go Related
    'pkg', 'bin', '.gvm', '.glide', '.dep', 'vendor', '.go-version', 'testdata', '.gonvim', '.gore',

    // Rust Related
    'target', '.cargo', '.rustup', '.cargo-cache', 'Cargo.lock', '.rust-version', '.rustfmt', '.clippy',

    // PHP Related
    'vendor', '.composer', '.php_cs.cache', '.phpunit', '.php-version', '.phpdoc', '.phalcon',
    '.phpbrew', '.php-cs-fixer',

    // Frontend Build and Dependencies
    'dist', 'build', '.cache', '.parcel-cache', '.vite', '.rollup', '.webpack', '.browserify',
    '.esbuild', '.swc', '.turbo', '.storybook', '.stylelint', '.sass-cache',

    // Mobile Development
    '.gradle', '.idea', 'build', 'captures', '.externalNativeBuild', '.cxx', '.pods', 'Pods',
    'xcuserdata', '.playground', '.flutter-plugins', '.pub-cache', '.android', '.ios',

    // Containers and Cloud Services
    '.docker', '.kubernetes', '.helm', '.terraform', '.vagrant', '.chef', '.puppet', '.ansible',
    '.salt', '.aws', '.azure', '.gcloud', '.digitalocean', '.heroku', '.vercel', '.netlify', '.firebase',

    // Databases and Caches
    '.mysql', '.postgresql', '.mongodb', '.redis', '.sqlite', '.cassandra', '.couchdb',
    '.elasticsearch', '.neo4j', '.influxdb', '.clickhouse', '.memcached',

    // CI/CD and Deployment
    '.github', '.gitlab', '.circleci', '.jenkins', '.travis', '.drone', '.teamcity', '.bamboo',
    '.buildkite', '.appveyor', '.codeship', '.wercker', '.semaphore', '.bitrise',

    // Documentation and Static Sites
    'docs', '_site', '.jekyll-cache', '.hugo_build.lock', '.vuepress', '.docusaurus', '.mkdocs',
    '.sphinx-build', '.gitbook', '.docz',

    // Tools and Utilities
    '.tmp', '.temp', '.cache', '.local', '.config', '.history', '.log', 'logs', 'backup', '.backup',
    'archive', '.archive', '.trash', '.recycle',

    // OS Specific
    '.DS_Store', '.Spotlight-V100', '.Trashes', '.Trash', '.fseventsd', 'Thumbs.db', 'Desktop.ini',
    '$RECYCLE.BIN', 'System Volume Information',

    // Security and Authentication
    '.ssh', '.gnupg', '.gpg', '.cert', '.credentials', '.secrets', '.vault', '.kube',
    '.keystore', '.truststore',

    // Monitoring and Analytics
    '.newrelic', '.datadog', '.sentry', '.grafana', '.prometheus', '.elastic', '.splunk',
    '.nagios', '.zabbix',

    // Testing and Quality Assurance
    'coverage', '.nyc_output', '.jest', '.cypress', '.selenium', '.testcafe', '.karma',
    '.playwright', '.puppeteer', '.webdriver', '.lighthouse', '.axe', '.sonar', '.codecov',

    // Multimedia and Resources
    'assets', 'media', 'uploads', 'downloads', 'public/uploads', 'storage/app', '.thumbs',
    '.previews', '.miniatures', '.compressed',

    // Localization and i18n
    'locale', 'locales', 'i18n', 'translations', '.translations', '.locale-data',
    '.messages', '.intl',

    // Old Files and Backups
    '.old', '.bak', '.backup', '.save', '.swp', '.swap', '.orig', '.rej', '.previous', '.historic',

    // Other Tools and Frameworks
    '.meteor', '.sails', '.yeoman', '.bower', '.grunt', '.gulp', '.brunch', '.middleman',
    '.eleventy', '.gatsby', '.phenomic', '.metalsmith'
  ]),

  // 按文件路径模式排除（glob语法）
  FILE_PATTERNS: [
    // Binary and Compiled Files
    '**/*.min.js', '**/*.js.map', '**/*.DS_Store', '**/*-lock.json', '**/*.lock',
    '**/*.log', '**/*.ttf', '**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.mp4',
    '**/*.svg', '**/*.ico', '**/*.pdf', '**/*.zip', '**/*.gz', '**/*.tar', '**/*.dmg',
    '**/*.tgz', '**/*.rar', '**/*.7z', '**/*.exe', '**/*.dll', '**/*.obj', '**/*.o',
    '**/*.o.d', '**/*.a', '**/*.lib', '**/*.so', '**/*.dylib', '**/*.ncb', '**/*.sdf',
    '**/*.woff', '**/*.woff2', '**/*.eot', '**/*.cur', '**/*.avi', '**/*.mpg', '**/*.mpeg',
    '**/*.mov', '**/*.mp3', '**/*.mp4', '**/*.mkv', '**/*.webm', '**/*.jar', '**/*.onnx',
    '**/*.parquet', '**/*.pqt', '**/*.wav', '**/*.webp', '**/*.db', '**/*.sqlite', '**/*.wasm',
    '**/*.plist', '**/*.profraw', '**/*.gcda', '**/*.gcno', '**/*.apk', '**/*.ipa',
    
    // Special Pattern Files
    '**/go.sum', '**/.env', '**/app.*.*.js', '**/chunk-*.js'
  ]
};