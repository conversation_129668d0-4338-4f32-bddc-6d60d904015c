import { Command, Disposable, StatusBarAlignment, StatusBarItem, ThemeColor, window } from 'vscode';
import { APP_NAME, RtnCode, RtnMessage, SrdCommand } from '../common/constants';
import { StatusBarState } from './types';

export default class SrdStatusBarItem implements Disposable {
  private item: StatusBarItem;

  private showAppName = false;

  public constructor(showAppName?: boolean) {
    if (showAppName !== undefined) {
      this.showAppName = showAppName;
    }

    this.item = window.createStatusBarItem(StatusBarAlignment.Left, -1);
    this.setDefault();
    this.item.show();
  }

  public dispose() {
    this.item.dispose();
  }

  public setStatusView(state: StatusBarState, code?: number) {
    switch (state) {
      case StatusBarState.NotLogin:
        case StatusBarState.NotLogin:
        this.setDefault('未登录', '点击查看选项', 'srd-copilot-unlogin');
        this.setCommand({
          command: SrdCommand.SHOW_STATUS_MENU,
          arguments: ['NotLogin', code],
          title: '',
        });
        break;
      case StatusBarState.Logining:
        this.setLoading('登录中...', '正在登录');
        this.setCommand(undefined);
        break;
      case StatusBarState.CodeCompleteEnabled:
        this.setDefault('自动补全启用', '点击查看选项', 'srd-copilot-code-enabled');
        this.setCommand({
          command: SrdCommand.SHOW_STATUS_MENU,
          arguments: ['CodeCompleteEnabled', code],
          title: '',
        });
        break;
      case StatusBarState.CodeCompleteDisabled:
        this.setWarning('自动补全禁用', '点击查看选项', 'srd-copilot-code-disabled');
        this.setCommand({
          command: SrdCommand.SHOW_STATUS_MENU,
          arguments: ['CodeCompleteDisabled', code],
          title: '',
        });
        break;
      case StatusBarState.WaitingAutoComplete:
        this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');
        this.setCommand(undefined);
        break;
      case StatusBarState.WaitingManualComplete:
        this.setLoading('获取手动补全代码中...', '正在获取手动补全代码');
        this.setCommand(undefined);
        break;
      case StatusBarState.WSServerError: {
        code = code || RtnCode.NO_CHANNEL;
        const msg = RtnMessage[code];
        this.setError(
          msg,
          '点击查看选项',
          code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'
        );
        this.setCommand(
          code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN
            ? {
                command: SrdCommand.SHOW_SVR_ERROR,
                arguments: [code],
                title: '',
              }
            : {
                command: SrdCommand.SHOW_STATUS_MENU,
                arguments: ['WSServerError', code],
                title: '',
              }
        );
        break;
      }
      case StatusBarState.SyncingAgent:
        this.setLoading('同步服务中...', '正在下载和安装智能编程服务');
        this.setCommand(undefined);
        break;
      case StatusBarState.AgentSyncFailed:
        this.setError('同步服务失败', '点击查看选项', 'error');
        this.setCommand({
          command: SrdCommand.SHOW_STATUS_MENU,
          arguments: ['AgentSyncFailed', code],
          title: '',
        });
        break;
      case StatusBarState.AgentStartupFailed:
        this.setError('服务启动失败', '点击查看选项', 'error');
        this.setCommand({
          command: SrdCommand.SHOW_STATUS_MENU,
          arguments: ['AgentStartupFailed', code],
          title: '',
        });
        break;
      case StatusBarState.CodeIndexing:
        const progress = code !== undefined ? Math.round(code) : 0;
        this.setLoading(`正在更新索引 (${progress}%)`, '正在更新代码索引，这可能需要一些时间');
        this.setCommand(undefined);
        break;
      default:
        break;
    }
  }

  private setDefault(statusText?: string, message?: string, icon?: string) {
    this.item.backgroundColor = undefined;

    if (statusText) {
      this.setText(statusText, icon);
    } else {
      this.item.text = '';
    }

    if (message) {
      this.item.tooltip = message;
    }
  }

  private setLoading(statusText: string, message: string) {
    this.setText(statusText, 'loading~spin');
    this.item.backgroundColor = undefined;
    this.item.tooltip = message;
  }

  private setError(statusText: string, message: string, icon?: string) {
    this.setText(statusText, icon);
    this.item.backgroundColor = new ThemeColor('statusBarItem.errorBackground');
    this.item.tooltip = message;
  }

  private setWarning(statusText: string, message: string, icon?: string) {
    this.setText(statusText, icon);
    this.item.backgroundColor = new ThemeColor('statusBarItem.warningBackground');
    this.item.tooltip = message;
  }

  private setCommand(command: string | Command | undefined) {
    this.item.command = command;
  }

  private setText(text: string, icon?: string) {
    const baseText = icon ? `$(${icon}) ${text}` : text;

    if (this.showAppName) {
      this.item.text = `${APP_NAME} (${baseText})`;
    } else {
      this.item.text = baseText;
    }
  }
}
