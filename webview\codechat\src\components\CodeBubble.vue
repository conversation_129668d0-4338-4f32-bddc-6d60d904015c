<template>
  <div class="code-bubble ai-bubble">
    <div class="message-base-info">
      <div class="logo">
        <svg-icon :name="brandStore.isSec ? 'ai-logo-sec' : 'ai-logo'" size="24px"></svg-icon>
      </div>
      <div class="message-base-info-text">
        <span class="message-user-name">{{ brandStore.brandName }}</span>
      </div>

    </div>

    <div class="message">
      <div class="message-text code-msg-text wait-for-copy-box" @click="triggerMessageText" v-html="htmlStr"></div>

      <div v-if="quote && quote.api" class="message-forward">
        <a-radio-group direction="vertical" :disabled="apiDisabled" v-model="apiIndex" @change="onChangeApi">
          <div v-for="(item, index) in quote.api" class="option-rows">
            <a-radio :value="index">
              {{ item.page_content }}
            </a-radio>
            <div v-if="item.metadata.source" class="option-row" @click.stop="openLink(item.metadata.source)">
              <svg-icon style="margin-left: 10px;cursor: pointer;" name="forward-icon" size="12px"></svg-icon>
            </div>
          </div>

        </a-radio-group>
      </div>

      <div v-if="quote && quote.document" class="message-quote">
        <div>
          <span class="quote-toggle" @click.stop="acitvedDocument = !acitvedDocument">
            相关链接 ({{ quote.document.length }})
            <icon-up v-if="acitvedDocument" class="icon-quote-arrow" />
            <icon-down v-else class="icon-quote-arrow" />
          </span>
        </div>
        <div v-if="acitvedDocument" class="message-quote-list">
          <div v-for="(item, index) in quote.document" class="message-quote-item"
            @click="openLink(item.metadata.source, item.type)">
            {{ index + 1 }}、{{ item.metadata.fileName }} <span style="margin-left: 20px;">{{ item.page_content }}</span>
          </div>

        </div>
      </div>
      <div v-if="quote && quote.guide" class="message-quote-guide">
        <div>
          更多开发说明请参考：
          <span @click="openLink(item.metadata.source)" v-for="(item, index) in quote.guide" class="guide-link">
            {{ item.page_content }}
          </span>
        </div>
      </div>
    </div>
    <div class="message-header" style="flex: 1; text-align: right;">
      <div>
        <slot name="toolbar"></slot>
      </div>
      <template v-if="answerText">
        <div tooltip="赞" style="margin-right: 3px;" class="msg-icon-btn">
          <svg-icon v-if="props.item.feedback === 'like'" name="like-color" class="msg-icon"
            style="cursor: not-allowed;"></svg-icon>
          <svg-icon v-else-if="props.item.feedback === 'unlike'" style="color: gray;cursor: not-allowed;" name="like"
            class="msg-icon"></svg-icon>
          <span v-else class="menu-icon">
            <svg-icon name="like" class="msg-icon" @click="like"></svg-icon>
          </span>

        </div>

        <div tooltip="踩" style="margin-right: 3px;" class="msg-icon-btn">
          <svg-icon v-if="props.item.feedback === 'unlike'" name="unlike-color" style="cursor: not-allowed;"
            class="msg-icon unlike-icon-actived"></svg-icon>
          <svg-icon v-else-if="props.item.feedback === 'like'" name="unlike" style="color: #504f4f;cursor: not-allowed;"
            class="msg-icon"></svg-icon>
          <span v-else class="menu-icon">
            <svg-icon name="unlike" class="msg-icon" @click="unlike"></svg-icon>
          </span>
        </div>
        <div v-if="showReGenBtn" tooltip="重新回答" style="margin-right: 3px;" class="menu-icon msg-icon-btn icon-re-gen">
          <svg-icon v-if="chatStore.answering" name="re-gen" class="msg-icon" style="cursor: not-allowed;"></svg-icon>
          <svg-icon v-else name="re-gen" class="msg-icon" @click="onRegen"></svg-icon>
        </div>

        <div tooltip="复制" class="msg-icon-btn menu-icon">
          <svg-icon name="copy" class="msg-icon copy-normal" @click="copyMessage"></svg-icon>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, reactive, ref, getCurrentInstance, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import MarkdownIt from 'markdown-it';
import MarkdownItTable from 'markdown-it-multimd-table'
import MarkdownItMath from 'markdown-it-texmath'
import katex from 'katex'
import hljs from 'highlight.js';
import { CodeInfo, Msg } from '@/types/index';
import { hashCode } from '@/utils';
import { useChatStore } from '@/store/chat';
import { useBrandStore } from '@/store/brand';
import * as sender from '@/MessageSender'
import { SubServiceType, WebViewReqCommand } from '@/constants/common';
import { v4 as uuidv4 } from 'uuid';
import mermaid from 'mermaid'
mermaid.initialize({
  startOnLoad: false,
  logLevel: 'debug',
});
const brandStore = useBrandStore();


function openFile(file: any) {
  chatStore.openFile(file)
}

function openLink(path: string, type: string = '') {
  // https://域名/smartassist/knowledgeBase/view/${文件类型}/${后端返回路径}
  const strs = path.split('.')
  const fileType = strs[strs.length - 1]
  const url = type === 'Web'? path : chatStore.host + `/smartassist/knowledgeBase/view/${fileType}/${encodeURIComponent(path)}`

  if (sender.isJet) {
    sender.postMessage({
      command: WebViewReqCommand.OPEN_EXTERNAL,
      data: {
        path: url,
        pureUrl: true
      }
    })
  } else {
    const a = document.createElement('a')
    a.href = url
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }

  console.log('文档地址', fileType, url)

}

const props = defineProps<{ item: any; files: any[], parent: any, userText: string }>();
/** 缓存会话中的代码块字符串 */
const codeInfo = reactive<CodeInfo>({
  codeStrMap: {},
});
const chatStore = useChatStore();
const apiIndex = ref(-1)

const showReGenBtn = computed(() => {
  return !chatStore.answering && props.item.children && props.item.children.length === 0;
})

const acitvedDocument = ref(false)

const apiDisabled = computed(() => {
  if (chatStore.chatAPIMap && chatStore.chatAPIMap[props.item.reqId]) {
    return false
  }
  return true
})

const quote = computed(() => {
  const content = props.item.content
  if (Array.isArray(content)) {
    const quoteContent = content.find(item => item.type === 'quote')
    if (quoteContent) {
      return JSON.parse(quoteContent.text)
    }
  }
  return null
})

function onChangeApi(index: any) {
  if (!quote.value) return
  const item = quote.value.api[index]
  chatStore.chatAPIMap[props.item.reqId] = false
  onSelectApi(item)
}

function onSelectApi(quote: any) {
  emit('onSelectApi', quote)
}

const answerText = computed(() => {
  const content = props.item.content
  if (Array.isArray(content)) {
    const textContent = content.find(item => item.type === 'text')
    if (textContent) {
      return textContent.text
    }
  }
  return ''
})

const msg = computed(() => {
  let text = answerText.value
  const index = chatStore.stopPrintPositionMap[props.item.reqId]
  if (index) {
    text = text.slice(0, index)
  }
  return text || props.item.errMsg || ''
})

/**
 * md & highlight
 */
const mdi = new MarkdownIt({
  linkify: true,
  highlight(code: string, language: string) {
    const validLang = !!(language && hljs.getLanguage(language));
    const hashCodeVal = hashCode(code);
    codeInfo.codeStrMap[hashCode(code)] = code;

    if (language === 'mermaid') {
      return renderMermaid(code, hashCodeVal)
    }

    if (validLang) {
      const lang = language ?? '';
      return highlightBlock(hljs.highlight(lang, code, true).value, lang, code, hashCodeVal);
    }
    return highlightBlock(hljs.highlightAuto(code).value, '', code, hashCodeVal);
  },
});
mdi.use(MarkdownItMath, {
  engine: katex,
  delimiters: 'brackets',
  katexOptions: {
    displayMode: true,
    output: 'mathml'
  }
})
mdi.use(MarkdownItTable as any)

function renderMermaid(str: string, hashCodeVal: number) {
  const id = 'graph_' + uuidv4()
  console.log(str)

  return `
    <div class="pre-code-box">
      <div class="pre-code-header">
        <span class="code-block-header__lang">Mermaid</span>
        <div class="code-block-header__icons" data-code="${hashCodeVal}">
          
          <div data-action="copy" tooltip="复制" class="code-block-header__icon">
            <svg class="inner-html-icon copy-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="12.086501121520996" height="13.800000190734863" viewBox="0 0 12.086501121520996 13.800000190734863"><g><path d="M3.45673,10.3384L0.654795,10.3384C0.293168,10.3384,6.8545e-8,10.0452,6.8545e-8,9.68356L6.8545e-8,0.654794C6.8545e-8,0.293167,0.293157,-7.53995e-7,0.654795,-7.53995e-7L7.97498,-7.53995e-7C8.33661,-7.53995e-7,8.62977,0.293156,8.62976,0.654794L8.62976,3.45672L11.4317,3.45672C11.7933,3.45672,12.0866,3.74988,12.0866,4.11152L12.0866,13.1452C12.0866,13.5068,11.7933,13.8,11.4317,13.8L4.11153,13.8C3.7499,13.8,3.45674,13.5068,3.45673,13.1452L3.45673,10.3384ZM3.45673,4.11645L3.45672,4.11152C3.45672,3.74988,3.74988,3.45672,4.11152,3.45672L7.44464,3.45672L7.44464,1.69277C7.44464,1.41241,7.21735,1.18514,6.93699,1.18515L1.67895,1.18515C1.40621,1.18515,1.18513,1.40622,1.18513,1.67895L1.18513,8.6752C1.18677,8.94061,1.40267,9.15473,1.66808,9.15419L3.45673,9.15419L3.45673,4.11645ZM5.09419,12.6099L10.4362,12.6099C10.698,12.6077,10.9085,12.3937,10.9063,12.132L10.9063,5.13763C10.9063,4.86492,10.6852,4.64384,10.4125,4.64384L5.11985,4.64384C4.85696,4.64384,4.64385,4.85696,4.64384,5.11986L4.64384,12.1585C4.64384,12.4075,4.8453,12.6093,5.09419,12.6099Z" fill="#307CFB" fill-opacity="1"/></g></svg>
          </div>
        </div>
      </div>
      <div class="pre-code">
        <div class="mermaid" id="${id}">${str}</div>
      </div>
    </div>
    `;
}

function highlightBlock(str: string, lang: string, code: string, hashCodeVal: number) {
  const blocks = code.split(/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/);
  // 渲染左侧的代码行数数字
  let decimalStr = '<div class="code-decimal-index">';
  blocks.forEach((item, itemIndex) => {
    decimalStr += `<div class="index-item">${itemIndex + 1}</div>`;
  });
  decimalStr += '</div>';
  // 插入图标显示
  // const showInsertIcon = [
  //   SubServiceType.CODEEXPLAIN,
  //   SubServiceType.CODEUNITTEST,
  //   SubServiceType.CODECOMMENT,
  //   SubServiceType.CODECHAT
  // ].includes(props.item.subService)
  const showInsertIcon = true

  // 新建文件图标显示
  const showCreateIcon = true //props.item.subService === SubServiceType.CODEUNITTEST;
  const showExecIcon = lang ? ['bash', 'PowerShell', 'CMD', 'Zsh'].map(item => item.toLowerCase()).includes(lang.toLowerCase()) : false
  const showDiffIcon = [SubServiceType.CODECOMMENT, SubServiceType.CODEOPTIMIZATION].includes(props.parent.subService)
  return `
    <div class="pre-code-box">
      <div class="pre-code-header">
        <span class="code-block-header__lang">${lang}</span>
        <div class="code-block-header__icons" data-code="${hashCodeVal}">

         ${showExecIcon
      ? `<div data-action="exec" tooltip="运行" class="code-block-header__icon">
                  <svg class="inner-html-icon" width="13px" height="13px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#307CFB" d="M512 0a512 512 0 1 0 0 1024A512 512 0 0 0 512 0z m0 936.229A424.74 424.74 0 0 1 87.771 512C87.771 278.09 278.09 87.771 512 87.771S936.229 278.09 936.229 512 745.91 936.229 512 936.229z"  /><path fill="#307CFB" d="M479.305 371.712a35.401 35.401 0 0 0-55.076 29.477V622.81c0 28.307 31.524 45.13 55.003 29.477l166.254-110.811a35.328 35.328 0 0 0 0.073-58.954L479.305 371.712z"  /></svg>
                </div>`
      : ''
    }
          ${showDiffIcon
      ? `<div data-action="diff"  tooltip="Diff" class="code-block-header__icon">
                  <svg class="inner-html-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="12.62168025970459" height="13.399999618530273" viewBox="0 0 12.62168025970459 13.399999618530273"><g><path d="M12.3377,3.81063L8.65316,0.165454C8.54557,0.0590123,8.40044,-0.000642549,8.24922,-0.000642549C7.93208,-0.000642549,7.67499,0.256448,7.67499,0.573586C7.67499,0.726951,7.73634,0.873942,7.84537,0.981803L11.4732,4.57088L11.4732,12.8251C11.4732,13.1423,11.7303,13.3994,12.0475,13.3994C12.3646,13.3994,12.6217,13.1423,12.6217,12.8251L12.6217,4.49099C12.6217,4.23538,12.5194,3.9904,12.3377,3.81063ZM0.957048,0L5.02165,0C5.47683,0,5.56318,0.0657737,5.8072,0.309767L10.2673,4.77192C10.4688,4.97345,10.5275,5.21499,10.5275,5.50781L10.5275,12.4416C10.5275,12.9702,10.099,13.3987,9.57048,13.3987L0.957048,13.3987C0.428485,13.3987,0,12.9702,0,12.4416L0,0.957048C0,0.428485,0.428485,0,0.957048,0ZM9.37907,5.50781L5.02165,1.14846L1.14846,1.14846L1.14846,12.2502L9.37907,12.2502L9.37907,5.50781ZM5.83799,5.55088L6.98645,5.55088C7.30227,5.55088,7.56068,5.80928,7.56068,6.1251C7.56068,6.44093,7.30227,6.69933,6.98645,6.69933L5.83799,6.69933L5.83799,7.84779C5.83799,8.16362,5.57959,8.42202,5.26376,8.42202C4.94794,8.42202,4.68953,8.16362,4.68953,7.84779L4.68953,6.69933L3.54108,6.69933C3.22525,6.69933,2.96685,6.44093,2.96685,6.1251C2.96685,5.80928,3.22525,5.55088,3.54108,5.55088L4.68953,5.55088L4.68953,4.40242C4.68953,4.08659,4.94794,3.82819,5.26376,3.82819C5.57959,3.82819,5.83799,4.08659,5.83799,4.40242L5.83799,5.55088ZM3.54108,9.37911L6.98645,9.37911C7.30227,9.37911,7.56068,9.63752,7.56068,9.95334C7.56068,10.2692,7.30227,10.5276,6.98645,10.5276L3.54108,10.5276C3.22525,10.5276,2.96685,10.2692,2.96685,9.95334C2.96685,9.63752,3.22525,9.37911,3.54108,9.37911Z" fill-rule="evenodd" fill="#307CFB" fill-opacity="1"/></g></svg>
                </div>`
      : ''
    }
       ${showCreateIcon
      ? `<div data-action="create" tooltip="新建文件" class="code-block-header__icon tooltip-width-70">
                  <svg class="inner-html-icon create-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="14" height="14" viewBox="0 0 14 14"><defs><clipPath id="master_svg0_14823_96862"><rect x="0" y="0" width="14" height="14" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_14823_96862)"><rect x="0" y="0" width="14" height="14" rx="0" fill="transport" fill-opacity="1"/><g><path d="M12.3857,0.5L1.61429,0.5C0.998883,0.4999999266382,0.5,0.998883,0.5,1.61429L0.5,12.3857C0.500000586894,13.0011,0.998883,13.5,1.61429,13.5L12.3857,13.5C13.0011,13.5,13.5,13.0011,13.5,12.3857L13.5,1.61429C13.5,0.998883,13.0011,0.5,12.3857,0.5ZM12.3857,12.3857L1.61429,12.3857L1.61429,1.61429L12.3857,1.61429L12.3857,12.3857Z" fill="#307CFB" fill-opacity="1"/></g><g transform="matrix(0.7071067690849304,-0.7071067690849304,0.7071067690849304,0.7071067690849304,-3.9434791273582164,4.479598946045655)"><path d="M3.598632585876465,11.089459999999999C3.381275185876465,11.3073,3.381275185876465,11.659980000000001,3.598632585876465,11.87782C3.8164725858764648,12.09517,4.169149585876465,12.09517,4.3869895858764645,11.87782L5.962774585876465,10.30203L7.538564585876465,11.87782C7.758964585876464,12.07838,8.098064585876465,12.07039,8.308774585876465,11.859670000000001C8.519494585876465,11.648959999999999,8.527484585876465,11.30986,8.326914585876466,11.089459999999999L6.751134585876464,9.51367L8.326914585876466,7.937888C8.531474585876465,7.718367,8.525434585876464,7.376282,8.313264585876464,7.164113C8.101094585876464,6.9519441,7.759014585876465,6.9459076,7.5394845858764645,7.150459L5.962774585876465,8.72532L4.3869895858764645,7.149531C4.167468585876465,6.9449792,3.8253835858764647,6.9510155,3.613214585876465,7.163184C3.401046085876465,7.375352,3.395009485876465,7.717437,3.5995615858764647,7.936959L5.174414585876464,9.51367L3.598632585876465,11.089459999999999Z" fill="#307CFB" fill-opacity="1"/></g></g></svg>
                </div>`
      : ''
    }
            ${showInsertIcon
      ? `<div data-action="insert" tooltip="插入" class="code-block-header__icon">
                  <svg class="inner-html-icon insert-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="13" height="13" viewBox="0 0 13 13"><g><path d="M3.71429,2.78571L3.71429,0.928571C3.71429,0.415736,4.13002,0,4.64286,0L12.0714,0C12.5843,0,13,0.415736,13,0.928571L13,2.78571C13,3.29855,12.5843,3.71429,12.0714,3.71429L4.64286,3.71429C4.13002,3.71429,3.71429,3.29855,3.71429,2.78571ZM11.8857,2.6L4.82857,2.6L4.82857,1.11429L11.8857,1.11429L11.8857,2.6ZM0,7.42857L0,5.57143C0,5.05859,0.415736,4.64286,0.928571,4.64286L8.35714,4.64286C8.86998,4.64286,9.28571,5.05859,9.28571,5.57143L9.28571,7.42857C9.28571,7.94141,8.86998,8.35714,8.35714,8.35714L0.928571,8.35714C0.415736,8.35714,0,7.94141,0,7.42857ZM10.9704,5.79039C10.7879,5.58544,10.7968,5.27368,10.9907,5.07948C11.1847,4.88528,11.4965,4.87601,11.7016,5.05834L12.7984,6.15512C13.0675,6.42529,13.0671,6.86234,12.7975,7.13204L11.7008,8.22968C11.4952,8.40874,11.1861,8.39792,10.9935,8.20493C10.801,8.01194,10.7909,7.70277,10.9704,7.49763L11.8232,6.64401L10.9704,5.79039ZM8.17143,5.75714L8.17143,7.24286L1.11429,7.24286L1.11429,5.75714L8.17143,5.75714ZM3.71429,12.0714L3.71429,10.2143C3.71429,9.70145,4.13002,9.28571,4.64286,9.28571L12.0714,9.28571C12.5843,9.28571,13,9.70145,13,10.2143L13,12.0714C13,12.5843,12.5843,13,12.0714,13L4.64286,13C4.13002,13,3.71429,12.5843,3.71429,12.0714ZM11.8857,10.4L11.8857,11.8857L4.82857,11.8857L4.82857,10.4L11.8857,10.4Z" fill-rule="evenodd" fill="#307CFB" fill-opacity="1"/></g></svg>
                </div>`
      : ''
    }

          <div data-action="copy" tooltip="复制" class="code-block-header__icon">
            <svg class="inner-html-icon copy-icon" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="12.086501121520996" height="13.800000190734863" viewBox="0 0 12.086501121520996 13.800000190734863"><g><path d="M3.45673,10.3384L0.654795,10.3384C0.293168,10.3384,6.8545e-8,10.0452,6.8545e-8,9.68356L6.8545e-8,0.654794C6.8545e-8,0.293167,0.293157,-7.53995e-7,0.654795,-7.53995e-7L7.97498,-7.53995e-7C8.33661,-7.53995e-7,8.62977,0.293156,8.62976,0.654794L8.62976,3.45672L11.4317,3.45672C11.7933,3.45672,12.0866,3.74988,12.0866,4.11152L12.0866,13.1452C12.0866,13.5068,11.7933,13.8,11.4317,13.8L4.11153,13.8C3.7499,13.8,3.45674,13.5068,3.45673,13.1452L3.45673,10.3384ZM3.45673,4.11645L3.45672,4.11152C3.45672,3.74988,3.74988,3.45672,4.11152,3.45672L7.44464,3.45672L7.44464,1.69277C7.44464,1.41241,7.21735,1.18514,6.93699,1.18515L1.67895,1.18515C1.40621,1.18515,1.18513,1.40622,1.18513,1.67895L1.18513,8.6752C1.18677,8.94061,1.40267,9.15473,1.66808,9.15419L3.45673,9.15419L3.45673,4.11645ZM5.09419,12.6099L10.4362,12.6099C10.698,12.6077,10.9085,12.3937,10.9063,12.132L10.9063,5.13763C10.9063,4.86492,10.6852,4.64384,10.4125,4.64384L5.11985,4.64384C4.85696,4.64384,4.64385,4.85696,4.64384,5.11986L4.64384,12.1585C4.64384,12.4075,4.8453,12.6093,5.09419,12.6099Z" fill="#307CFB" fill-opacity="1"/></g></svg>
          </div>
        </div>
      </div>
      <div class="pre-code">
        ${decimalStr}
        <code class="hljs code-block-body ${lang}">${str}</code>
      </div>
    </div>
    `;
}
const getMdiText = (value: string) => {
  return mdi.render(value);
};
const htmlStr = computed(() => {
  return getMdiText(msg.value);
});
let debounceTimeout: any = null

/**
 * 代码操作
 * @param event
 */
function triggerMessageText(event: any) {
  if (debounceTimeout) return
  debounceTimeout = setTimeout(() => {
    clearTimeout(debounceTimeout)
    debounceTimeout = null
  }, 400)
  // 复制代码到剪贴板
  let code = event.target.parentElement.dataset.code || '';
  const content = codeInfo.codeStrMap[code] || '';
  if (event.target.dataset.action === 'copy') {
    chatStore.handleReportData(content);

    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(content).then(() => {
        Message.info('已复制');

      })
    } else if (document.queryCommandSupported('copy')) {
      const tempTextarea = document.createElement('textarea');
      tempTextarea.value = content;
      document.body.appendChild(tempTextarea);
      tempTextarea.select();
      document.execCommand('copy');
      document.body.removeChild(tempTextarea);
      Message.info('已复制');

    } else {
      Message.error('复制失败');
    }

  } else if (event.target.dataset.action === 'create') {  // 新建文件(单元测试插入)
    chatStore.handleInsertUittest(content);
    chatStore.handleReportData(content);
  } else if (event.target.dataset.action === 'insert') { // 插入代码到编辑器
    chatStore.handleInsertCode(content);
    chatStore.handleReportData(content);
  } else if (event.target.dataset.action === 'diff') { // diff
    const file = props.files && props.files[0]
    console.log('show Diff', file)
    if (file) {
      let rawCode = props.userText.split('```')[1]
      if (rawCode) {
        const indx = rawCode.indexOf('\n')
        rawCode = rawCode.slice(indx + 1)
      }

      console.log('rawCode', rawCode)
      chatStore.showDiff({
        filePath: file.path, // 绝对路径
        originalContent: rawCode, // 提问代码块内容
        generatedContent: content, // 模型生成内容
        // startLine: param.startLine, // 非必填
        // endLine: param.endLine, // 非必填
      })
      chatStore.handleReportData(content);
    }

  } else if (event.target.dataset.action === 'exec') {
    chatStore.execCode(content)
  }
}
function copyMessage() {
  // 上报生成的多段代码行数信息
  const list = `${msg.value}`.match(/```(.|\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n))*?```/g);
  const resList: string[] = [];
  list?.forEach(item => {
    const str = (item || '').replace(/```/g, '');
    resList.push(str);
  });
  chatStore.handleReportData(resList.join(''));

  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(msg.value).then(() => {
      Message.info('已复制');
    })
  } else if (document.queryCommandSupported('copy')) {
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = msg.value;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    document.execCommand('copy');
    document.body.removeChild(tempTextarea);
    Message.info('已复制');
  } else {
    Message.error('复制失败');
  }
}



const emit = defineEmits(['like', 'unlike', 'onRegen', 'onSelectApi']);

function like() {
  emit('like')
}
function unlike() {
  emit('unlike',)
}

function onRegen() {
  emit('onRegen')
}
onMounted(() => {
  setTimeout(async () => {
    // const els = document.querySelectorAll('.mermaid')

    // if (els && els.length) {
    //   mermaid.run({
    //     querySelector: '.mermaid',
    //   })
    // }
    mermaid.run({
      querySelector: '.mermaid',
    })
  }, 600)
})

</script>

<style lang="scss">
.message-header {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;


  .msg-icon-btn {
    font-size: 12px;
    border-radius: 3px;
    height: 24px;
    width: 24px;
    display: flex;
    justify-content: center;
    align-items: center;


    .copy-normal {
      display: inline;
      opacity: 0.8;
    }
  }


  .msg-icon {
    cursor: pointer;
    font-size: 15px;
  }

  .like-icon-actived {
    color: rgb(121, 187, 126) !important;
    cursor: not-allowed;
  }

  .unlike-icon-actived {
    color: currentColor;
    opacity: 0.6;
    cursor: not-allowed;
  }

  .msg-icon-btn {
    font-size: 12px;
    border-radius: 3px;
    height: 24px;
    width: 24px;
    display: flex;
    justify-content: center;
    align-items: center;


    .copy-normal {
      display: inline;
      opacity: 0.8;
    }
  }

  .menu-icon {
    box-sizing: border-box;
    width: 24px;
    margin: 0 1px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--vscode-inputOption-activeForeground);

    &.active-btn {
      background-color: var(--vscode-list-inactiveSelectionBackground);
      border-radius: 3px;
    }

    &:hover {
      background-color: var(--vscode-toolbar-hoverBackground);
      // color: var(--vscode-textLink-foreground);
      border-radius: 3px;
    }
  }
}


.icon-re-gen::after {
  width: 76px !important;
}


.guide-link {
  color: var(--vscode-textLink-foreground);
  cursor: pointer;
  margin-right: 10px;
  font-size: 12px;
}

.message-quote-guide {
  margin-bottom: 10px;
  color: var(--vscode-input-foreground);
  font-size: 12px;
  line-height: 1.5;
}

.message-quote {
  margin-top: 10px;
  opacity: 0.9;
  margin-bottom: 10px;
  color: var(--vscode-textLink-foreground);
  font-size: 12px;

  .quote-toggle {
    cursor: pointer;
  }

  .icon-quote-arrow {
    color: var(--vscode-textLink-foreground);
  }

  .message-quote-list {
    margin-top: 10px;
  }

  .message-quote-item {
    margin-top: 4px;
    border-radius: 4px;
    border: 1px solid var(--vscode-input-border);
    color: var(--vscode-input-foreground);
    padding: 10px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;

    &:hover {
      border: 1px solid var(--vscode-textLink-foreground);
    }
  }
}

.option-rows {
  display: flex;
  align-items: center;
  overflow: hidden;
  width: 100%;

  .option-row {
    width: 30px;
  }

  .arco-radio-label {
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: nowrap;
  }

  .arco-radio {
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: nowrap;
  }

}

.arco-radio-group {
  width: 98%;
  overflow: hidden;
}

.message-forward {
  margin-bottom: 10px;
  overflow: hidden;

  .arco-radio-icon {
    border: 2px solid var(--vscode-input-foreground) !important;
  }

  .arco-radio-checked {}

  .arco-radio-disabled .arco-radio-icon {
    border-color: var(--color-neutral-3) !important;
  }

  .arco-radio-label {
    color: var(--vscode-foreground) !important;
  }
}

.code-msg-text {
  p {
    word-wrap: break-word;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  .inner-html-icon {
    pointer-events: none;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    text-align: left;

    th {
      font-weight: bold;
    }

    th,
    td {
      padding: 5px;
      border: 1px solid var(--vscode-panel-border);
    }

    caption {
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: bold;
      text-align: left;
    }

    th {
      font-weight: bold;
      color: var(--vscode-input-foreground)
    }

    tr:nth-child(even) {}

    tr:hover {}
  }
}
</style>
