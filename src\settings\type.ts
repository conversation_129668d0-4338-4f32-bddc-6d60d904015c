export interface LanguageConfig {
  语言: string;
  尾缀名: string;
  是否禁用: boolean;
}

// 语言配置键名到文件扩展名的映射
export const LANGUAGE_EXTENSION_MAP: Record<string, string> = {
  'ABAP': 'abap',
  'ActionScript': 'as',
  'Ada': 'ada',
  'AsciiDoc': 'adoc',
  'Makefile': 'am',
  'AppleScript': 'applescript',
  'Arc': 'arc',
  'ASP': 'asp',
  'Assembly': 'asm',
  'AutoHotkey': 'ahk',
  'AutoIt': 'au3',
  'Awk': 'awk',
  'Batch': 'bat',
  'Bazel': 'bzl',
  'BibTeX': 'bib',
  'Bison': 'bison',
  'BitBake': 'bb',
  'Blade': 'blade.php',
  'Bash': 'sh',
  'C': 'c',
  'CSharp': 'cs',
  'CPlusPlus': 'cpp',
  'CMake': 'cmake',
  'COBOL': 'cob',
  'CoffeeScript': 'coffee',
  'ColdFusion': 'cfm',
  'Clojure': 'clj',
  'CSS': 'css',
  'CSV': 'csv',
  'CUDA': 'cu',
  'D': 'd',
  'Dart': 'dart',
  'Delphi': 'dpr',
  'Pascal': 'pas',
  'Diff': 'diff',
  'Patch': 'patch',
  'Dockerfile': 'dockerfile',
  'DTD': 'dtd',
  'Erlang': 'erl',
  'Elixir': 'ex',
  'ElixirScript': 'exs',
  'Elm': 'elm',
  'EEx': 'eex',
  'LiveEEx': 'leex',
  'FSharp': 'fs',
  'Fortran': 'f',
  'Fortran90': 'f90',
  'Fish': 'fish',
  'Forth': 'fth',
  'GLSL': 'glsl',
  'Go': 'go',
  'GraphQL': 'gql',
  'Groovy': 'groovy',
  'Gradle': 'gradle',
  'Haml': 'haml',
  'Haskell': 'hs',
  'HCL': 'hcl',
  'HLSL': 'hlsl',
  'HTML': 'html',
  'HTTP': 'http',
  'Haxe': 'hx',
  'INI': 'ini',
  'Java': 'java',
  'JavaScript': 'js',
  'JSX': 'jsx',
  'JSON': 'json',
  'Julia': 'jl',
  'Kotlin': 'kt',
  'KotlinScript': 'kts',
  'LaTeX': 'tex',
  'Less': 'less',
  'Lisp': 'lisp',
  'Lua': 'lua',
  'MATLAB': 'm',
  'ObjectiveCPlusPlus': 'mm',
  'Markdown': 'md',
  'Nix': 'nix',
  'OCaml': 'ml',
  'Perl': 'pl',
  'PHP': 'php',
  'PowerShell': 'ps1',
  'ProtocolBuffers': 'proto',
  'Python': 'py',
  'R': 'r',
  'Ruby': 'rb',
  'Rust': 'rs',
  'Sass': 'sass',
  'Scala': 'scala',
  'SCSS': 'scss',
  'SQL': 'sql',
  'Stylus': 'styl',
  'SVG': 'svg',
  'Swift': 'swift',
  'Tcl': 'tcl',
  'Terraform': 'tf',
  'TypeScript': 'ts',
  'TSX': 'tsx',
  'Twig': 'twig',
  'Text': 'txt',
  'VBNET': 'vb',
  'Vue': 'vue',
  'XML': 'xml',
  'XSLT': 'xsl',
  'YAML': 'yaml',
  'Zig': 'zig'
};
