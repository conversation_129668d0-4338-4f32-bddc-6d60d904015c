import { createApp } from 'vue';
import App from './App.vue';
import './styles/index.scss';
import SvgIcon from '@/components/SvgIcon.vue';
import 'virtual:svg-icons-register';
import store from './store';
import mitt from 'mitt';
import { BusEvents } from './types';
import { isJet } from './MessageSender';

if (isJet) {
  console.log('isJet')
  import('@/styles/jb/vscode-var-dark.scss');
  import('@/styles/jb/vscode-var.scss');
}

const app = createApp(App);

app.use(store);
app.config.globalProperties.$EventBus = mitt<BusEvents>();
app.component('SvgIcon', SvgIcon);
app.mount('#app');
