<template>
  <div class="code-bubble ai-bubble" :class="{ 'is-printing': isPrinting }">
    <div v-if="props.logo" class="message-base-info">
      <div class="logo">
        <svg-icon :name="brandStore.isSec ? 'ai-logo-sec' : 'ai-logo'" size="24px"></svg-icon>
      </div>
      <div class="message-base-info-text">
        <span class="message-user-name">{{ brandStore.brandName }}</span>
      </div>
    </div>
    <div class="message">
      <div class="message-text code-msg-loading-text wait-for-copy-box" v-html="htmlStr" @click="handleClick"></div>
    </div>
    <div>

    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onBeforeUnmount, ref, watch, getCurrentInstance, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import markdownitAttrs from 'markdown-it-attrs'
import MarkdownItTable from 'markdown-it-multimd-table'
import MarkdownItMath from 'markdown-it-texmath'
import katex from 'katex'
import hljs from 'highlight.js';
import { ConvLoadingMsg } from '@/types/index';
import { WebViewReqCommand } from '@/constants/common';
import { ChatMessageType, introQuestion } from '@/constants/common';
import { useChatStore } from '@/store/chat';
import { useBrandStore } from '@/store/brand';
import * as sender from '@/MessageSender'
const emit = defineEmits(['linePrint', 'finishPrint', 'shortcut', 'submitMsg']);
/**
 * props传参
 */
const props = defineProps<ConvLoadingMsg>();
const chatStore = useChatStore()
const brandStore = useBrandStore();

/**
 * md & highlight
 */
const mdi = new MarkdownIt({
  linkify: true,
  highlight(code: string, language: string) {
    const validLang = !!(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? '';
      return highlightBlock(hljs.highlight(lang, code, true).value, lang, code);
    }
    return highlightBlock(hljs.highlightAuto(code).value, '', code);
  },
  html: true
});

mdi.use(markdownitAttrs as any);
mdi.use(MarkdownItMath, {
  engine: katex,
  delimiters: 'brackets',
  katexOptions: {
    displayMode: true,
    output: 'mathml'
  }
})
mdi.use(MarkdownItTable as any)

function highlightBlock(str: string, lang: string, code: string) {
  const blocks = code.split(/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/);
  let decimalStr = '<div class="code-decimal-index">';
  blocks.forEach((item, itemIndex) => {
    decimalStr += `<div class="index-item">${itemIndex + 1}</div>`;
  });
  decimalStr += '</div>';
  return `
    <div class="pre-code-box">
      <div class="pre-code-header">
        <span class="code-block-header__lang">${lang}</span>
      </div>
      <div class="pre-code">
        ${decimalStr}
        <code class="hljs code-block-body ${lang}">${str}</code>
      </div>
    </div>
    `;
}
const innerText = ref('');
const getMdiText = (value: string) => {

  return mdi.render(value);
};
const htmlStr = computed(() => {
  if (props.silent) {
    return getMdiText(props.content)
  }
  if (printingIndex.value < (props.content || '').length) {
    const len = innerText.value.length;
    if (
      len > 3 &&
      /\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/.test(
        innerText.value.slice(len - 3, len)
      )
    ) {
      return getMdiText(innerText.value);
    } else {
      return getMdiText(innerText.value + '▌');
    }
  } else {
    return getMdiText(innerText.value);
  }
});

/**
 * 定时打字效果
 */
const printingIndex = ref(0); // 截取的字符长度索引
const isPrinting = computed(() => printingIndex.value < (props.content || '').length);
const printInterval = ref<number | null>(null);

const app = getCurrentInstance();

const $EventBus = app?.appContext.config.globalProperties.$EventBus;


function printText() {
  const speed = chatStore.PrintCacheConfig.printInterval
  const chunkSize = chatStore.PrintCacheConfig.chunkSize

  if (printInterval.value !== null) return;
  printInterval.value = Number(
    setInterval(() => {
      // 判断接下来6个字符中是否包含代码块标识 ```，是的话，索引+=6，避免将符号 ``` 截断
      const testStr = props.content.slice(printingIndex.value, printingIndex.value + 6);
      printingIndex.value += /```/.test(testStr) ? 6 : chunkSize;
      chatStore.printIndex = printingIndex.value 
      innerText.value = props.content.slice(0, printingIndex.value);
      // 判断当前打字效果是否应换行
      if (/\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n)/.test(testStr)) {
        emit('linePrint');
      }
      // 结束打字
      if (printingIndex.value >= props.content.length) {
        clearInterval(Number(printInterval.value));
        printInterval.value = null;
        emit('finishPrint');
      }
    }, speed)
  );
}

watch(
  () => props.content,
  () => {
    if (!props.silent) {
      printText();
    } else {
      emit('finishPrint');
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
function handleClick(event: any) {
  console.log('event', event)
  if (event.target.id === 'helpDoc') {
    event.preventDefault()
    helpDoc()
  } else if (event.target.id === 'feedback') {
    event.preventDefault()
    feedback()
  } else if (event.target.id === 'codeExplain') {
    event.preventDefault()
    emit('shortcut', ChatMessageType.EXPLAIN)
  } else if (event.target.id === 'codeComment') {
    event.preventDefault()
    emit('shortcut', ChatMessageType.COMMENT)
  } else if (event.target.id === 'codeTest') {
    event.preventDefault()
    emit('shortcut', ChatMessageType.UNITTEST)
  } else if (event.target.id === 'codeOptimazation') {
    event.preventDefault()
    emit('shortcut', ChatMessageType.OPTIMIZATION)
  } else if (event.target.id === 'codeOptimazation') {
    event.preventDefault()
    emit('shortcut', ChatMessageType.OPTIMIZATION)
  } else if (event.target.id === 'introHelpDoc') {
    event.preventDefault()
    emit('shortcut', ChatMessageType.HELP)
  } else if (event.target.id === 'introQuestion') {
    event.preventDefault()
    emit('submitMsg', introQuestion)
  } else if (event.target.id === 'login') {
    chatStore.handleSubmitLogin();
  } else if (event.target.id === 'whatisnew') {
    event.preventDefault()
    emit('shortcut', 999)
  }

}
function helpDoc() {
  sender.postMessage({
    command: WebViewReqCommand.OPEN_EXTERNAL,
    data: {
      path: '/helpcenter/content?id=1189244999559761920'
    }
  })
}
function feedback() {
  sender.postMessage({
    command: WebViewReqCommand.OPEN_EXTERNAL,
    data: {
      path: '/feedback/feedback'
    }
  })
}

function onStopAnswer() {
  if (chatStore.lastIsEndAnswerReqId) {
    chatStore.stopPrintPositionMap[chatStore.lastIsEndAnswerReqId] = printingIndex.value;
    emit('finishPrint');
    clearInterval(printInterval.value!)
  }
}
onMounted(() => {
  $EventBus.on('onStopAnswer', onStopAnswer);
})

onBeforeUnmount(() => {
  clearInterval(Number(printInterval.value));
  $EventBus.off('onStopAnswer', onStopAnswer);
});
</script>

<style lang="scss">
.is-printing .code-block-header__icon {
  display: none;
}

.yellow {
  color: rgb(255, 140, 0);
}

ol {
  list-style-type: none;
}

ol li::before {
  content: '•';
  color: rgb(0, 153, 255);
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

a:focus {
  outline: none;
}

.message {
  flex-grow: 1;
}

.custom_button {
  background-color: #307CFB;
  /* 蓝色背景 */
  border: none;
  /* 去掉默认边框 */
  color: white;
  /* 白色文字 */
  padding: 5px 10px;
  /* 内边距 */
  font-size: 11px;
  /* 字体大小 */
  border-radius: 15px;
  /* 圆角边框 */
  cursor: pointer;
  /* 鼠标指针样式 */
  transition: background-color 0.3s ease;
  margin-right: 8px;
  /* 背景色过渡效果 */
}

#introHelpDoc {
  text-decoration: none !important;
  padding-right: 10px;
}

#introQuestion {
  color: #468CFF;
  text-decoration: none !important;
  padding-left: 10px;
}

#helpDoc {
  color: #468CFF;
  text-decoration: none !important;
  padding-right: 10px;
}

#feedback {
  color: #468CFF;
  text-decoration: none !important;
  padding-left: 10px;
}

#login {
  background-color: #307CFB;
  /* 蓝色背景 */
  border: none;
  /* 去掉默认边框 */
  color: white;
  /* 白色文字 */
  padding: 5px 10px;
  /* 内边距 */
  font-size: 14px;
  /* 字体大小 */
  border-radius: 15px;
  /* 圆角边框 */
  cursor: pointer;
  /* 鼠标指针样式 */
  transition: background-color 0.3s ease;
  /* 背景色过渡效果 */
  width: 120px;
  height: 32px;
  overflow: hidden
}

.custom_button:hover {
  background-color: #0056b3;
  /* 鼠标悬停时的背景色 */
}

.button-group {
  display: flex;
  gap: 2px;
  padding-top: 5px;
  flex-wrap: wrap;
}

.button-group button {
  flex: 0 0 auto;
}

.intro-note {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}

.code-msg-loading-text {
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    text-align: left;

    th {
      font-weight: bold;
    }

    th,
    td {
      padding: 5px;
      border: 1px solid var(--vscode-panel-border);
    }

    caption {
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: bold;
      text-align: left;
    }

    th {
      font-weight: bold;
      color: var(--vscode-input-foreground)
    }

    tr:nth-child(even) {}

    tr:hover {}
  }
}
</style>
