# Use Node.js 18 as base image
# node:18.20-slim 这个镜像在我们的jenkins环境中有问题，理论上容器中应该能正常运行node --version的，而jenkins下未知原因导致node -v运行错误
#FROM node:18.20-slim
FROM python:3.6

# Install necessary tools: curl and xz-utils
# Python images are often based on Debian/Ubuntu, so apt-get is usually available.
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && apt-get update  \
    && apt-get install -y libsecret-1-dev && apt-get install -y curl && apt-get install -y xz-utils  \
    && apt-get install -y build-essential && rm -rf /var/lib/apt/lists/*

# Download Node.js from Taobao mirror
RUN mkdir -p /usr/local/nodejs && curl -SLO "https://npmmirror.com/mirrors/node/v18.20.2/node-v18.20.2-linux-x64.tar.xz" \
    && tar -xvf node-v18.20.2-linux-x64.tar.xz -C /usr/local/nodejs --strip-components=1 \
    && rm node-v18.20.2-linux-x64.tar.xz

# Add Node.js to the PATH
ENV PATH="/usr/local/nodejs/bin:${PATH}"

# Verify Node.js installation
RUN npm config set registry https://registry.npmmirror.com/ && npm install -g yarn

# set China mirrors
RUN yarn config set registry https://registry.npmmirror.com/

# Set working directory
WORKDIR /app
# Copy project to working directory
COPY . /app

# 根据DOCKER_BUILDKIT=1?判断是否挂在yarn缓存
RUN --mount=type=cache,target=/root/.cache/yarn true

# Set working directory
WORKDIR /app/secidea-plugin-vscode

# Install dependencies
RUN yarn install

# Build the package and the output .vsix file will be in the /app/dist directory
RUN yarn run vsce:package:prod --out /app/dist


FROM alpine:3.20.2

RUN mkdir -p /build

COPY --from=0 /app/dist/*.vsix /build/
