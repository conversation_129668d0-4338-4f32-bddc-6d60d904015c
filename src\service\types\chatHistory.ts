
export interface GetChatHistoryResp { 
  optResult: number;
  dialogs: DialogInfo[];
  pageNo: number;
  totalDataCount: number;
  code?: number | string;
}

export interface GetDialogResp { 
  optResult: number;
  code?: number;
  dialog: DialogType;
}

export interface DialogType { 
  createTime: string;
  title: string;
  questions: unknown;
  systemPrompt: unknown;
}

export interface DialogInfo { 
  createTime?: string;
  dialogId?: string;
  subService?: string;
  title?: string;
  updateTime?: string;
  isNewConversation ?: boolean;
}
