import { getProjectPathWithRetry } from './util';
import * as path from 'path';
import { DiffEditorViewManager } from './diffView/diffEditor';
import { DiffFile } from './types';
import { InlineDiffViewManager } from './diffView/InlineDiff';
import * as vscode from 'vscode';
import { DiffViewManager } from './diffView';
import { DiffStatus } from './types';
import { Logger } from '../utils/logger';

export class DiffService {
  public diffFiles: DiffFile[] = [];
  private static instance: DiffService | null;
  private inlineDiffEnabled: boolean = true;
  public static diffViewManager: DiffViewManager | null;

  // private ideMessenger: any; // Assuming IdeMessengerContext is available or can be injected

  // diff回答消息的处理
  public constructor() {
    if (DiffService.diffViewManager == null) {
      if (this.inlineDiffEnabled) {
        DiffService.diffViewManager = InlineDiffViewManager.getInstance();
      } else { 
        DiffService.diffViewManager = DiffEditorViewManager.getInstance();
      }
    }
  }

  public static getInstance(): DiffService {
    if (DiffService.instance == null) {
      DiffService.instance = new DiffService();
    }
    return DiffService.instance;
  }

  public static setInstance(diffService: DiffService) {
    DiffService.instance = diffService;
  }

  public async initialize(
    res: {
      path: string;
      beforeContent: string;
      afterContent: string;
    }[]
  ) {
    this.clearDiffs();

    // Check if res exists and has items
    if (!res || !Array.isArray(res) || res.length === 0) {
      // Silently return if no diffs to apply
      return;
    }

    // Get the project path once before processing diffs
    const projectBasePath = await getProjectPathWithRetry();

    res.forEach(diff => {
      if (!diff || !diff.path || typeof diff.path !== 'string') {
        // console.warn("Skipping invalid diff:", diff);
        return;
      }

      try {
        const absolutePath =
          projectBasePath && diff.path
            ? `${projectBasePath}/${diff.path}`.replace(/\/+/g, '/').replace(/\\/g, '/')
            : '';

        if (!absolutePath) {
          // console.warn("Invalid path construction:", { projectBasePath, diffPath: diff.path });
          return;
        }

        // 管理文件列表并通知webiew

        // 启动diff窗口
        this.writeFileDiff(absolutePath, diff.afterContent || '');

        this.addDiff({
          path: absolutePath,
          name: path.basename(diff.path),
          content: diff.afterContent || '',
          originalContent: diff.beforeContent || '',
          status: 'undecided',
        });


        // Open the diff editor instead of accepting
        
      } catch (error) {
        // console.error("Error processing diff:", error);
      }
    });
  }

  private async writeFileDiff(path: string, contents: string): Promise<void> {
    const data = { path, content: contents };
    if (this.inlineDiffEnabled) {
      /***inlineDiff能力***/
      if (this.diffFiles.some((diffFile) => diffFile.path === path)) {
        let uri = vscode.Uri.file(path);
        try {
          await vscode.workspace.fs.stat(uri);
        } catch (error) {
          // this is a new file
          uri = vscode.Uri.file(path).with({ scheme: 'untitled' });
        }
        vscode.workspace.openTextDocument(uri).then(doc => {
          vscode.window.showTextDocument(doc);
        })
        return;
      }
      return InlineDiffViewManager.getInstance().openDiffView(data);
    } else { 
      /***原生diff能力**/
      return DiffEditorViewManager.getInstance().openDiffView(data);
    }
  }

  public openDiffEditor(path: string, content: string) {
    this.writeFileDiff(path, content);
  }

  public async acceptFile(data: { path: string; content: string }): Promise<void> {
    if (this.inlineDiffEnabled) {
      let uri = vscode.Uri.file(data.path);
      try {
        await vscode.workspace.fs.stat(uri);
      } catch (error) {
        // this is a new file
        uri = vscode.Uri.file(data.path).with({ scheme: 'untitled' });
      }
      vscode.commands.executeCommand('codefree-composer.AcceptAllChanges', uri);
    } else { 
      DiffEditorViewManager.getInstance().acceptFile(data);
    }
  }

  public async undoAcceptFile(data: { path: string; originalContent?: string; content: string }): Promise<void> {
    if (this.inlineDiffEnabled) { 
      InlineDiffViewManager.getInstance().undoAllChanges(data);
      return;
    }
    DiffEditorViewManager.getInstance().undoAcceptFile(data);
  }

  public async rejectFile(data: { path: string; content: string }): Promise<void> { 
    if (this.inlineDiffEnabled) { 
      let uri = vscode.Uri.file(data.path);
      try {
        await vscode.workspace.fs.stat(uri);
      } catch (error) {
        // this is a new file
        uri = vscode.Uri.file(data.path).with({ scheme: 'untitled' });
      }
      vscode.commands.executeCommand('codefree-composer.RejectAllChanges', uri);
    } 
  }

  public rejectDiff(path: string): void {
    const normalizedPath = path.replace(/\\/g, '/');
    this.diffFiles = this.diffFiles.map(diff =>
      diff.path === normalizedPath ? { ...diff, status: 'undecided' } : diff
    );
  }

  public async acceptFileDiffWithoutOpening(path: string, content: string): Promise<void> {
    const data = { path, content };
    return this.acceptFile(data);
  }

  public async undoFileDiffWithoutOpening(path: string, content: string, originalContent?: string): Promise<void> {
    const data = { path, content, originalContent };
    return this.undoAcceptFile(data);
  }

  public async rejectFileDiffWithoutOpening(path: string, content: string): Promise<void> { 
    const data = { path, content };
    return this.rejectFile(data);
  }

  public getDiffFiles(): DiffFile[] {
    return this.diffFiles;
  }

  // 添加文件到diff列表
  public addDiff(file: DiffFile): void {
    const existingFileIndex = this.diffFiles.findIndex(f => f.path === file.path);
    if (existingFileIndex !== -1) {
      this.diffFiles[existingFileIndex] = {
        ...file,
        status: 'undecided',
        originalContent: file.originalContent,
      };
    } else {
      this.diffFiles.push({
        ...file,
        status: 'undecided',
        originalContent: file.originalContent,
      });
    }
  }

  public clearDiffs(): void {
    this.diffFiles = [];
  }

  // 切换文件状态
  public toggleFileStatus(file: DiffFile): void {
    const newStatus = file.status === 'accepted' ? 'undecided' : 'accepted';

    if (newStatus === 'accepted') {
      this.acceptFileDiffWithoutOpening(file.path, file.content);
    } else {
      this.undoFileDiffWithoutOpening(file.path, file.originalContent);
    }

    this.diffFiles = this.diffFiles.map(f =>
      f.path === file.path ? { ...f, status: newStatus } : f
    );
  }

  public toggleAllFiles(): void {
    const allAccepted = this.diffFiles.every(file => file.status === 'accepted');

    if (allAccepted) {
      this.diffFiles.forEach(file => {
        this.undoFileDiffWithoutOpening(file.path, file.originalContent);
      });
      this.diffFiles = this.diffFiles.map(file => ({ ...file, status: 'undecided' }));
    } else {
      this.diffFiles.forEach(file => {
        this.acceptFileDiffWithoutOpening(file.path, file.content);
      });
      this.diffFiles = this.diffFiles.map(file => ({ ...file, status: 'accepted' }));
    }
  }

  public updateFileStatus(filePath: string, newStatus: DiffStatus): void {
    filePath = filePath.replace(/\/+/g, '/').replace(/\\/g, '/');
    this.diffFiles = this.diffFiles.map(file =>
      file.path === filePath ? { ...file, status: newStatus } : file
    );
  }

  public getAllItemsStatus(): 'none' | 'all_accepted' | 'partial_accepted' {
    if (this.diffFiles.length === 0) {
      return 'none';
    }
    const acceptedCount = this.diffFiles.filter(file => file.status === 'accepted').length;
    if (acceptedCount === 0) {
      return 'none';
    }
    if (acceptedCount === this.diffFiles.length) {
      return 'all_accepted';
    }
    return 'partial_accepted';
  }



 /*****inline diff******/
  public acceptAllChangesWithInlineDiff(path: string) { 
    const uri = vscode.Uri.file(path);
    vscode.commands.executeCommand('codefree-composer.AcceptAllChanges', uri);
  }

  public rejectAllChangesWithInlineDiff(path: string) { 
    const uri = vscode.Uri.file(path);
    vscode.commands.executeCommand('codefree-composer.RejectAllChanges', uri);
  }

  public undoAllChangesWithInlineDiff(path: string, content: string) { 
    const data = {path, content};
    InlineDiffViewManager.getInstance().openDiffView(data);
  }
}
