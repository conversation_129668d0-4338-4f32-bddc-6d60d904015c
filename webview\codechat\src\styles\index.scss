// 全局 CSS 变量
@import './variables.css';
// 注册多主题
@import './theme/register.scss';
// mixin
@import './mixins.scss';
@import './vscode-var.scss';
@import './vscode-var-dark.scss';
@import './variables.css';

* {
  box-sizing: border-box;
  font-family: PingFang SC, Segoe WPC, Segoe UI, Microsoft YaHei, sans-serif;
}

html {
  height: 100%;
}

body {
  height: 100%;
  padding: 0;
}

#app {
  height: 100%;
  font-size: 14px;
}

.active-link {
  color: var(--vscode-textLink-activeForeground);
  text-decoration: solid underline var(--vscode-textLink-activeForeground);
  text-decoration-thickness: 2px;
  text-underline-offset: 10px;
}

svg:focus {
  outline: none;
}

code {
  border: 1px solid var(--vscode-sideBarSectionHeader-border);
}

.vscode-high-contrast-dark,
.vscode-dark {
  .header-right-icon {
    display: none;
  }
}

.vscode-high-contrast-light,
.vscode-light {
  .header-right-icon {
    display: block;
  }
}

.vscode-high-contrast-light,
.vscode-light {
  .bk_header-right-icon {
    display: none;
  }
}

.vscode-high-contrast-dark,
.vscode-dark {
  .bk_header-right-icon {
    display: block;
  }
}

.ai-bubble {
  background-color: var(--cf-ai-bubble-background);
}

code {
  color: var(--vscode-textPreformat-foreground);
  background-color: var(--vscode-textPreformat-background);
  padding: 1px 3px;
  border-radius: 4px;
}

// 含有代码的对话气泡
.code-bubble {
  padding: 20px;
  position: relative;

  .message-user-name {
    font-weight: bold;
  }

  .message {
    margin-top: 10px;

    &-header__copy {
      cursor: pointer;
      width: 12px;
      width: 12px;
    }

    &-time {
      font-size: 14px;
      margin-left: 8px;
      opacity: 0.6;
      color: var(--vscode-inputOption-activeForeground);
    }

    &-text {
      line-height: normal;
      color: var(--vscode-inputOption-activeForeground);
      p:first-child,
      pre:first-child {
        margin-top: 0;
      }

      p:last-child {
        margin-bottom: 0;
      }

      pre>code {
        display: contents;
      }

      a {
        color: var(--vscode-textLink-foreground);
      }
    }

    .right-btn {
      float: right;
    }
  }

  .logo {
    width: 24px;
    height: 24px;
    margin-right: 8px;

    &.user-logo {
      background: #D6E5FE;
      border-radius: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .pre-code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px 4px 0px 0px;
    height: 32px;
    border: 1px solid var(--vscode-code-border);
    background: var(--vscode-input-background);
    padding: 0 15px;
  }

  .code-block-header__icons {
    display: flex;
  }

  .code-block-header__icon {
    display: flex;
    cursor: pointer;

    &:not(:first-child) {
      margin-left: 5px;
    }

    svg {
      color: var(--vscode-editor-foreground);
      width: 14px;
      height: 14px;
    }

    border-radius: 3px;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #cccccc4a;
    }
  }

  .code-block-header__lang {
    color: var(--vscode-editor-foreground);
  }

  .pre-code {
    display: flex;
    border-bottom: 1px solid var(--vscode-code-border);
    border-left: 1px solid var(--vscode-code-border);
    border-right: 1px solid var(--vscode-code-border);
    border-radius: 0 0 4px 4px;
    background: var(--vscode-input-background);
    font-family: inherit;

    .code-decimal-index {
      min-width: 30px;
      padding-top: 1em;
      padding-bottom: 1em;
      border-right: 1px solid var(--vscode-code-border);
      background: var(--vscode-input-background);
      line-height: 20px;
      border-bottom-left-radius: 4px;
      font-family: inherit;

      .index-item {
        font-family: inherit;

        text-align: center;
        color: var(--vscode-editor-foreground);
      }
    }
  }

  code,
  pre {
    white-space: normal;
    border: none;
    word-break: break-all;
  }

  pre>code {
    display: contents;
  }

  .hljs.code-block-body {
    background: var(--vscode-input-background);
    white-space: pre;
    padding-top: 1em;
    padding-bottom: 1em;
    width: 100%;
    line-height: 20px;
    white-space: pre;
    flex: 1;
    font-family: inherit;

    span {
      font-family: inherit;
    }
  }
  .hljs.diff-code-block-body {
    background: var(--vscode-input-background);
    padding-top: 1em;
    padding-bottom: 1em;
    width: 100%;
    line-height: 20px;
    display: table !important;
    font-family: inherit;

    span {
      font-family: inherit;
    }
  }
}

// css-tooltip
[tooltip] {
  position: relative;
  display: inline-block;
}

[tooltip]::after {
  content: attr(tooltip);
  position: absolute;
  background: #373739;
  text-align: center;
  color: #fff;
  border-radius: 5px;
  padding: 4px;
  pointer-events: none;
  z-index: 999;
  opacity: 0;
  left: 50%;
  top: -5px;
  width: attr(tooltipwidth);
  width: 40px;
  transform: translateX(-50%) translateY(-100%);
  transition: all 0.3;
}

.tooltip-width-70::after {
  width: 70px;
}

[tooltip]:hover::after {
  opacity: 1;
}

/* 右侧 */
[tooltip][position='right']::after {
  top: 50%;
  left: 100%;
  margin-left: 5px;
  transform: translateY(-50%);
}

/* 左侧 */
[tooltip][position='left']::after {
  top: 50%;
  left: 0;
  margin-left: -5px;
  transform: translateY(-50%) translateX(-100%);
}

/* 底侧 */
[tooltip][position='bottom']::after {
  top: 100%;
  left: 50%;
  margin-top: 5px;
  transform: translatex(-50%);
}

/* 公共样式 */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-btn {
  padding: 3.5px 12px;
  font-size: 12px;
  border-radius: 3px;
  border: 1px solid var(--vscode-input-placeholderForeground);
  color: var(--vscode-input-placeholderForeground);
  cursor: pointer;
  line-height: 14px;

  &:hover {
    border: 1px solid var(--vscode-textLink-foreground);
    color: var(--vscode-textLink-foreground);
  }
}

.normal-icon-btn {
  padding: 5px;
  font-size: 14px;
  border-radius: 3px;
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--vscode-input-placeholderForeground);
  cursor: pointer;
}

.icon-btn {
  padding: 5px;
  font-size: 12px;
  border-radius: 3px;
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--vscode-input-placeholderForeground);
  color: var(--vscode-input-placeholderForeground);
  cursor: pointer;

  &:hover {
    border: 1px solid var(--vscode-textLink-foreground);
    color: var(--vscode-textLink-foreground);
  }
}

.active-btn {
  // border: 1px solid var(--vscode-textLink-foreground);
  color: var(--vscode-textLink-foreground);
}

.link-btn {
  cursor: pointer;
  color: var(--vscode-textLink-foreground);
}

.hightlight-text {
  color: var(--vscode-textLink-foreground) !important;
}

.composer-input-mention, .input-mention {
  background: var(--color-primary-light-3);
  margin: 0 4px;
  display: inline-flex;
  align-items: center;
  height: 24px;
  &.input-mention-file {
    // cursor: pointer;
  }
}
.input-mention__project {
  &::before{
    content: '@当前工程';
  }
  &:hover {
    &::before{
      content: '@codebase';
    }
  }
}
.composer-diff-line {
  height: 20px;
  &.composer-diff-line-addition {
    background: #99f45842;
  }
  &.composer-diff-line-deletion {
    background: #e06c7561;
  }
  // &::after {
  //   content: "";
  //   display: table-cell;
  // }
  display: table-row;
  white-space: pre;
}
.mermaid {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.home-link {
  cursor: pointer;
  &#whatisnew {
    position: relative;
    span {
      position: absolute;
      top: -30%;
      left: 100%;
      border-radius: 100px;
      padding: 1px 5px;
      background: red;
      color: #fff;
      line-height: 16px;

    }
  }
}

.user-text-content {
  word-wrap: break-word;

  p {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.longlong-filename {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.arco-collapse-item-header {
  &:focus-visible {
      outline: none !important;
  }
}

.longlong-filename {
  svg {
    opacity: 0.6;
  }
}